import { APIResource } from "../resource.js";
import * as Core from "../core.js";
import { DefaultPagination, type DefaultPaginationParams } from "../pagination.js";
export declare class Executions extends APIResource {
    /**
     * Returns a list of executions in your project.
     */
    list(query?: ExecutionListParams, options?: Core.RequestOptions): Core.PagePromise<ExecutionsDefaultPagination, Execution>;
    list(options?: Core.RequestOptions): Core.PagePromise<ExecutionsDefaultPagination, Execution>;
    /**
     * Retrieves an execution.
     */
    get(id: string, options?: Core.RequestOptions): Core.APIPromise<Execution>;
}
export declare class ExecutionsDefaultPagination extends DefaultPagination<Execution> {
}
export interface Execution {
    id: string;
    duration: number;
    exit_code: number;
    language: 'python' | 'javascript' | 'typescript' | 'ruby' | 'php';
    started_at: string;
    details?: Execution.ToolExecutionDetails | Execution.FunctionExecutionDetails | Execution.ScriptExecutionDetails;
}
export declare namespace Execution {
    interface ToolExecutionDetails {
        request: ToolExecutionDetails.Request;
        response: ToolExecutionDetails.Response;
        tool_id: string;
        type: 'tool';
    }
    namespace ToolExecutionDetails {
        interface Request {
            /**
             * Set of key-value pairs to add to the tool's execution environment.
             */
            env?: Array<Request.Env>;
            /**
             * Configuration for HTTP requests and authentication.
             */
            http?: Request.HTTP;
            /**
             * The input to the tool. This must be a valid JSON-serializable object. It will be
             * validated against the tool's input schema.
             */
            input?: unknown;
            /**
             * The Tool revision ID to execute. This optional parmeter is used to pin
             * executions to specific versions of the Tool. If not provided, the latest
             * (current) version of the Tool will be executed.
             */
            revision_id?: string;
        }
        namespace Request {
            /**
             * Set of key-value pairs to add to the tool's execution environment.
             */
            interface Env {
                name: string;
                secret_id?: string;
                value?: string;
            }
            /**
             * Configuration for HTTP requests and authentication.
             */
            interface HTTP {
                /**
                 * List of allowed HTTP hosts and associated authentication.
                 */
                allow?: Array<HTTP.Allow>;
            }
            namespace HTTP {
                /**
                 * List of allowed HTTP hosts and associated authentication.
                 */
                interface Allow {
                    /**
                     * Authentication configuration for outbound requests to this host.
                     */
                    auth?: Allow.Auth;
                    /**
                     * The hostname to allow.
                     */
                    host?: string;
                }
                namespace Allow {
                    /**
                     * Authentication configuration for outbound requests to this host.
                     */
                    interface Auth {
                        basic?: Auth.Basic;
                        /**
                         * Configuration to add an 'Authorization' header using the 'Bearer' scheme.
                         */
                        bearer?: Auth.Bearer;
                        query?: Auth.Query;
                    }
                    namespace Auth {
                        interface Basic {
                            password?: string;
                            secret_id?: string;
                            user_id?: string;
                        }
                        /**
                         * Configuration to add an 'Authorization' header using the 'Bearer' scheme.
                         */
                        interface Bearer {
                            /**
                             * The token to set, e.g. 'Authorization: Bearer <token>'.
                             */
                            token?: string;
                            secret_id?: string;
                        }
                        interface Query {
                            key?: string;
                            secret_id?: string;
                            value?: string;
                        }
                    }
                }
            }
        }
        interface Response {
            /**
             * The execution details of the function.
             */
            execution: Response.Execution;
            /**
             * The returned value of the Tool's execute function.
             */
            output: unknown;
            /**
             * The status of the output. "valid" means your Tool executed successfully and
             * returned a valid JSON-serializable object, or void. "json_serialization_error"
             * means your Tool executed successfully, but returned a nonserializable object.
             * "error" means your Tool failed to execute.
             */
            output_status: 'error' | 'json_serialization_error' | 'valid';
        }
        namespace Response {
            /**
             * The execution details of the function.
             */
            interface Execution {
                /**
                 * The ID of the execution.
                 */
                id: string;
                /**
                 * The execution time of the function in milliseconds.
                 */
                duration: number;
                /**
                 * The exit code returned by the function. Will often be '0' on success and
                 * non-zero on failure.
                 */
                exit_code: number;
                /**
                 * The contents of 'stderr' after executing the function.
                 */
                stderr: string;
                /**
                 * The contents of 'stdout' after executing the function.
                 */
                stdout: string;
            }
        }
    }
    interface FunctionExecutionDetails {
        request: FunctionExecutionDetails.Request;
        response: FunctionExecutionDetails.Response;
        type: 'function';
    }
    namespace FunctionExecutionDetails {
        interface Request {
            /**
             * The function to execute. Your code must define a function named "execute" that
             * takes in a single argument and returns a JSON-serializable value.
             */
            code: string;
            /**
             * The interpreter to use when executing code.
             */
            language: 'python' | 'javascript' | 'typescript';
            /**
             * Set of key-value pairs to add to the function's execution environment.
             */
            env?: {
                [key: string]: string;
            };
            /**
             * List of input files.
             */
            files?: Array<Request.File>;
            /**
             * Configuration for HTTP requests and authentication.
             */
            http?: Request.HTTP;
            /**
             * The input to the function. This must be a valid JSON-serializable object. If you
             * do not pass an input, your function will be called with None (Python) or null
             * (JavaScript/TypeScript) as the argument.
             */
            input?: unknown;
            /**
             * Configuration for execution environment limits.
             */
            limits?: Request.Limits;
            /**
             * The ID of the runtime revision to use when executing code.
             */
            runtime_revision_id?: string;
        }
        namespace Request {
            interface File {
                /**
                 * The contents of the file.
                 */
                contents?: string;
                /**
                 * The relative path of the file.
                 */
                path?: string;
            }
            /**
             * Configuration for HTTP requests and authentication.
             */
            interface HTTP {
                /**
                 * List of allowed HTTP hosts and associated authentication.
                 */
                allow?: Array<HTTP.Allow>;
            }
            namespace HTTP {
                /**
                 * List of allowed HTTP hosts and associated authentication.
                 */
                interface Allow {
                    /**
                     * Authentication configuration for outbound requests to this host.
                     */
                    auth?: Allow.Auth;
                    /**
                     * The hostname to allow.
                     */
                    host?: string;
                }
                namespace Allow {
                    /**
                     * Authentication configuration for outbound requests to this host.
                     */
                    interface Auth {
                        basic?: Auth.Basic;
                        /**
                         * Configuration to add an 'Authorization' header using the 'Bearer' scheme.
                         */
                        bearer?: Auth.Bearer;
                        header?: Auth.Header;
                        query?: Auth.Query;
                    }
                    namespace Auth {
                        interface Basic {
                            password?: string;
                            user_id?: string;
                        }
                        /**
                         * Configuration to add an 'Authorization' header using the 'Bearer' scheme.
                         */
                        interface Bearer {
                            /**
                             * The token to set, e.g. 'Authorization: Bearer <token>'.
                             */
                            token?: string;
                        }
                        interface Header {
                            name?: string;
                            value?: string;
                        }
                        interface Query {
                            key?: string;
                            value?: string;
                        }
                    }
                }
            }
            /**
             * Configuration for execution environment limits.
             */
            interface Limits {
                /**
                 * The maximum time allowed for execution (in seconds). Default is 30.
                 */
                execution_timeout?: number;
                /**
                 * The maximum memory allowed for execution (in MiB). Default is 128.
                 */
                memory_size?: number;
            }
        }
        interface Response {
            /**
             * The execution details of the function.
             */
            execution: Response.Execution;
            /**
             * The output of the function.
             */
            output: unknown;
            /**
             * The status of the output. "valid" means your function executed successfully and
             * returned a valid JSON-serializable object, or void. "json_serialization_error"
             * means your function executed successfully, but returned a nonserializable
             * object. "error" means your function failed to execute.
             */
            output_status: 'error' | 'json_serialization_error' | 'valid';
        }
        namespace Response {
            /**
             * The execution details of the function.
             */
            interface Execution {
                /**
                 * The ID of the execution.
                 */
                id: string;
                /**
                 * The execution time of the function in milliseconds.
                 */
                duration: number;
                /**
                 * The exit code returned by the function. Will often be '0' on success and
                 * non-zero on failure.
                 */
                exit_code: number;
                /**
                 * The contents of 'stderr' after executing the function.
                 */
                stderr: string;
                /**
                 * The contents of 'stdout' after executing the function.
                 */
                stdout: string;
            }
        }
    }
    interface ScriptExecutionDetails {
        request: ScriptExecutionDetails.Request;
        response: ScriptExecutionDetails.Response;
        type: 'script';
    }
    namespace ScriptExecutionDetails {
        interface Request {
            /**
             * The code to execute.
             */
            code: string;
            /**
             * The interpreter to use when executing code.
             */
            language: 'python' | 'javascript' | 'typescript' | 'ruby' | 'php';
            /**
             * List of command line arguments to pass to the script.
             */
            args?: Array<string>;
            /**
             * Set of key-value pairs to add to the script's execution environment.
             */
            env?: {
                [key: string]: string;
            };
            /**
             * List of input files.
             */
            files?: Array<Request.File>;
            /**
             * Configuration for HTTP requests and authentication.
             */
            http?: Request.HTTP;
            /**
             * Configuration for execution environment limits.
             */
            limits?: Request.Limits;
            /**
             * The ID of the runtime revision to use when executing code.
             */
            runtime_revision_id?: string;
            /**
             * Input made available to the script via 'stdin'.
             */
            stdin?: string;
        }
        namespace Request {
            interface File {
                /**
                 * The contents of the file.
                 */
                contents?: string;
                /**
                 * The relative path of the file.
                 */
                path?: string;
            }
            /**
             * Configuration for HTTP requests and authentication.
             */
            interface HTTP {
                /**
                 * List of allowed HTTP hosts and associated authentication.
                 */
                allow?: Array<HTTP.Allow>;
            }
            namespace HTTP {
                /**
                 * List of allowed HTTP hosts and associated authentication.
                 */
                interface Allow {
                    /**
                     * Authentication configuration for outbound requests to this host.
                     */
                    auth?: Allow.Auth;
                    /**
                     * The hostname to allow.
                     */
                    host?: string;
                }
                namespace Allow {
                    /**
                     * Authentication configuration for outbound requests to this host.
                     */
                    interface Auth {
                        basic?: Auth.Basic;
                        /**
                         * Configuration to add an 'Authorization' header using the 'Bearer' scheme.
                         */
                        bearer?: Auth.Bearer;
                        header?: Auth.Header;
                        query?: Auth.Query;
                    }
                    namespace Auth {
                        interface Basic {
                            password?: string;
                            user_id?: string;
                        }
                        /**
                         * Configuration to add an 'Authorization' header using the 'Bearer' scheme.
                         */
                        interface Bearer {
                            /**
                             * The token to set, e.g. 'Authorization: Bearer <token>'.
                             */
                            token?: string;
                        }
                        interface Header {
                            name?: string;
                            value?: string;
                        }
                        interface Query {
                            key?: string;
                            value?: string;
                        }
                    }
                }
            }
            /**
             * Configuration for execution environment limits.
             */
            interface Limits {
                /**
                 * The maximum time allowed for execution (in seconds). Default is 30.
                 */
                execution_timeout?: number;
                /**
                 * The maximum memory allowed for execution (in MiB). Default is 128.
                 */
                memory_size?: number;
            }
        }
        interface Response {
            /**
             * The ID of the execution.
             */
            id: string;
            /**
             * The execution time of the script in milliseconds.
             */
            duration: number;
            /**
             * The exit code returned by the script. Will often be '0' on success and non-zero
             * on failure.
             */
            exit_code: number;
            /**
             * The contents of 'stderr' after executing the script.
             */
            stderr: string;
            /**
             * The contents of 'stdout' after executing the script.
             */
            stdout: string;
        }
    }
}
export interface ExecutionListParams extends DefaultPaginationParams {
    /**
     * If true, only show executions where the exit code is not 0, indicating an
     * execution error. Defaults to false.
     */
    only_non_zero_exit_codes?: boolean;
}
export declare namespace Executions {
    export { type Execution as Execution, ExecutionsDefaultPagination as ExecutionsDefaultPagination, type ExecutionListParams as ExecutionListParams, };
}
//# sourceMappingURL=executions.d.ts.map