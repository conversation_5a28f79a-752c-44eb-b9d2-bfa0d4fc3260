{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 检索器：调优 - 上下文压缩\n", "****\n", "- <PERSON><PERSON>hainExtractor\n", "- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "- 多个压缩器组合管道"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_deepseek import ChatDeepSeek\n", "import os\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def pretty_print_docs(docs):\n", "    print(\n", "        f\"\\n{'-' * 100}\\n\".join(\n", "            [f\"Document {i+1}:\\n\\n\" + d.page_content for i, d in enumerate(docs)]\n", "        )\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 上下文压缩\n", "****\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["未优化前使用向量库自带能力"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document 1:\n", "\n", "3.1 创建知识库\n", "将需要 AI 分析处理的文档上传至知识库中。为确保 DeepSeek 模型能够准确理解文档内容，建议使用\"父子分段\"模式进行文本处理 - 这种模式能够更好地保留文档的层级结构和上下文关系。如需了解详细的配置步骤，请参考：创建知识库。\n", "\n", "\n", "3.2 将知识库集成至 AI 应用\n", "在 AI 应用的\"上下文\"内添加知识库，在对话框内输入相关问题。LLM 将首先从知识库内获取与问题相关上下文，在此基础上进行总结并给出更高质量的回答。\n", "\n", "\n", "4. 分享 AI 应用\n", "构建完成后，你可以将该 AI 应用分享给他人使用或集成至其它网站内。\n", "\n", "\n", "阅读更多\n", "除了构建简单的 AI 应用外，你还可以创建 Chatflow / Workflow 搭建更多复杂功能的应用（例如具备文件识别、图像识别、语音识别等能力）。详细说明请参考以下文档：\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "Deepseek R1 是⽀持复杂推理、多模态处理、技术⽂档⽣成的⾼性能通⽤⼤语⾔模型。本⼿册\n", "为技术团队提供完整的本地部署指南，涵盖硬件配置、国产芯⽚适配、量化⽅案、云端替代⽅\n", "案及完整671B MoE模型的Ollama部署⽅法。模型 参数规\n", "模\n", "计算精\n", "度\n", "最低显存需\n", "求 最低算⼒需求\n", "DeepSeek-R1 (671B) 671B FP8 ≥890GB\n", "2*XE9680（16*H20\n", "GPU）\n", "DeepSeek-R1-<PERSON><PERSON><PERSON>-\n", "70B\n", "70B BF16 ≥180GB 4*L20 或 2*H20 GPU\n", "三、国产芯⽚与硬件适配⽅案\n", "1. 国内⽣态合作伙伴动态\n", "企业 适配内容 性能对标（vs\n", "NVIDIA）\n", "华为昇\n", "腾\n", "昇腾910B原⽣⽀持R1全系列，提供端到端推理优化\n", "⽅案 等效A100（FP16）\n", "沐曦\n", "GPU\n", "MXN系列⽀持70B模型BF16推理，显存利⽤率提升\n", "30% 等效RTX 3090\n", "海光\n", "DCU 适配V3/R1模型，性能对标NVIDIA A100 等效A100（BF16）\n", "2. 国产硬件推荐配置\n", "模型参数 推荐⽅案 适⽤场景\n", "1.5B 太初T100加速卡 个⼈开发者原型验证\n", "14B 昆仑芯K200集群 企业级复杂任务推理\n", "32B 壁彻算⼒平台+昇腾910B集群 科研计算与多模态处理\n", "12月26日，Deepseek发布了全新系列模型DeepSeek-v3，一夜之间霸榜开源模型，并在性能上和世界顶尖的闭源模型GPT-4o以及 Claude-3.5-Sonnet相提并论。\n", "\n", "该模型为MOE架构，大大降低了训练成本，据说训练成本仅600万美元，成本降低10倍，资源运用效率极高。有AI投资机构负责人直言，DeepSeek发布的53页的技术论文是黄金。\n", "\n", "那就先让我们看看论文是怎么说的吧，老规矩，先上资源地址：\n", "\n", "Github: GitHub - deepseek-ai/DeepSeek-V3\n", "\n", "模型地址：https://huggingface.co/deepseek-ai\n", "\n", "论文地址：https://github.com/deepseek-ai/DeepSeek-V3/blob/main/DeepSeek_V3.pdf\n", "\n", "以下为技术解读：\n", "----------------------------------------------------------------------------------------------------\n", "Document 3:\n", "\n", "进一步改进模型架构，以提高训练和推理效率，并尝试突破 Transformer 架构的限制。\n", "持续迭代训练数据的质量和数量，并探索其他训练信号来源，以推动数据扩展到更广泛的维度。\n", "持续探索和迭代模型的深度思考能力，以增强其智能和问题解决能力，并扩展其推理长度和深度。\n", "探索更全面和多维度的模型评估方法，以防止在研究过程中优化固定的一组基准测试，从而产生对模型能力的误导印象并影响我们的基础评估。\n", "DeepSeek-V3 的发布标志着开源大型语言模型领域的一个重大里程碑，并为未来的研究和应用开辟了新的可能性。\n", "\n", "简单测试\n", "DeepSeek-V3开源模型，我肯定是没有资源部署了，所以只能通过它的服务网站进行测试了。\n", "\n", "地址：DeepSeek\n", "\n", "\n", "算一下星舰从地球到火星的飞行时间：\n", "\n", "\n", "让它分析一下自己的技术文档：\n", "\n", "\n", "最后让它比较了一下自己与GPT-4o-0513\n", "\n", "\n", "... 略...\n", "\n", "\n", "——完——\n", "\n", "@北方的郎 · 专注模型与代码\n", "\n", "概述\n", "前置准备\n", "1. 申请 DeepSeek API\n", "2. 注册 Dify\n", "集成步骤\n", "1. 将 DeepSeek 接入至 Dify\n", "2. 搭建 DeepSeek AI 应用\n", "3. 为 AI 应用启用文本分析能力\n", "4. 分享 AI 应用\n", "阅读更多\n", "Edit on GitHub\n", "\n", "阅读更多\n", "应用案例\n", "DeepSeek 与 Dify 集成指南：打造具备多轮思考的 AI 应用\n", "概述\n", "DeepSeek 作为具备多轮推理能力的开源大语言模型，以高性能、低成本、易部署的特性成为智能应用开发的理想基座。通过其 API 服务，开发者可快速调用 DeepSeek 的复杂逻辑推理与内容生成能力。在传统开发模式下，构建生产级 AI 应用往往需要独立完成模型适配、接口开发、交互设计等环节。\n", "\n", "Dify 作为同样开源的生成式 AI 应用开发平台，能够帮助开发者基于 DeepSeek 大模型快速开发出更加智能的 AI 应用，你可以在 Dify 平台内获得以下开发体验：\n", "\n", "可视化构建 - 通过可视化编排界面，3 分钟搭建基于 DeepSeek R1 的 AI 应用\n", "\n", "知识库增强 - 关联内部文档，开启 RAG 能力并构建精准问答系统\n", "\n", "工作流扩展 - 提供多种第三方工具插件、可视化拖拽式编排应用功能节点，实现复杂业务逻辑\n", "----------------------------------------------------------------------------------------------------\n", "Document 4:\n", "\n", "数据洞察力 - 内置总对话数、应用使用用户数等数据监控模块，支持与更加专业的监控平台集成 ...\n", "\n", "本文将详解 DeepSeek API 与 Dify 的集成步骤，助你快速实现两大核心场景：\n", "\n", "智能对话机器人开发 - 直接调用 DeepSeek R1 的思维链推理能力\n", "\n", "知识增强型应用构建 - 通过私有知识库实现精准信息检索与生成\n", "\n", "针对金融、法律等高合规需求场景，Dify 提供 本地私有化部署 DeepSeek + Dify，支持 DeepSeek 模型与 Dify 平台同步部署至内网\n", "\n", "通过 Dify × DeepSeek 的技术组合，开发者可跳过底层架构搭建，跃迁至场景化 AI 能力落地阶段，让大模型技术快速转化为业务生产力。\n", "\n", "前置准备\n", "1. 申请 DeepSeek API\n", "访问 DeepSeek API 开放平台，按照页面提示进行申请 API Key。\n", "\n", "若提示链接无法访问，你也可以考虑在本地部署 DeepSeek 模型。详细说明请参考 本地部署指南\n", "\n", "2. 注册 Dify\n", "Dify 是一个能够帮助你快速搭建生成式 AI 应用的平台，通过接入 DeepSeek API，你可以快速搭建出一个能够易于使用的 DeepSeek AI 应用。\n", "\n", "集成步骤\n", "1. 将 DeepSeek 接入至 Dify\n", "访问 Dify 平台，点击右上角头像 → 设置 → 模型供应商，找到 DeepSeek，将上文获取的 API Key 粘贴至其中。点击保存，校验通过后将出现成功提示。\n", "\n", "\n", "2. 搭建 DeepSeek AI 应用\n", "轻点 Dify 平台首页左侧的\"创建空白应用\"，选择\"聊天助手\"类型应用并进行简单的命名。\n", "\n", "\n", "选择 deepseek-reasoner 模型\n", "\n", "deepseek-reasoner 模型又称为 deepseek-r1 模型。\n", "\n", "\n", "配置完成后即可在聊天框中进行互动。\n", "\n", "\n", "3. 为 AI 应用启用文本分析能力\n", "RAG（检索增强生成）是一种先进的信息处理技术，它通过检索相关知识，向 LLM 提供必要的上下文信息，融入 LLM 的内容生成过程，提升回答的准确性和专业度。当你上传内部文档或专业资料后，AI 能够基于这些知识提供更有针对性的解答。\n"]}], "source": ["from langchain_community.document_loaders import TextLoader\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_text_splitters import CharacterTextSplitter\n", "\n", "documents = TextLoader(\"test.txt\").load()\n", "text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)\n", "texts = text_splitter.split_documents(documents)\n", "retriever = FAISS.from_documents(texts, OpenAIEmbeddings()).as_retriever()\n", "\n", "docs = retriever.invoke(\"如何进行模型部署\")\n", "pretty_print_docs(docs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用LL<PERSON>hainExtractor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["基础检索器ContextualCompressionRetriever以及LLMChainExtractor，它将迭代最初返回的文档，并从每个文档中仅提取与查询相关的内容"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'llm' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mlangchain\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mretrievers\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m ContextualCompressionRetriever\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mlangchain\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mretrievers\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mdocument_compressors\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m LLMChainExtractor\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m compressor = LLMChainExtractor.from_llm(\u001b[43mllm\u001b[49m)\n\u001b[32m      5\u001b[39m compression_retriever = ContextualCompressionRetriever(\n\u001b[32m      6\u001b[39m     base_compressor=compressor, base_retriever=retriever\n\u001b[32m      7\u001b[39m )\n\u001b[32m      9\u001b[39m compressed_docs = compression_retriever.invoke(\n\u001b[32m     10\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33m如何进行部署\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m     11\u001b[39m )\n", "\u001b[31mNameError\u001b[39m: name 'llm' is not defined"]}], "source": ["from langchain.retrievers import ContextualCompressionRetriever\n", "from langchain.retrievers.document_compressors import LLMChainExtractor\n", "\n", "compressor = LLMChainExtractor.from_llm(llm)\n", "compression_retriever = ContextualCompressionRetriever(\n", "    base_compressor=compressor, base_retriever=retriever\n", ")\n", "\n", "compressed_docs = compression_retriever.invoke(\n", "    \"如何进行部署\"\n", ")\n", "pretty_print_docs(compressed_docs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["LLMChainFilter：使用 LLM 链来决定过滤掉哪些最初检索到的文档以及返回哪些文档，而无需操作文档内容"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document 1:\n", "\n", "3.1 创建知识库\n", "将需要 AI 分析处理的文档上传至知识库中。为确保 DeepSeek 模型能够准确理解文档内容，建议使用\"父子分段\"模式进行文本处理 - 这种模式能够更好地保留文档的层级结构和上下文关系。如需了解详细的配置步骤，请参考：创建知识库。\n", "\n", "\n", "3.2 将知识库集成至 AI 应用\n", "在 AI 应用的\"上下文\"内添加知识库，在对话框内输入相关问题。LLM 将首先从知识库内获取与问题相关上下文，在此基础上进行总结并给出更高质量的回答。\n", "\n", "\n", "4. 分享 AI 应用\n", "构建完成后，你可以将该 AI 应用分享给他人使用或集成至其它网站内。\n", "\n", "\n", "阅读更多\n", "除了构建简单的 AI 应用外，你还可以创建 Chatflow / Workflow 搭建更多复杂功能的应用（例如具备文件识别、图像识别、语音识别等能力）。详细说明请参考以下文档：\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "Deepseek R1 是⽀持复杂推理、多模态处理、技术⽂档⽣成的⾼性能通⽤⼤语⾔模型。本⼿册\n", "为技术团队提供完整的本地部署指南，涵盖硬件配置、国产芯⽚适配、量化⽅案、云端替代⽅\n", "案及完整671B MoE模型的Ollama部署⽅法。模型 参数规\n", "模\n", "计算精\n", "度\n", "最低显存需\n", "求 最低算⼒需求\n", "DeepSeek-R1 (671B) 671B FP8 ≥890GB\n", "2*XE9680（16*H20\n", "GPU）\n", "DeepSeek-R1-<PERSON><PERSON><PERSON>-\n", "70B\n", "70B BF16 ≥180GB 4*L20 或 2*H20 GPU\n", "三、国产芯⽚与硬件适配⽅案\n", "1. 国内⽣态合作伙伴动态\n", "企业 适配内容 性能对标（vs\n", "NVIDIA）\n", "华为昇\n", "腾\n", "昇腾910B原⽣⽀持R1全系列，提供端到端推理优化\n", "⽅案 等效A100（FP16）\n", "沐曦\n", "GPU\n", "MXN系列⽀持70B模型BF16推理，显存利⽤率提升\n", "30% 等效RTX 3090\n", "海光\n", "DCU 适配V3/R1模型，性能对标NVIDIA A100 等效A100（BF16）\n", "2. 国产硬件推荐配置\n", "模型参数 推荐⽅案 适⽤场景\n", "1.5B 太初T100加速卡 个⼈开发者原型验证\n", "14B 昆仑芯K200集群 企业级复杂任务推理\n", "32B 壁彻算⼒平台+昇腾910B集群 科研计算与多模态处理\n", "12月26日，Deepseek发布了全新系列模型DeepSeek-v3，一夜之间霸榜开源模型，并在性能上和世界顶尖的闭源模型GPT-4o以及 Claude-3.5-Sonnet相提并论。\n", "\n", "该模型为MOE架构，大大降低了训练成本，据说训练成本仅600万美元，成本降低10倍，资源运用效率极高。有AI投资机构负责人直言，DeepSeek发布的53页的技术论文是黄金。\n", "\n", "那就先让我们看看论文是怎么说的吧，老规矩，先上资源地址：\n", "\n", "Github: GitHub - deepseek-ai/DeepSeek-V3\n", "\n", "模型地址：https://huggingface.co/deepseek-ai\n", "\n", "论文地址：https://github.com/deepseek-ai/DeepSeek-V3/blob/main/DeepSeek_V3.pdf\n", "\n", "以下为技术解读：\n"]}], "source": ["from langchain.retrievers.document_compressors import LLMChainFilter\n", "\n", "_filter = LLMChainFilter.from_llm(llm)\n", "compression_retriever = ContextualCompressionRetriever(\n", "    base_compressor=_filter, base_retriever=retriever,\n", ")\n", "\n", "compressed_docs = compression_retriever.invoke(\n", "    \"如何进行部署\"\n", ")\n", "pretty_print_docs(compressed_docs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 多个压缩器组合管道\n", "***\n", "- 使用DocumentCompressorPipeline轻松地按顺序组合多个压缩器\n", "- 将文档拆解成更小的碎片\n", "- 删除冗余文档\n", "- 串联多个压缩器"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from langchain.retrievers.document_compressors import DocumentCompressorPipeline\n", "from langchain_community.document_transformers import EmbeddingsRedundantFilter\n", "from langchain_text_splitters import CharacterTextSplitter\n", "from langchain.retrievers.document_compressors import EmbeddingsFilter\n", "\n", "splitter = CharacterTextSplitter(chunk_size=400, chunk_overlap=0, separator=\". \")\n", "redundant_filter = EmbeddingsRedundantFilter(embeddings=OpenAIEmbeddings())\n", "relevant_filter = EmbeddingsFilter(embeddings=OpenAIEmbeddings(), similarity_threshold=0.76)\n", "pipeline_compressor = DocumentCompressorPipeline(\n", "    transformers=[splitter, redundant_filter, relevant_filter]\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document 1:\n", "\n", "3.1 创建知识库\n", "将需要 AI 分析处理的文档上传至知识库中。为确保 DeepSeek 模型能够准确理解文档内容，建议使用\"父子分段\"模式进行文本处理 - 这种模式能够更好地保留文档的层级结构和上下文关系。如需了解详细的配置步骤，请参考：创建知识库。\n", "\n", "\n", "3.2 将知识库集成至 AI 应用\n", "在 AI 应用的\"上下文\"内添加知识库，在对话框内输入相关问题。LLM 将首先从知识库内获取与问题相关上下文，在此基础上进行总结并给出更高质量的回答。\n", "\n", "\n", "4. 分享 AI 应用\n", "构建完成后，你可以将该 AI 应用分享给他人使用或集成至其它网站内。\n", "\n", "\n", "阅读更多\n", "除了构建简单的 AI 应用外，你还可以创建 Chatflow / Workflow 搭建更多复杂功能的应用（例如具备文件识别、图像识别、语音识别等能力）。详细说明请参考以下文档：\n", "----------------------------------------------------------------------------------------------------\n", "Document 2:\n", "\n", "申请 DeepSeek API\n", "访问 DeepSeek API 开放平台，按照页面提示进行申请 API Key。\n", "\n", "若提示链接无法访问，你也可以考虑在本地部署 DeepSeek 模型。详细说明请参考 本地部署指南\n", "\n", "2. 注册 Dify\n", "Dify 是一个能够帮助你快速搭建生成式 AI 应用的平台，通过接入 DeepSeek API，你可以快速搭建出一个能够易于使用的 DeepSeek AI 应用。\n", "\n", "集成步骤\n", "1. 将 DeepSeek 接入至 Dify\n", "访问 Dify 平台，点击右上角头像 → 设置 → 模型供应商，找到 DeepSeek，将上文获取的 API Key 粘贴至其中。点击保存，校验通过后将出现成功提示。\n", "\n", "\n", "2\n", "----------------------------------------------------------------------------------------------------\n", "Document 3:\n", "\n", "国产硬件推荐配置\n", "模型参数 推荐⽅案 适⽤场景\n", "1.5B 太初T100加速卡 个⼈开发者原型验证\n", "14B 昆仑芯K200集群 企业级复杂任务推理\n", "32B 壁彻算⼒平台+昇腾910B集群 科研计算与多模态处理\n", "12月26日，Deepseek发布了全新系列模型DeepSeek-v3，一夜之间霸榜开源模型，并在性能上和世界顶尖的闭源模型GPT-4o以及 Claude-3.5-Sonnet相提并论。\n", "\n", "该模型为MOE架构，大大降低了训练成本，据说训练成本仅600万美元，成本降低10倍，资源运用效率极高。有AI投资机构负责人直言，DeepSeek发布的53页的技术论文是黄金。\n", "\n", "那就先让我们看看论文是怎么说的吧，老规矩，先上资源地址：\n", "\n", "Github: GitHub - deepseek-ai/DeepSeek-V3\n", "\n", "模型地址：https://huggingface.co/deepseek-ai\n", "\n", "论文地址：https://github.com/deepseek-ai/DeepSeek-V3/blob/main/DeepSeek_V3.pdf\n", "\n", "以下为技术解读：\n", "----------------------------------------------------------------------------------------------------\n", "Document 4:\n", "\n", "搭建 DeepSeek AI 应用\n", "轻点 Dify 平台首页左侧的\"创建空白应用\"，选择\"聊天助手\"类型应用并进行简单的命名。\n", "\n", "\n", "选择 deepseek-reasoner 模型\n", "\n", "deepseek-reasoner 模型又称为 deepseek-r1 模型。\n", "\n", "\n", "配置完成后即可在聊天框中进行互动。\n", "\n", "\n", "3. 为 AI 应用启用文本分析能力\n", "RAG（检索增强生成）是一种先进的信息处理技术，它通过检索相关知识，向 LLM 提供必要的上下文信息，融入 LLM 的内容生成过程，提升回答的准确性和专业度。当你上传内部文档或专业资料后，AI 能够基于这些知识提供更有针对性的解答。\n", "----------------------------------------------------------------------------------------------------\n", "Document 5:\n", "\n", "数据洞察力 - 内置总对话数、应用使用用户数等数据监控模块，支持与更加专业的监控平台集成 ...\n", "\n", "本文将详解 DeepSeek API 与 Dify 的集成步骤，助你快速实现两大核心场景：\n", "\n", "智能对话机器人开发 - 直接调用 DeepSeek R1 的思维链推理能力\n", "\n", "知识增强型应用构建 - 通过私有知识库实现精准信息检索与生成\n", "\n", "针对金融、法律等高合规需求场景，Dify 提供 本地私有化部署 DeepSeek + Dify，支持 DeepSeek 模型与 Dify 平台同步部署至内网\n", "\n", "通过 Dify × DeepSeek 的技术组合，开发者可跳过底层架构搭建，跃迁至场景化 AI 能力落地阶段，让大模型技术快速转化为业务生产力。\n", "\n", "前置准备\n", "1\n", "----------------------------------------------------------------------------------------------------\n", "Document 6:\n", "\n", "Deepseek R1 是⽀持复杂推理、多模态处理、技术⽂档⽣成的⾼性能通⽤⼤语⾔模型。本⼿册\n", "为技术团队提供完整的本地部署指南，涵盖硬件配置、国产芯⽚适配、量化⽅案、云端替代⽅\n", "案及完整671B MoE模型的Ollama部署⽅法。模型 参数规\n", "模\n", "计算精\n", "度\n", "最低显存需\n", "求 最低算⼒需求\n", "DeepSeek-R1 (671B) 671B FP8 ≥890GB\n", "2*XE9680（16*H20\n", "GPU）\n", "DeepSeek-R1-<PERSON><PERSON><PERSON>-\n", "70B\n", "70B BF16 ≥180GB 4*L20 或 2*H20 GPU\n", "三、国产芯⽚与硬件适配⽅案\n", "1\n", "----------------------------------------------------------------------------------------------------\n", "Document 7:\n", "\n", "进一步改进模型架构，以提高训练和推理效率，并尝试突破 Transformer 架构的限制。\n", "持续迭代训练数据的质量和数量，并探索其他训练信号来源，以推动数据扩展到更广泛的维度。\n", "持续探索和迭代模型的深度思考能力，以增强其智能和问题解决能力，并扩展其推理长度和深度。\n", "探索更全面和多维度的模型评估方法，以防止在研究过程中优化固定的一组基准测试，从而产生对模型能力的误导印象并影响我们的基础评估。\n", "DeepSeek-V3 的发布标志着开源大型语言模型领域的一个重大里程碑，并为未来的研究和应用开辟了新的可能性。\n", "\n", "简单测试\n", "DeepSeek-V3开源模型，我肯定是没有资源部署了，所以只能通过它的服务网站进行测试了。\n", "\n", "地址：DeepSeek\n", "\n", "\n", "算一下星舰从地球到火星的飞行时间：\n", "\n", "\n", "让它分析一下自己的技术文档：\n", "\n", "\n", "最后让它比较了一下自己与GPT-4o-0513\n", "\n", "\n", "..\n", "----------------------------------------------------------------------------------------------------\n", "Document 8:\n", "\n", "略...\n", "\n", "\n", "——完——\n", "\n", "@北方的郎 · 专注模型与代码\n", "\n", "概述\n", "前置准备\n", "1. 申请 DeepSeek API\n", "2. 注册 Dify\n", "集成步骤\n", "1. 将 DeepSeek 接入至 Dify\n", "2. 搭建 DeepSeek AI 应用\n", "3. 为 AI 应用启用文本分析能力\n", "4\n"]}], "source": ["compression_retriever = ContextualCompressionRetriever(\n", "    base_compressor=pipeline_compressor, base_retriever=retriever\n", ")\n", "\n", "compressed_docs = compression_retriever.invoke(\n", "    \"如何进行部署\"\n", ")\n", "pretty_print_docs(compressed_docs)"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}