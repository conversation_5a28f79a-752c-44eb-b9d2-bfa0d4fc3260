{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## LCEl支持在运行时候对链进行配置\n", "***\n", "- 动态改写模型的温度\n", "- 动态切换提示词"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 动态改写模型温度"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='42', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 1, 'prompt_tokens': 24, 'total_tokens': 25, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-4eb5f137-38a9-401f-bf2a-7d643c7724c3-0', usage_metadata={'input_tokens': 24, 'output_tokens': 1, 'total_tokens': 25, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import PromptTemplate\n", "from langchain_core.runnables import ConfigurableField\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "\n", "llm = ChatOpenAI(\n", "    model=\"gpt-4\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    ).configurable_fields(\n", "    temperature=ConfigurableField(\n", "        id=\"llm_temperature\",\n", "        name=\"LLM Temperature\",\n", "        description=\"The temperature of the LLM\",\n", "    )\n", ")\n", "\n", "\n", "\n", "llm.invoke(\"随意挑选一个随机数,输出为一个整数\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 在运行时改写温度"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='79', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 1, 'prompt_tokens': 24, 'total_tokens': 25, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-2420b1c5-03b8-4b32-8229-83a4880132ef-0', usage_metadata={'input_tokens': 24, 'output_tokens': 1, 'total_tokens': 25, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.with_config(configurable={\"llm_temperature\": 0.9}).invoke(\"随意挑选一个随机数，输出为一个整数\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 链的提示词动态切换"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatPromptValue(messages=[HumanMessage(content=\"You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.\\nQuestion: foo \\nContext: bar \\nAnswer:\", additional_kwargs={}, response_metadata={})])"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.runnables.hub import HubRunnable\n", "\n", "prompt = HubRunnable(\"rlm/rag-prompt\").configurable_fields(\n", "    owner_repo_commit=ConfigurableField(\n", "        id=\"hub_commit\",\n", "        name=\"<PERSON><PERSON>mmit\",\n", "        description=\"The Hub commit to pull from\",\n", "    )\n", ")\n", "\n", "prompt.invoke({\"question\": \"foo\", \"context\": \"bar\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 在运行时切换提示词"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatPromptValue(messages=[HumanMessage(content=\"[INST]<<SYS>> You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.<</SYS>> \\nQuestion: foo \\nContext: bar \\nAnswer: [/INST]\", additional_kwargs={}, response_metadata={})])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt.with_config(configurable={\"hub_commit\": \"rlm/rag-prompt-llama\"}).invoke(\n", "    {\"question\": \"foo\", \"context\": \"bar\"}\n", ")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}