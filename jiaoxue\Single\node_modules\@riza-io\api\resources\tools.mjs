// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../resource.mjs";
import { isRequestOptions } from "../core.mjs";
import { ToolsPagination } from "../pagination.mjs";
export class Tools extends APIResource {
    /**
     * Create a tool in your project.
     */
    create(body, options) {
        return this._client.post('/v1/tools', { body, ...options });
    }
    /**
     * Update the source code and input schema of a tool.
     */
    update(id, body, options) {
        return this._client.post(`/v1/tools/${id}`, { body, ...options });
    }
    list(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/tools', ToolsToolsPagination, { query, ...options });
    }
    /**
     * Execute a tool with a given input. The input is validated against the tool's
     * input schema.
     */
    exec(id, body, options) {
        return this._client.post(`/v1/tools/${id}/execute`, { body, ...options });
    }
    /**
     * Retrieves a tool.
     */
    get(id, options) {
        return this._client.get(`/v1/tools/${id}`, options);
    }
}
export class ToolsToolsPagination extends ToolsPagination {
}
Tools.ToolsToolsPagination = ToolsToolsPagination;
//# sourceMappingURL=tools.mjs.map