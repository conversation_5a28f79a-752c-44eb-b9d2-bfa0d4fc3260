import { APIResource } from "../resource.js";
import * as Core from "../core.js";
import { SecretsPagination, type SecretsPaginationParams } from "../pagination.js";
export declare class Secrets extends APIResource {
    /**
     * Create a secret in your project.
     */
    create(body: SecretCreateParams, options?: Core.RequestOptions): Core.APIPromise<Secret>;
    /**
     * Returns a list of secrets in your project.
     */
    list(query?: SecretListParams, options?: Core.RequestOptions): Core.PagePromise<SecretsSecretsPagination, Secret>;
    list(options?: Core.RequestOptions): Core.PagePromise<SecretsSecretsPagination, Secret>;
}
export declare class SecretsSecretsPagination extends SecretsPagination<Secret> {
}
export interface Secret {
    id: string;
    name: string;
}
export interface SecretCreateParams {
    name: string;
    value: string;
}
export interface SecretListParams extends SecretsPaginationParams {
}
export declare namespace Secrets {
    export { type Secret as Secret, SecretsSecretsPagination as SecretsSecretsPagination, type Secret<PERSON>reate<PERSON>arams as SecretCreateParams, type SecretListParams as SecretListParams, };
}
//# sourceMappingURL=secrets.d.ts.map