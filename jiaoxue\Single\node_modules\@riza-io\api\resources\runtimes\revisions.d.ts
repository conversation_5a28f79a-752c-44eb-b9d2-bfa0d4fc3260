import { APIResource } from "../../resource.js";
import * as Core from "../../core.js";
export declare class Revisions extends APIResource {
    /**
     * Creates a new runtime revision.
     */
    create(id: string, body: RevisionCreateParams, options?: Core.RequestOptions): Core.APIPromise<Revision>;
    /**
     * Lists all revisions for a runtime.
     */
    list(id: string, options?: Core.RequestOptions): Core.APIPromise<RevisionListResponse>;
    /**
     * Retrieves a runtime revision.
     */
    get(runtimeId: string, revisionId: string, options?: Core.RequestOptions): Core.APIPromise<Revision>;
}
export interface Revision {
    id: string;
    manifest_file: Revision.ManifestFile;
    runtime_id: string;
    status: 'pending' | 'building' | 'succeeded' | 'failed' | 'cancelled';
    additional_python_imports?: string;
}
export declare namespace Revision {
    interface ManifestFile {
        contents: string;
        name: 'requirements.txt' | 'package.json';
    }
}
export interface RevisionListResponse {
    revisions: Array<Revision>;
}
export interface RevisionCreateParams {
    manifest_file: RevisionCreateParams.ManifestFile;
    additional_python_imports?: string;
}
export declare namespace RevisionCreateParams {
    interface ManifestFile {
        contents: string;
        name: 'requirements.txt' | 'package.json';
    }
}
export declare namespace Revisions {
    export { type Revision as Revision, type RevisionListResponse as RevisionListResponse, type RevisionCreateParams as RevisionCreateParams, };
}
//# sourceMappingURL=revisions.d.ts.map