{"name": "humanize-ms", "version": "1.2.1", "description": "transform humanize time to ms", "main": "index.js", "files": ["index.js"], "scripts": {"test": "make test"}, "keywords": ["humanize", "ms"], "author": {"name": "dead-horse", "email": "<EMAIL>", "url": "http://deadhorse.me"}, "repository": {"type": "git", "url": "https://github.com/node-modules/humanize-ms"}, "license": "MIT", "dependencies": {"ms": "^2.0.0"}, "devDependencies": {"autod": "*", "beautify-benchmark": "~0.2.4", "benchmark": "~1.0.0", "istanbul": "*", "mocha": "*", "should": "*"}}