{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 并行运行多条链\n", "**** "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_deepseek import ChatDeepSeek\n", "import os\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid character '：' (U+FF1A) (2772573888.py, line 1)", "output_type": "error", "traceback": ["\u001b[0;36m  Cell \u001b[0;32mIn[2], line 1\u001b[0;36m\u001b[0m\n\u001b[0;31m    并行运行链会构造：\u001b[0m\n\u001b[0m            ^\u001b[0m\n\u001b[0;31mSyntaxError\u001b[0m\u001b[0;31m:\u001b[0m invalid character '：' (U+FF1A)\n"]}], "source": ["并行运行链会构造：\n", "     Input\n", "      / \\\n", "     /   \\\n", " Branch1 Branch2\n", "     \\   /\n", "      \\ /\n", "      Combine"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'joke': AIMessage(content='## 程序员的笑话\\n\\n**笑话一：**\\n\\n一个程序员走进一家酒吧，对酒保说：“给我来一杯 Hello World！”\\n\\n酒保一脸茫然：“什么？”\\n\\n程序员叹了口气：“算了，给我来杯 404 吧。”\\n\\n**笑话二：**\\n\\n两个程序员在聊天。\\n\\n程序员A：“我昨天梦见自己变成了一台电脑。”\\n\\n程序员B：“然后呢？”\\n\\n程序员A：“然后我就蓝屏了。”\\n\\n**笑话三：**\\n\\n一个程序员对他的女朋友说：“亲爱的，我写了一首诗给你。”\\n\\n女朋友：“真的吗？快念给我听！”\\n\\n程序员：“while (true) { I love you; }”\\n\\n**笑话四：**\\n\\n一个程序员在面试。\\n\\n面试官：“你最大的优点是什么？”\\n\\n程序员：“我从不写 bug。”\\n\\n面试官：“那你最大的缺点呢？”\\n\\n程序员：“我从不写代码。”\\n\\n**笑话五：**\\n\\n一个程序员对他的朋友说：“我最近在学习冥想。”\\n\\n朋友：“效果怎么样？”\\n\\n程序员：“还不错，我现在可以连续 8 个小时什么都不想，就像在调试代码一样。”\\n\\n**笑话六：**\\n\\n一个程序员对他的老板说：“我需要加薪。”\\n\\n老板：“为什么？”\\n\\n程序员：“因为我的代码已经优化到无法再优化了。”\\n\\n老板：“那好吧，我给你加薪 10%。”\\n\\n程序员：“谢谢老板，现在我的代码又可以优化了。”\\n\\n**笑话七：**\\n\\n一个程序员对他的同事说：“我昨天梦见自己变成了一台服务器。”\\n\\n同事：“然后呢？”\\n\\n程序员：“然后我就被 DDoS 攻击了。”\\n\\n**笑话八：**\\n\\n一个程序员对他的朋友说：“我最近在学习心理学。”\\n\\n朋友：“为什么？”\\n\\n程序员：“因为我想知道为什么我的代码总是让我崩溃。”\\n\\n**笑话九：**\\n\\n一个程序员对他的女朋友说：“亲爱的，我写了一首歌给你。”\\n\\n女朋友：“真的吗？快唱给我听！”\\n\\n程序员：“Error 404: Song not found.”\\n\\n**笑话十：**\\n\\n一个程序员对他的老板说：“我需要休假。”\\n\\n老板：“为什么？”\\n\\n程序员：“因为我的大脑已经缓存满了，需要清理一下。”\\n\\n老板：“那好吧，给你一周的假期。”\\n\\n程序员：“谢谢老板，现在我的大脑又可以缓存更多代码了。”\\n\\n**希望这些笑话能让你会心一笑！**', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 435, 'prompt_tokens': 10, 'total_tokens': 445, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-97ec6144-43d5-4436-b200-6a4558639fc4-0', usage_metadata={'input_tokens': 10, 'output_tokens': 435, 'total_tokens': 445, 'input_token_details': {}, 'output_token_details': {}}),\n", " 'poem': AIMessage(content='代码如诗夜未央，  \\n键盘轻响月微凉。  \\n千行万句皆心血，  \\n一梦成真世界光。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 31, 'prompt_tokens': 11, 'total_tokens': 42, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-8878433a-5151-4c15-95af-aa8f2fed78b7-0', usage_metadata={'input_tokens': 11, 'output_tokens': 31, 'total_tokens': 42, 'input_token_details': {}, 'output_token_details': {}})}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnableParallel\n", "\n", "joke_chain = ChatPromptTemplate.from_template(\"给我讲一个关于{topic}的笑话\") | llm\n", "poem_chain = (\n", "    ChatPromptTemplate.from_template(\"给我写一首关于{topic}的绝句\") | llm\n", ")\n", "\n", "map_chain = RunnableParallel(joke=joke_chain, poem=poem_chain)\n", "\n", "map_chain.invoke({\"topic\": \"程序员\"})"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}