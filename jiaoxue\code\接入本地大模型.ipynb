{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 接入本地大模型\n", "***\n", "- ollama"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 安装依赖\n", "****"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! pip install langchain-ollama==0.1.3"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 接入模型\n", "***"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='\\n你好，欢迎来到我的语言模型中。请告诉我你需要什么帮助或问题解答。\\n', response_metadata={'model': 'llama2-chinese:latest', 'created_at': '2025-02-13T14:58:51.1681Z', 'done': True, 'done_reason': 'stop', 'total_duration': 496503557333, 'load_duration': 569020083, 'prompt_eval_count': 13, 'prompt_eval_duration': 17945000000, 'eval_count': 47, 'eval_duration': 477967000000, 'message': Message(role='assistant', content='', images=None, tool_calls=None)}, id='run-42ef6378-ae27-42de-908d-b272c589be79-0', usage_metadata={'input_tokens': 13, 'output_tokens': 47, 'total_tokens': 60})"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_ollama import ChatOllama\n", "\n", "llm = ChatOllama(\n", "    model=\"llama2-chinese:latest\",\n", "    temperature=0,\n", ")\n", "llm.invoke(\"你好\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 本地跑deepseek 7B\n", "****"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain_ollama import ChatOllama\n", "\n", "llm = ChatOllama(\n", "    model=\"deepseek-r1:7b\",\n", "    temperature=0,\n", ")\n", "for chunk in llm.stream(\"介绍下自己\"):\n", "    print(chunk)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}