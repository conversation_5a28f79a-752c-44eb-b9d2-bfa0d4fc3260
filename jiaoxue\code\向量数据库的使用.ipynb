{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 向量数据库的使用\n", "***\n", "- 向量库的数据增加\n", "- 向量库的数据删除\n", "- 向量库的相似性搜索\n", "- 高级使用：MMR\n", "- 高级使用：混合搜索"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 向量库的数据增加\n", "****"]}, {"cell_type": "markdown", "metadata": {}, "source": ["统一使用国产嵌入模型"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "import os\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key = \"sk-khjeakixzbxqaraibcdenuhfrlostygebnqkwvyrtbrxhwmu\",\n", "    base_url = \"https://api.siliconflow.cn/v1\",\n", "    # api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    # base_url=os.environ.get(\"DEEPSEEK_API_BASE\")+\"/v1\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["为了演示方便我们引入一个内存向量数据库，它将向量暂存在内存中，并使用字典以及numpy计算搜索的余弦相似度。"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from langchain_core.vectorstores import InMemoryVectorStore\n", "vector_store = InMemoryVectorStore(embedding=embeddings_model)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["['7679c6fd-cc74-423c-9aee-89c35941fecd',\n", " '7855cc33-0331-4514-9b89-baa96a2f719c']"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.documents import Document\n", "\n", "document_1 = Document(\n", "    page_content=\"今天在抖音学会了一个新菜：锅巴土豆泥！看起来简单，实际炸了厨房，连猫都嫌弃地走开了。\",\n", "    metadata={\"source\": \"社交媒体\"},\n", ")\n", "\n", "document_2 = Document(\n", "    page_content=\"小区遛狗大爷今日播报：广场舞大妈占领健身区，遛狗群众纷纷撤退。现场气氛诡异，BGM已循环播放《最炫民族风》两小时。\",\n", "    metadata={\"source\": \"社区新闻\"},\n", ")\n", "\n", "documents = [document_1, document_2]\n", "\n", "vector_store.add_documents(documents=documents)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["你可以为添加的文档增加ID索引，便于后面管理"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Document(id='doc2', metadata={'source': '社区新闻'}, page_content='小区遛狗大爷今日播报：广场舞大妈占领健身区，遛狗群众纷纷撤退。现场气氛诡异，BGM已循环播放《最炫民族风》两小时。'), Document(id='7855cc33-0331-4514-9b89-baa96a2f719c', metadata={'source': '社区新闻'}, page_content='小区遛狗大爷今日播报：广场舞大妈占领健身区，遛狗群众纷纷撤退。现场气氛诡异，BGM已循环播放《最炫民族风》两小时。'), Document(id='doc1', metadata={'source': '社交媒体'}, page_content='今天在抖音学会了一个新菜：锅巴土豆泥！看起来简单，实际炸了厨房，连猫都嫌弃地走开了。'), Document(id='7679c6fd-cc74-423c-9aee-89c35941fecd', metadata={'source': '社交媒体'}, page_content='今天在抖音学会了一个新菜：锅巴土豆泥！看起来简单，实际炸了厨房，连猫都嫌弃地走开了。')]\n"]}], "source": ["vector_store.add_documents(documents=documents, ids=[\"doc1\", \"doc2\"])\n", "result = vector_store.similarity_search(\"怎么做菜\")\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 向量库的删除\n", "****"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["vector_store.delete(ids=[\"doc1\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 向量库的相似性搜索\n", "****\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["小区遛狗大爷今日播报：广场舞大妈占领健身区，遛狗群众纷纷撤退。现场气氛诡异，BGM已循环播放《最炫民族风》两小时。\n"]}], "source": ["query = \"遛狗\"\n", "docs = vector_store.similarity_search(query)\n", "print(docs[0].page_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["还可以使用“向量”查相似”向量“的方式来进行搜索"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["小区遛狗大爷今日播报：广场舞大妈占领健身区，遛狗群众纷纷撤退。现场气氛诡异，BGM已循环播放《最炫民族风》两小时。\n"]}], "source": ["embedding_vector = embeddings_model.embed_query(query)\n", "docs = vector_store.similarity_search_by_vector(embedding_vector)\n", "print(docs[0].page_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["注意：langchain只是在接口层面进行了封装，具体的搜索实现要依赖向量库本身的能力，比如Pinecone就可以进行元数据过滤，内存向量就不可以"]}, {"cell_type": "markdown", "metadata": {}, "source": ["https://docs.pinecone.io/guides/get-started/quickstart"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install -qU langchain-pinecone pinecone-notebooks"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Error while installing plugin inference: property 'inference' of 'Pinecone' object has no setter\n", "Traceback (most recent call last):\n", "  File \"/Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages/pinecone_plugin_interface/actions/installation.py\", line 13, in install_plugins\n", "    setattr(target, plugin.namespace, impl(target.config, plugin_client_builder))\n", "    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "AttributeError: property 'inference' of 'Pinecone' object has no setter\n"]}], "source": ["import getpass\n", "import os\n", "import time\n", "\n", "from pinecone import Pinecone, ServerlessSpec\n", "\n", "if not os.getenv(\"PINECONE_API_KEY\"):\n", "    os.environ[\"PINECONE_API_KEY\"] = getpass.getpass(\"Enter your Pinecone API key: \")\n", "\n", "pinecone_api_key = os.environ.get(\"PINECONE_API_KEY\")\n", "\n", "pc = Pinecone(api_key=pinecone_api_key)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["初始化"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "index_name = \"langchain-test-index\"  # change if desired\n", "\n", "existing_indexes = [index_info[\"name\"] for index_info in pc.list_indexes()]\n", "\n", "if index_name not in existing_indexes:\n", "    pc.create_index(\n", "        name=index_name,\n", "        dimension=3072, #注意维度要一致\n", "        metric=\"cosine\",\n", "        spec=ServerlessSpec(cloud=\"aws\", region=\"us-east-1\"),\n", "    )\n", "    while not pc.describe_index(index_name).status[\"ready\"]:\n", "        time.sleep(1)\n", "\n", "index = pc.Index(index_name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["添加嵌入模型"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["from langchain_pinecone import PineconeVectorStore\n", "# 因为国产嵌入模型的维度只有1024，所以无法使用\n", "vector_store = PineconeVectorStore(index=index, embedding=OpenAIEmbeddings(model=\"text-embedding-3-large\"))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["['4a167660-65a2-4c22-a7f0-4e2f1c27f798',\n", " '1687e3b4-d8cc-4652-b9e8-1626c146975a',\n", " '555413b0-5677-44c0-a574-3e081ecf22a1',\n", " '5ef9765d-e32a-4d8a-9f20-49ec6f6296c6',\n", " '90503ce6-9197-4738-9f5d-f7031b30f2e3',\n", " '9ad5e511-8dcf-4358-a4ab-d792baff6fa7',\n", " 'b5e9d05a-fb8f-4d9b-bd3c-4e86f4cceda6',\n", " 'def48eea-cce5-4803-ad7f-a450cf64eddf',\n", " 'a327e865-100c-4321-8ea8-d5355b537167',\n", " 'bebf9b8d-4cb1-4617-8c87-a36e0fb9c364']"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from uuid import uuid4\n", "from langchain_core.documents import Document\n", "\n", "document_1 = Document(\n", "    page_content=\"今天早餐吃了老王家的生煎包，馅料实在得快从褶子里跳出来了！这才是真正的上海味道！\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_2 = Document(\n", "    page_content=\"明日天气预报：北京地区将出现大范围雾霾，建议市民戴好口罩，看不见脸的时候请不要慌张。\",\n", "    metadata={\"source\": \"news\"},\n", ")\n", "\n", "document_3 = Document(\n", "    page_content=\"终于搞定了AI聊天机器人！我问它'你是谁'，它回答'我是你爸爸'，看来还需要调教...\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_4 = Document(\n", "    page_content=\"震惊！本市一男子在便利店抢劫，只因店员说'扫码支付才有优惠'，现已被警方抓获。\",\n", "    metadata={\"source\": \"news\"},\n", ")\n", "\n", "document_5 = Document(\n", "    page_content=\"刚看完《流浪地球3》，特效简直炸裂！就是旁边大妈一直问'这是在哪拍的'有点影响观影体验。\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_6 = Document(\n", "    page_content=\"新发布的小米14Ultra值不值得买？看完这篇测评你就知道为什么李老板笑得合不拢嘴了。\",\n", "    metadata={\"source\": \"website\"},\n", ")\n", "\n", "document_7 = Document(\n", "    page_content=\"2025年中超联赛十大最佳球员榜单新鲜出炉，第一名居然是他？！\",\n", "    metadata={\"source\": \"website\"},\n", ")\n", "\n", "document_8 = Document(\n", "    page_content=\"用LangChain开发的AI助手太神奇了！问它'人生的意义'，它给我推荐了一份外卖优惠券...\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "document_9 = Document(\n", "    page_content=\"A股今日暴跌，分析师称原因是'大家都在抢着卖'，投资者表示很有道理。\",\n", "    metadata={\"source\": \"news\"},\n", ")\n", "\n", "document_10 = Document(\n", "    page_content=\"感觉我马上要被删库跑路了，祝我好运 /(ㄒoㄒ)/~~\",\n", "    metadata={\"source\": \"tweet\"},\n", ")\n", "\n", "documents = [\n", "    document_1,\n", "    document_2,\n", "    document_3,\n", "    document_4,\n", "    document_5,\n", "    document_6,\n", "    document_7,\n", "    document_8,\n", "    document_9,\n", "    document_10,\n", "]\n", "uuids = [str(uuid4()) for _ in range(len(documents))]\n", "\n", "vector_store.add_documents(documents=documents, ids=uuids)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["删除啊最后一项"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["vector_store.delete(ids=[uuids[-1]])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["相似性搜索支持元数据过滤"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* 刚看完《流浪地球3》，特效简直炸裂！就是旁边大妈一直问'这是在哪拍的'有点影响观影体验。 [{'source': 'tweet'}]\n"]}], "source": ["results = vector_store.similarity_search(\n", "    \"看电影\",\n", "    k=1,\n", "    filter={\"source\": \"tweet\"},\n", ")\n", "for res in results:\n", "    print(f\"* {res.page_content} [{res.metadata}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["通过分数进行搜索"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* [SIM=0.468292] 明日天气预报：北京地区将出现大范围雾霾，建议市民戴好口罩，看不见脸的时候请不要慌张。 [{'source': 'news'}]\n"]}], "source": ["results = vector_store.similarity_search_with_score(\n", "    \"明天热吗?\", k=1, filter={\"source\": \"news\"}\n", ")\n", "for res, score in results:\n", "    print(f\"* [SIM={score:3f}] {res.page_content} [{res.metadata}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MMR（最大边际相关性）\n", "****\n", "- 并非所有向量库支持"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'website'}, page_content='新发布的小米14Ultra值不值得买？看完这篇测评你就知道为什么李老板笑得合不拢嘴了。')]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store.max_marginal_relevance_search(\n", "    query=\"新手机\",\n", "    k=1,\n", "    lambda_val=0.8,\n", "    filter={\"source\": \"website\"},\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 混合搜索\n", "****\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "langchain-pinecone 0.2.3 requires pinecone<6.0.0,>=5.4.0, but you have pinecone 6.0.1 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install --upgrade --quiet  pinecone pinecone-text pinecone-notebooks"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "api_key = os.environ[\"PINECONE_API_KEY\"]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["from langchain_community.retrievers import (\n", "    PineconeHybridSearchRetriever,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["初始化"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Error while installing plugin inference: property 'inference' of 'Pinecone' object has no setter\n", "Traceback (most recent call last):\n", "  File \"/Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages/pinecone_plugin_interface/actions/installation.py\", line 13, in install_plugins\n", "    setattr(target, plugin.namespace, impl(target.config, plugin_client_builder))\n", "    ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "AttributeError: property 'inference' of 'Pinecone' object has no setter\n"]}], "source": ["import os\n", "\n", "from pinecone import Pinecone, ServerlessSpec\n", "\n", "index_name = \"langchain-pinecone-hybrid-search\"\n", "\n", "# initialize Pinecone client\n", "pc = Pinecone(api_key=api_key)\n", "\n", "# create the index\n", "if index_name not in pc.list_indexes().names():\n", "    pc.create_index(\n", "        name=index_name,\n", "        dimension=1536,  # dimensionality of dense model\n", "        metric=\"dotproduct\",  # sparse values supported only for dotproduct\n", "        spec=ServerlessSpec(cloud=\"aws\", region=\"us-east-1\"),\n", "    )"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["index = pc.Index(index_name)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "\n", "embeddings = OpenAIEmbeddings()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["from pinecone_text.sparse import BM25Encoder\n", "\n", "# or from pinecone_text.sparse import SpladeEncoder if you wish to work with SPLADE\n", "\n", "# use default tf-idf values\n", "bm25_encoder = BM25Encoder().default()"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 4/4 [00:00<00:00, 247.62it/s]\n"]}], "source": ["corpus = [\"foo\", \"bar\", \"world\", \"hello\"]\n", "\n", "# fit tf-idf values on your corpus\n", "bm25_encoder.fit(corpus)\n", "\n", "# store the values to a json file\n", "bm25_encoder.dump(\"bm25_values.json\")\n", "\n", "# load to your BM25Encoder object\n", "bm25_encoder = BM25Encoder().load(\"bm25_values.json\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["retriever = PineconeHybridSearchRetriever(\n", "    embeddings=embeddings, sparse_encoder=bm25_encoder, index=index\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["添加文本"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [00:04<00:00,  4.96s/it]\n"]}], "source": ["retriever.add_texts([\"foo\", \"bar\", \"world\", \"hello\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["检索"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["result = retriever.invoke(\"foo\")\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'score': 0.730220914}, page_content='foo')"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["result[0]"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}