import { APIResource } from "../../resource.js";
import * as Core from "../../core.js";
import * as RevisionsAPI from "./revisions.js";
import { Revision, RevisionCreateParams, RevisionListResponse, Revisions } from "./revisions.js";
import { RuntimesPagination, type RuntimesPaginationParams } from "../../pagination.js";
export declare class Runtimes extends APIResource {
    revisions: RevisionsAPI.Revisions;
    /**
     * Creates a runtime.
     */
    create(body: RuntimeCreateParams, options?: Core.RequestOptions): Core.APIPromise<Runtime>;
    /**
     * Returns a list of runtimes in your project.
     */
    list(query?: RuntimeListParams, options?: Core.RequestOptions): Core.PagePromise<RuntimesRuntimesPagination, Runtime>;
    list(options?: Core.RequestOptions): Core.PagePromise<RuntimesRuntimesPagination, Runtime>;
    /**
     * Deletes a runtime.
     */
    delete(id: string, options?: Core.RequestOptions): Core.APIPromise<RuntimeDeleteResponse>;
    /**
     * Retrieves a runtime.
     */
    get(id: string, options?: Core.RequestOptions): Core.APIPromise<Runtime>;
}
export declare class RuntimesRuntimesPagination extends RuntimesPagination<Runtime> {
}
export interface Runtime {
    id: string;
    engine: 'wasi' | 'microvm' | 'v8';
    language: 'python' | 'javascript';
    name: string;
    revision_id: string;
    status: 'pending' | 'building' | 'succeeded' | 'failed' | 'cancelled';
    additional_python_imports?: string;
    manifest_file?: Runtime.ManifestFile;
}
export declare namespace Runtime {
    interface ManifestFile {
        contents: string;
        name: 'requirements.txt' | 'package.json';
    }
}
export interface RuntimeDeleteResponse {
    id?: string;
    deleted?: boolean;
}
export interface RuntimeCreateParams {
    language: 'python' | 'javascript';
    manifest_file: RuntimeCreateParams.ManifestFile;
    name: string;
    additional_python_imports?: string;
    engine?: 'wasi' | 'microvm' | 'v8';
}
export declare namespace RuntimeCreateParams {
    interface ManifestFile {
        contents: string;
        name: 'requirements.txt' | 'package.json';
    }
}
export interface RuntimeListParams extends RuntimesPaginationParams {
}
export declare namespace Runtimes {
    export { type Runtime as Runtime, type RuntimeDeleteResponse as RuntimeDeleteResponse, RuntimesRuntimesPagination as RuntimesRuntimesPagination, type RuntimeCreateParams as RuntimeCreateParams, type RuntimeListParams as RuntimeListParams, };
    export { Revisions as Revisions, type Revision as Revision, type RevisionListResponse as RevisionListResponse, type RevisionCreateParams as RevisionCreateParams, };
}
//# sourceMappingURL=runtimes.d.ts.map