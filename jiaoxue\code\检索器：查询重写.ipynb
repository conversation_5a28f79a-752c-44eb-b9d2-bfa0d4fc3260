{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 检索器：查询重写\n", "*****\n", "- 使用多重查询来改写问题"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting langchain_chroma\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/61/70/384bcdcaac7cb74e239a8411a9bd16079c7e0d9174391d66c635c45514f4/langchain_chroma-0.2.4-py3-none-any.whl (11 kB)\n", "Requirement already satisfied: langchain-core>=0.3.60 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langchain_chroma) (0.3.66)\n", "Requirement already satisfied: numpy>=2.1.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langchain_chroma) (2.3.1)\n", "Collecting chromadb>=1.0.9 (from langchain_chroma)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/98/3d/1df1b47a3fba6bc4dc78cf042a440bfa068b1bae2524c3b99ef0538be38c/chromadb-1.0.13-cp39-abi3-win_amd64.whl (19.3 MB)\n", "     ---------------------------------------- 0.0/19.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/19.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/19.3 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/19.3 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/19.3 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/19.3 MB ? eta -:--:--\n", "     - ------------------------------------- 0.5/19.3 MB 587.5 kB/s eta 0:00:33\n", "     - ------------------------------------- 0.5/19.3 MB 587.5 kB/s eta 0:00:33\n", "     - ------------------------------------- 0.5/19.3 MB 587.5 kB/s eta 0:00:33\n", "     - ------------------------------------- 0.5/19.3 MB 587.5 kB/s eta 0:00:33\n", "     - ------------------------------------- 0.8/19.3 MB 367.8 kB/s eta 0:00:51\n", "     - ------------------------------------- 0.8/19.3 MB 367.8 kB/s eta 0:00:51\n", "     - ------------------------------------- 0.8/19.3 MB 367.8 kB/s eta 0:00:51\n", "     - ------------------------------------- 0.8/19.3 MB 367.8 kB/s eta 0:00:51\n", "     - ------------------------------------- 0.8/19.3 MB 367.8 kB/s eta 0:00:51\n", "     -- ------------------------------------ 1.0/19.3 MB 331.0 kB/s eta 0:00:56\n", "     -- ------------------------------------ 1.0/19.3 MB 331.0 kB/s eta 0:00:56\n", "     -- ------------------------------------ 1.0/19.3 MB 331.0 kB/s eta 0:00:56\n", "     -- ------------------------------------ 1.0/19.3 MB 331.0 kB/s eta 0:00:56\n", "     -- ------------------------------------ 1.3/19.3 MB 320.2 kB/s eta 0:00:57\n", "     -- ------------------------------------ 1.3/19.3 MB 320.2 kB/s eta 0:00:57\n", "     -- ------------------------------------ 1.3/19.3 MB 320.2 kB/s eta 0:00:57\n", "     -- ------------------------------------ 1.3/19.3 MB 320.2 kB/s eta 0:00:57\n", "     -- ------------------------------------ 1.3/19.3 MB 320.2 kB/s eta 0:00:57\n", "     --- ----------------------------------- 1.6/19.3 MB 308.6 kB/s eta 0:00:58\n", "     --- ----------------------------------- 1.6/19.3 MB 308.6 kB/s eta 0:00:58\n", "     --- ----------------------------------- 1.6/19.3 MB 308.6 kB/s eta 0:00:58\n", "     --- ----------------------------------- 1.6/19.3 MB 308.6 kB/s eta 0:00:58\n", "     --- ----------------------------------- 1.6/19.3 MB 308.6 kB/s eta 0:00:58\n", "     --- ----------------------------------- 1.6/19.3 MB 308.6 kB/s eta 0:00:58\n", "     --- ----------------------------------- 1.8/19.3 MB 286.2 kB/s eta 0:01:02\n", "     --- ----------------------------------- 1.8/19.3 MB 286.2 kB/s eta 0:01:02\n", "     --- ----------------------------------- 1.8/19.3 MB 286.2 kB/s eta 0:01:02\n", "     --- ----------------------------------- 1.8/19.3 MB 286.2 kB/s eta 0:01:02\n", "     --- ----------------------------------- 1.8/19.3 MB 286.2 kB/s eta 0:01:02\n", "     ---- ---------------------------------- 2.1/19.3 MB 273.0 kB/s eta 0:01:04\n", "     ---- ---------------------------------- 2.1/19.3 MB 273.0 kB/s eta 0:01:04\n", "     ---- ---------------------------------- 2.1/19.3 MB 273.0 kB/s eta 0:01:04\n", "     ---- ---------------------------------- 2.1/19.3 MB 273.0 kB/s eta 0:01:04\n", "     ---- ---------------------------------- 2.1/19.3 MB 273.0 kB/s eta 0:01:04\n", "     ---- ---------------------------------- 2.1/19.3 MB 273.0 kB/s eta 0:01:04\n", "     ---- ---------------------------------- 2.1/19.3 MB 273.0 kB/s eta 0:01:04\n", "     ---- ---------------------------------- 2.4/19.3 MB 260.5 kB/s eta 0:01:06\n", "     ---- ---------------------------------- 2.4/19.3 MB 260.5 kB/s eta 0:01:06\n", "     ---- ---------------------------------- 2.4/19.3 MB 260.5 kB/s eta 0:01:06\n", "     ---- ---------------------------------- 2.4/19.3 MB 260.5 kB/s eta 0:01:06\n", "     ---- ---------------------------------- 2.4/19.3 MB 260.5 kB/s eta 0:01:06\n", "     ---- ---------------------------------- 2.4/19.3 MB 260.5 kB/s eta 0:01:06\n", "     ----- --------------------------------- 2.6/19.3 MB 250.7 kB/s eta 0:01:07\n", "     ----- --------------------------------- 2.6/19.3 MB 250.7 kB/s eta 0:01:07\n", "     ----- --------------------------------- 2.6/19.3 MB 250.7 kB/s eta 0:01:07\n", "     ----- --------------------------------- 2.6/19.3 MB 250.7 kB/s eta 0:01:07\n", "     ----- --------------------------------- 2.9/19.3 MB 256.3 kB/s eta 0:01:05\n", "     ----- --------------------------------- 2.9/19.3 MB 256.3 kB/s eta 0:01:05\n", "     ----- --------------------------------- 2.9/19.3 MB 256.3 kB/s eta 0:01:05\n", "     ----- --------------------------------- 2.9/19.3 MB 256.3 kB/s eta 0:01:05\n", "     ----- --------------------------------- 2.9/19.3 MB 256.3 kB/s eta 0:01:05\n", "     ----- --------------------------------- 2.9/19.3 MB 256.3 kB/s eta 0:01:05\n", "     ----- --------------------------------- 2.9/19.3 MB 256.3 kB/s eta 0:01:05\n", "     ----- --------------------------------- 2.9/19.3 MB 256.3 kB/s eta 0:01:05\n", "     ------ -------------------------------- 3.1/19.3 MB 240.3 kB/s eta 0:01:08\n", "     ------ -------------------------------- 3.1/19.3 MB 240.3 kB/s eta 0:01:08\n", "     ------ -------------------------------- 3.1/19.3 MB 240.3 kB/s eta 0:01:08\n", "     ------ -------------------------------- 3.1/19.3 MB 240.3 kB/s eta 0:01:08\n", "     ------ -------------------------------- 3.1/19.3 MB 240.3 kB/s eta 0:01:08\n", "     ------ -------------------------------- 3.1/19.3 MB 240.3 kB/s eta 0:01:08\n", "     ------ -------------------------------- 3.1/19.3 MB 240.3 kB/s eta 0:01:08\n", "     ------ -------------------------------- 3.1/19.3 MB 240.3 kB/s eta 0:01:08\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------ -------------------------------- 3.4/19.3 MB 230.4 kB/s eta 0:01:10\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.7/19.3 MB 210.3 kB/s eta 0:01:15\n", "     ------- ------------------------------- 3.9/19.3 MB 199.9 kB/s eta 0:01:18\n", "     ------- ------------------------------- 3.9/19.3 MB 199.9 kB/s eta 0:01:18\n", "     ------- ------------------------------- 3.9/19.3 MB 199.9 kB/s eta 0:01:18\n", "     ------- ------------------------------- 3.9/19.3 MB 199.9 kB/s eta 0:01:18\n", "     ------- ------------------------------- 3.9/19.3 MB 199.9 kB/s eta 0:01:18\n", "     ------- ------------------------------- 3.9/19.3 MB 199.9 kB/s eta 0:01:18\n", "     ------- ------------------------------- 3.9/19.3 MB 199.9 kB/s eta 0:01:18\n", "     ------- ------------------------------- 3.9/19.3 MB 199.9 kB/s eta 0:01:18\n", "     ------- ------------------------------- 3.9/19.3 MB 199.9 kB/s eta 0:01:18\n", "     -------- ------------------------------ 4.2/19.3 MB 193.3 kB/s eta 0:01:19\n", "     -------- ------------------------------ 4.2/19.3 MB 193.3 kB/s eta 0:01:19\n", "     -------- ------------------------------ 4.2/19.3 MB 193.3 kB/s eta 0:01:19\n", "     -------- ------------------------------ 4.2/19.3 MB 193.3 kB/s eta 0:01:19\n", "     -------- ------------------------------ 4.2/19.3 MB 193.3 kB/s eta 0:01:19\n", "     -------- ------------------------------ 4.2/19.3 MB 193.3 kB/s eta 0:01:19\n", "     -------- ------------------------------ 4.2/19.3 MB 193.3 kB/s eta 0:01:19\n", "     -------- ------------------------------ 4.2/19.3 MB 193.3 kB/s eta 0:01:19\n", "     -------- ------------------------------ 4.2/19.3 MB 193.3 kB/s eta 0:01:19\n", "     -------- ------------------------------ 4.2/19.3 MB 193.3 kB/s eta 0:01:19\n", "     -------- ------------------------------ 4.5/19.3 MB 186.4 kB/s eta 0:01:20\n", "     -------- ------------------------------ 4.5/19.3 MB 186.4 kB/s eta 0:01:20\n", "     -------- ------------------------------ 4.5/19.3 MB 186.4 kB/s eta 0:01:20\n", "     -------- ------------------------------ 4.5/19.3 MB 186.4 kB/s eta 0:01:20\n", "     -------- ------------------------------ 4.5/19.3 MB 186.4 kB/s eta 0:01:20\n", "     -------- ------------------------------ 4.5/19.3 MB 186.4 kB/s eta 0:01:20\n", "     -------- ------------------------------ 4.5/19.3 MB 186.4 kB/s eta 0:01:20\n", "     -------- ------------------------------ 4.5/19.3 MB 186.4 kB/s eta 0:01:20\n", "     --------- ----------------------------- 4.7/19.3 MB 184.4 kB/s eta 0:01:20\n", "     --------- ----------------------------- 4.7/19.3 MB 184.4 kB/s eta 0:01:20\n", "     --------- ----------------------------- 4.7/19.3 MB 184.4 kB/s eta 0:01:20\n", "     --------- ----------------------------- 4.7/19.3 MB 184.4 kB/s eta 0:01:20\n", "     --------- ----------------------------- 4.7/19.3 MB 184.4 kB/s eta 0:01:20\n", "     --------- ----------------------------- 4.7/19.3 MB 184.4 kB/s eta 0:01:20\n", "     --------- ----------------------------- 4.7/19.3 MB 184.4 kB/s eta 0:01:20\n", "     ---------- ---------------------------- 5.0/19.3 MB 183.9 kB/s eta 0:01:19\n", "     ---------- ---------------------------- 5.0/19.3 MB 183.9 kB/s eta 0:01:19\n", "     ---------- ---------------------------- 5.0/19.3 MB 183.9 kB/s eta 0:01:19\n", "     ---------- ---------------------------- 5.0/19.3 MB 183.9 kB/s eta 0:01:19\n", "     ---------- ---------------------------- 5.0/19.3 MB 183.9 kB/s eta 0:01:19\n", "     ---------- ---------------------------- 5.0/19.3 MB 183.9 kB/s eta 0:01:19\n", "     ---------- ---------------------------- 5.0/19.3 MB 183.9 kB/s eta 0:01:19\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ---------- ---------------------------- 5.2/19.3 MB 184.4 kB/s eta 0:01:17\n", "     ----------- --------------------------- 5.5/19.3 MB 143.6 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.5/19.3 MB 143.6 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.5/19.3 MB 143.6 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.5/19.3 MB 143.6 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.5/19.3 MB 143.6 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.5/19.3 MB 143.6 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.5/19.3 MB 143.6 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.8/19.3 MB 141.0 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.8/19.3 MB 141.0 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.8/19.3 MB 141.0 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.8/19.3 MB 141.0 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.8/19.3 MB 141.0 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.8/19.3 MB 141.0 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.8/19.3 MB 141.0 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.8/19.3 MB 141.0 kB/s eta 0:01:37\n", "     ----------- --------------------------- 5.8/19.3 MB 141.0 kB/s eta 0:01:37\n", "     ------------ -------------------------- 6.0/19.3 MB 134.9 kB/s eta 0:01:39\n", "     ------------ -------------------------- 6.0/19.3 MB 134.9 kB/s eta 0:01:39\n", "     ------------ -------------------------- 6.0/19.3 MB 134.9 kB/s eta 0:01:39\n", "     ------------ -------------------------- 6.0/19.3 MB 134.9 kB/s eta 0:01:39\n", "     ------------ -------------------------- 6.0/19.3 MB 134.9 kB/s eta 0:01:39\n", "     ------------ -------------------------- 6.0/19.3 MB 134.9 kB/s eta 0:01:39\n", "     ------------ -------------------------- 6.0/19.3 MB 134.9 kB/s eta 0:01:39\n", "     ------------ -------------------------- 6.3/19.3 MB 134.0 kB/s eta 0:01:38\n", "     ------------ -------------------------- 6.3/19.3 MB 134.0 kB/s eta 0:01:38\n", "     ------------ -------------------------- 6.3/19.3 MB 134.0 kB/s eta 0:01:38\n", "     ------------ -------------------------- 6.3/19.3 MB 134.0 kB/s eta 0:01:38\n", "     ------------ -------------------------- 6.3/19.3 MB 134.0 kB/s eta 0:01:38\n", "     ------------ -------------------------- 6.3/19.3 MB 134.0 kB/s eta 0:01:38\n", "     ------------- ------------------------- 6.6/19.3 MB 134.8 kB/s eta 0:01:35\n", "     ------------- ------------------------- 6.6/19.3 MB 134.8 kB/s eta 0:01:35\n", "     ------------- ------------------------- 6.6/19.3 MB 134.8 kB/s eta 0:01:35\n", "     ------------- ------------------------- 6.6/19.3 MB 134.8 kB/s eta 0:01:35\n", "     ------------- ------------------------- 6.6/19.3 MB 134.8 kB/s eta 0:01:35\n", "     ------------- ------------------------- 6.8/19.3 MB 134.1 kB/s eta 0:01:34\n", "     ------------- ------------------------- 6.8/19.3 MB 134.1 kB/s eta 0:01:34\n", "     ------------- ------------------------- 6.8/19.3 MB 134.1 kB/s eta 0:01:34\n", "     ------------- ------------------------- 6.8/19.3 MB 134.1 kB/s eta 0:01:34\n", "     ------------- ------------------------- 6.8/19.3 MB 134.1 kB/s eta 0:01:34\n", "     -------------- ------------------------ 7.1/19.3 MB 136.7 kB/s eta 0:01:30\n", "     -------------- ------------------------ 7.1/19.3 MB 136.7 kB/s eta 0:01:30\n", "     -------------- ------------------------ 7.1/19.3 MB 136.7 kB/s eta 0:01:30\n", "     -------------- ------------------------ 7.1/19.3 MB 136.7 kB/s eta 0:01:30\n", "     -------------- ------------------------ 7.1/19.3 MB 136.7 kB/s eta 0:01:30\n", "     -------------- ------------------------ 7.1/19.3 MB 136.7 kB/s eta 0:01:30\n", "     -------------- ------------------------ 7.1/19.3 MB 136.7 kB/s eta 0:01:30\n", "     -------------- ------------------------ 7.1/19.3 MB 136.7 kB/s eta 0:01:30\n", "     -------------- ------------------------ 7.3/19.3 MB 137.5 kB/s eta 0:01:28\n", "     -------------- ------------------------ 7.3/19.3 MB 137.5 kB/s eta 0:01:28\n", "     -------------- ------------------------ 7.3/19.3 MB 137.5 kB/s eta 0:01:28\n", "     -------------- ------------------------ 7.3/19.3 MB 137.5 kB/s eta 0:01:28\n", "     --------------- ----------------------- 7.6/19.3 MB 141.8 kB/s eta 0:01:23\n", "     --------------- ----------------------- 7.6/19.3 MB 141.8 kB/s eta 0:01:23\n", "     --------------- ----------------------- 7.6/19.3 MB 141.8 kB/s eta 0:01:23\n", "     --------------- ----------------------- 7.6/19.3 MB 141.8 kB/s eta 0:01:23\n", "     --------------- ----------------------- 7.6/19.3 MB 141.8 kB/s eta 0:01:23\n", "     --------------- ----------------------- 7.9/19.3 MB 150.2 kB/s eta 0:01:17\n", "     --------------- ----------------------- 7.9/19.3 MB 150.2 kB/s eta 0:01:17\n", "     --------------- ----------------------- 7.9/19.3 MB 150.2 kB/s eta 0:01:17\n", "     --------------- ----------------------- 7.9/19.3 MB 150.2 kB/s eta 0:01:17\n", "     --------------- ----------------------- 7.9/19.3 MB 150.2 kB/s eta 0:01:17\n", "     ---------------- ---------------------- 8.1/19.3 MB 153.5 kB/s eta 0:01:14\n", "     ---------------- ---------------------- 8.1/19.3 MB 153.5 kB/s eta 0:01:14\n", "     ---------------- ---------------------- 8.1/19.3 MB 153.5 kB/s eta 0:01:14\n", "     ---------------- ---------------------- 8.1/19.3 MB 153.5 kB/s eta 0:01:14\n", "     ---------------- ---------------------- 8.1/19.3 MB 153.5 kB/s eta 0:01:14\n", "     ---------------- ---------------------- 8.1/19.3 MB 153.5 kB/s eta 0:01:14\n", "     ---------------- ---------------------- 8.4/19.3 MB 158.9 kB/s eta 0:01:09\n", "     ---------------- ---------------------- 8.4/19.3 MB 158.9 kB/s eta 0:01:09\n", "     ---------------- ---------------------- 8.4/19.3 MB 158.9 kB/s eta 0:01:09\n", "     ---------------- ---------------------- 8.4/19.3 MB 158.9 kB/s eta 0:01:09\n", "     ---------------- ---------------------- 8.4/19.3 MB 158.9 kB/s eta 0:01:09\n", "     ----------------- --------------------- 8.7/19.3 MB 162.0 kB/s eta 0:01:06\n", "     ----------------- --------------------- 8.7/19.3 MB 162.0 kB/s eta 0:01:06\n", "     ----------------- --------------------- 8.7/19.3 MB 162.0 kB/s eta 0:01:06\n", "     ----------------- --------------------- 8.7/19.3 MB 162.0 kB/s eta 0:01:06\n", "     ----------------- --------------------- 8.9/19.3 MB 167.8 kB/s eta 0:01:03\n", "     ----------------- --------------------- 8.9/19.3 MB 167.8 kB/s eta 0:01:03\n", "     ----------------- --------------------- 8.9/19.3 MB 167.8 kB/s eta 0:01:03\n", "     ----------------- --------------------- 8.9/19.3 MB 167.8 kB/s eta 0:01:03\n", "     ----------------- --------------------- 8.9/19.3 MB 167.8 kB/s eta 0:01:03\n", "     ------------------ -------------------- 9.2/19.3 MB 171.0 kB/s eta 0:01:00\n", "     ------------------ -------------------- 9.2/19.3 MB 171.0 kB/s eta 0:01:00\n", "     ------------------ -------------------- 9.2/19.3 MB 171.0 kB/s eta 0:01:00\n", "     ------------------ -------------------- 9.2/19.3 MB 171.0 kB/s eta 0:01:00\n", "     ------------------ -------------------- 9.2/19.3 MB 171.0 kB/s eta 0:01:00\n", "     ------------------ -------------------- 9.2/19.3 MB 171.0 kB/s eta 0:01:00\n", "     ------------------- ------------------- 9.4/19.3 MB 176.5 kB/s eta 0:00:57\n", "     ------------------- ------------------- 9.4/19.3 MB 176.5 kB/s eta 0:00:57\n", "     ------------------- ------------------- 9.4/19.3 MB 176.5 kB/s eta 0:00:57\n", "     ------------------- ------------------- 9.4/19.3 MB 176.5 kB/s eta 0:00:57\n", "     ------------------- ------------------- 9.4/19.3 MB 176.5 kB/s eta 0:00:57\n", "     ------------------- ------------------- 9.4/19.3 MB 176.5 kB/s eta 0:00:57\n", "     ------------------- ------------------- 9.7/19.3 MB 177.5 kB/s eta 0:00:55\n", "     ------------------- ------------------- 9.7/19.3 MB 177.5 kB/s eta 0:00:55\n", "     ------------------- ------------------- 9.7/19.3 MB 177.5 kB/s eta 0:00:55\n", "     ------------------- ------------------- 9.7/19.3 MB 177.5 kB/s eta 0:00:55\n", "     ------------------- ------------------- 9.7/19.3 MB 177.5 kB/s eta 0:00:55\n", "     ------------------- ------------------ 10.0/19.3 MB 181.8 kB/s eta 0:00:52\n", "     ------------------- ------------------ 10.0/19.3 MB 181.8 kB/s eta 0:00:52\n", "     ------------------- ------------------ 10.0/19.3 MB 181.8 kB/s eta 0:00:52\n", "     ------------------- ------------------ 10.0/19.3 MB 181.8 kB/s eta 0:00:52\n", "     ------------------- ------------------ 10.0/19.3 MB 181.8 kB/s eta 0:00:52\n", "     -------------------- ----------------- 10.2/19.3 MB 183.9 kB/s eta 0:00:50\n", "     -------------------- ----------------- 10.2/19.3 MB 183.9 kB/s eta 0:00:50\n", "     -------------------- ----------------- 10.2/19.3 MB 183.9 kB/s eta 0:00:50\n", "     -------------------- ----------------- 10.2/19.3 MB 183.9 kB/s eta 0:00:50\n", "     -------------------- ----------------- 10.2/19.3 MB 183.9 kB/s eta 0:00:50\n", "     -------------------- ----------------- 10.2/19.3 MB 183.9 kB/s eta 0:00:50\n", "     -------------------- ----------------- 10.5/19.3 MB 185.4 kB/s eta 0:00:48\n", "     -------------------- ----------------- 10.5/19.3 MB 185.4 kB/s eta 0:00:48\n", "     -------------------- ----------------- 10.5/19.3 MB 185.4 kB/s eta 0:00:48\n", "     -------------------- ----------------- 10.5/19.3 MB 185.4 kB/s eta 0:00:48\n", "     -------------------- ----------------- 10.5/19.3 MB 185.4 kB/s eta 0:00:48\n", "     -------------------- ----------------- 10.5/19.3 MB 185.4 kB/s eta 0:00:48\n", "     -------------------- ----------------- 10.5/19.3 MB 185.4 kB/s eta 0:00:48\n", "     --------------------- ---------------- 10.7/19.3 MB 184.4 kB/s eta 0:00:47\n", "     --------------------- ---------------- 10.7/19.3 MB 184.4 kB/s eta 0:00:47\n", "     --------------------- ---------------- 10.7/19.3 MB 184.4 kB/s eta 0:00:47\n", "     --------------------- ---------------- 10.7/19.3 MB 184.4 kB/s eta 0:00:47\n", "     --------------------- ---------------- 10.7/19.3 MB 184.4 kB/s eta 0:00:47\n", "     --------------------- ---------------- 10.7/19.3 MB 184.4 kB/s eta 0:00:47\n", "     --------------------- ---------------- 10.7/19.3 MB 184.4 kB/s eta 0:00:47\n", "     --------------------- ---------------- 10.7/19.3 MB 184.4 kB/s eta 0:00:47\n", "     --------------------- ---------------- 10.7/19.3 MB 184.4 kB/s eta 0:00:47\n", "     --------------------- ---------------- 11.0/19.3 MB 210.9 kB/s eta 0:00:40\n", "     --------------------- ---------------- 11.0/19.3 MB 210.9 kB/s eta 0:00:40\n", "     --------------------- ---------------- 11.0/19.3 MB 210.9 kB/s eta 0:00:40\n", "     --------------------- ---------------- 11.0/19.3 MB 210.9 kB/s eta 0:00:40\n", "     --------------------- ---------------- 11.0/19.3 MB 210.9 kB/s eta 0:00:40\n", "     --------------------- ---------------- 11.0/19.3 MB 210.9 kB/s eta 0:00:40\n", "     --------------------- ---------------- 11.0/19.3 MB 210.9 kB/s eta 0:00:40\n", "     ---------------------- --------------- 11.3/19.3 MB 209.0 kB/s eta 0:00:39\n", "     ---------------------- --------------- 11.3/19.3 MB 209.0 kB/s eta 0:00:39\n", "     ---------------------- --------------- 11.3/19.3 MB 209.0 kB/s eta 0:00:39\n", "     ---------------------- --------------- 11.3/19.3 MB 209.0 kB/s eta 0:00:39\n", "     ---------------------- --------------- 11.3/19.3 MB 209.0 kB/s eta 0:00:39\n", "     ---------------------- --------------- 11.3/19.3 MB 209.0 kB/s eta 0:00:39\n", "     ---------------------- --------------- 11.5/19.3 MB 208.6 kB/s eta 0:00:38\n", "     ---------------------- --------------- 11.5/19.3 MB 208.6 kB/s eta 0:00:38\n", "     ---------------------- --------------- 11.5/19.3 MB 208.6 kB/s eta 0:00:38\n", "     ---------------------- --------------- 11.5/19.3 MB 208.6 kB/s eta 0:00:38\n", "     ---------------------- --------------- 11.5/19.3 MB 208.6 kB/s eta 0:00:38\n", "     ---------------------- --------------- 11.5/19.3 MB 208.6 kB/s eta 0:00:38\n", "     ----------------------- -------------- 11.8/19.3 MB 209.6 kB/s eta 0:00:36\n", "     ----------------------- -------------- 11.8/19.3 MB 209.6 kB/s eta 0:00:36\n", "     ----------------------- -------------- 11.8/19.3 MB 209.6 kB/s eta 0:00:36\n", "     ----------------------- -------------- 11.8/19.3 MB 209.6 kB/s eta 0:00:36\n", "     ----------------------- -------------- 11.8/19.3 MB 209.6 kB/s eta 0:00:36\n", "     ----------------------- -------------- 11.8/19.3 MB 209.6 kB/s eta 0:00:36\n", "     ----------------------- -------------- 12.1/19.3 MB 213.9 kB/s eta 0:00:35\n", "     ----------------------- -------------- 12.1/19.3 MB 213.9 kB/s eta 0:00:35\n", "     ----------------------- -------------- 12.1/19.3 MB 213.9 kB/s eta 0:00:35\n", "     ----------------------- -------------- 12.1/19.3 MB 213.9 kB/s eta 0:00:35\n", "     ----------------------- -------------- 12.1/19.3 MB 213.9 kB/s eta 0:00:35\n", "     ----------------------- -------------- 12.1/19.3 MB 213.9 kB/s eta 0:00:35\n", "     ----------------------- -------------- 12.1/19.3 MB 213.9 kB/s eta 0:00:35\n", "     ------------------------ ------------- 12.3/19.3 MB 212.0 kB/s eta 0:00:34\n", "     ------------------------ ------------- 12.3/19.3 MB 212.0 kB/s eta 0:00:34\n", "     ------------------------ ------------- 12.3/19.3 MB 212.0 kB/s eta 0:00:34\n", "     ------------------------ ------------- 12.3/19.3 MB 212.0 kB/s eta 0:00:34\n", "     ------------------------ ------------- 12.3/19.3 MB 212.0 kB/s eta 0:00:34\n", "     ------------------------ ------------- 12.3/19.3 MB 212.0 kB/s eta 0:00:34\n", "     ------------------------ ------------- 12.3/19.3 MB 212.0 kB/s eta 0:00:34\n", "     ------------------------ ------------- 12.3/19.3 MB 212.0 kB/s eta 0:00:34\n", "     ------------------------ ------------- 12.3/19.3 MB 212.0 kB/s eta 0:00:34\n", "     ------------------------ ------------- 12.6/19.3 MB 210.4 kB/s eta 0:00:33\n", "     ------------------------ ------------- 12.6/19.3 MB 210.4 kB/s eta 0:00:33\n", "     ------------------------ ------------- 12.6/19.3 MB 210.4 kB/s eta 0:00:33\n", "     ------------------------ ------------- 12.6/19.3 MB 210.4 kB/s eta 0:00:33\n", "     ------------------------ ------------- 12.6/19.3 MB 210.4 kB/s eta 0:00:33\n", "     ------------------------ ------------- 12.6/19.3 MB 210.4 kB/s eta 0:00:33\n", "     ------------------------ ------------- 12.6/19.3 MB 210.4 kB/s eta 0:00:33\n", "     ------------------------- ------------ 12.8/19.3 MB 206.3 kB/s eta 0:00:32\n", "     ------------------------- ------------ 12.8/19.3 MB 206.3 kB/s eta 0:00:32\n", "     ------------------------- ------------ 12.8/19.3 MB 206.3 kB/s eta 0:00:32\n", "     ------------------------- ------------ 12.8/19.3 MB 206.3 kB/s eta 0:00:32\n", "     ------------------------- ------------ 12.8/19.3 MB 206.3 kB/s eta 0:00:32\n", "     ------------------------- ------------ 12.8/19.3 MB 206.3 kB/s eta 0:00:32\n", "     ------------------------- ------------ 12.8/19.3 MB 206.3 kB/s eta 0:00:32\n", "     ------------------------- ------------ 12.8/19.3 MB 206.3 kB/s eta 0:00:32\n", "     ------------------------- ------------ 12.8/19.3 MB 206.3 kB/s eta 0:00:32\n", "     ------------------------- ------------ 12.8/19.3 MB 206.3 kB/s eta 0:00:32\n", "     ------------------------- ------------ 13.1/19.3 MB 200.4 kB/s eta 0:00:32\n", "     ------------------------- ------------ 13.1/19.3 MB 200.4 kB/s eta 0:00:32\n", "     ------------------------- ------------ 13.1/19.3 MB 200.4 kB/s eta 0:00:32\n", "     ------------------------- ------------ 13.1/19.3 MB 200.4 kB/s eta 0:00:32\n", "     ------------------------- ------------ 13.1/19.3 MB 200.4 kB/s eta 0:00:32\n", "     ------------------------- ------------ 13.1/19.3 MB 200.4 kB/s eta 0:00:32\n", "     ------------------------- ------------ 13.1/19.3 MB 200.4 kB/s eta 0:00:32\n", "     ------------------------- ------------ 13.1/19.3 MB 200.4 kB/s eta 0:00:32\n", "     ------------------------- ------------ 13.1/19.3 MB 200.4 kB/s eta 0:00:32\n", "     ------------------------- ------------ 13.1/19.3 MB 200.4 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.4/19.3 MB 193.3 kB/s eta 0:00:31\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     -------------------------- ----------- 13.6/19.3 MB 180.9 kB/s eta 0:00:32\n", "     --------------------------- ---------- 13.9/19.3 MB 169.9 kB/s eta 0:00:33\n", "     --------------------------- ---------- 13.9/19.3 MB 169.9 kB/s eta 0:00:33\n", "     --------------------------- ---------- 13.9/19.3 MB 169.9 kB/s eta 0:00:33\n", "     --------------------------- ---------- 13.9/19.3 MB 169.9 kB/s eta 0:00:33\n", "     --------------------------- ---------- 13.9/19.3 MB 169.9 kB/s eta 0:00:33\n", "     --------------------------- ---------- 13.9/19.3 MB 169.9 kB/s eta 0:00:33\n", "     --------------------------- ---------- 13.9/19.3 MB 169.9 kB/s eta 0:00:33\n", "     --------------------------- ---------- 14.2/19.3 MB 167.5 kB/s eta 0:00:31\n", "     --------------------------- ---------- 14.2/19.3 MB 167.5 kB/s eta 0:00:31\n", "     --------------------------- ---------- 14.2/19.3 MB 167.5 kB/s eta 0:00:31\n", "     --------------------------- ---------- 14.2/19.3 MB 167.5 kB/s eta 0:00:31\n", "     --------------------------- ---------- 14.2/19.3 MB 167.5 kB/s eta 0:00:31\n", "     ---------------------------- --------- 14.4/19.3 MB 169.0 kB/s eta 0:00:30\n", "     ---------------------------- --------- 14.4/19.3 MB 169.0 kB/s eta 0:00:30\n", "     ---------------------------- --------- 14.4/19.3 MB 169.0 kB/s eta 0:00:30\n", "     ---------------------------- --------- 14.4/19.3 MB 169.0 kB/s eta 0:00:30\n", "     ---------------------------- --------- 14.4/19.3 MB 169.0 kB/s eta 0:00:30\n", "     ---------------------------- --------- 14.4/19.3 MB 169.0 kB/s eta 0:00:30\n", "     ---------------------------- --------- 14.4/19.3 MB 169.0 kB/s eta 0:00:30\n", "     ---------------------------- --------- 14.4/19.3 MB 169.0 kB/s eta 0:00:30\n", "     ---------------------------- --------- 14.4/19.3 MB 169.0 kB/s eta 0:00:30\n", "     ---------------------------- --------- 14.7/19.3 MB 162.0 kB/s eta 0:00:29\n", "     ---------------------------- --------- 14.7/19.3 MB 162.0 kB/s eta 0:00:29\n", "     ---------------------------- --------- 14.7/19.3 MB 162.0 kB/s eta 0:00:29\n", "     ---------------------------- --------- 14.7/19.3 MB 162.0 kB/s eta 0:00:29\n", "     ---------------------------- --------- 14.7/19.3 MB 162.0 kB/s eta 0:00:29\n", "     ---------------------------- --------- 14.7/19.3 MB 162.0 kB/s eta 0:00:29\n", "     ---------------------------- --------- 14.7/19.3 MB 162.0 kB/s eta 0:00:29\n", "     ---------------------------- --------- 14.7/19.3 MB 162.0 kB/s eta 0:00:29\n", "     ---------------------------- --------- 14.7/19.3 MB 162.0 kB/s eta 0:00:29\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 14.9/19.3 MB 158.1 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ----------------------------- -------- 15.2/19.3 MB 151.4 kB/s eta 0:00:28\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------ ------- 15.5/19.3 MB 117.0 kB/s eta 0:00:34\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     ------------------------------- ------- 15.7/19.3 MB 91.7 kB/s eta 0:00:40\n", "     -------------------------------- ------ 16.0/19.3 MB 87.9 kB/s eta 0:00:39\n", "     -------------------------------- ------ 16.0/19.3 MB 87.9 kB/s eta 0:00:39\n", "     -------------------------------- ------ 16.0/19.3 MB 87.9 kB/s eta 0:00:39\n", "     -------------------------------- ------ 16.0/19.3 MB 87.9 kB/s eta 0:00:39\n", "     -------------------------------- ------ 16.0/19.3 MB 87.9 kB/s eta 0:00:39\n", "     -------------------------------- ------ 16.0/19.3 MB 87.9 kB/s eta 0:00:39\n", "     -------------------------------- ------ 16.0/19.3 MB 87.9 kB/s eta 0:00:39\n", "     -------------------------------- ------ 16.0/19.3 MB 87.9 kB/s eta 0:00:39\n", "     -------------------------------- ------ 16.0/19.3 MB 87.9 kB/s eta 0:00:39\n", "     -------------------------------- ------ 16.0/19.3 MB 87.9 kB/s eta 0:00:39\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     -------------------------------- ------ 16.3/19.3 MB 88.4 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.5/19.3 MB 80.8 kB/s eta 0:00:35\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     --------------------------------- ----- 16.8/19.3 MB 71.3 kB/s eta 0:00:36\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.0/19.3 MB 62.7 kB/s eta 0:00:37\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ---------------------------------- ---- 17.3/19.3 MB 69.8 kB/s eta 0:00:30\n", "     ----------------------------------- --- 17.6/19.3 MB 77.2 kB/s eta 0:00:24\n", "     ----------------------------------- --- 17.6/19.3 MB 77.2 kB/s eta 0:00:24\n", "     ----------------------------------- --- 17.6/19.3 MB 77.2 kB/s eta 0:00:24\n", "     ----------------------------------- --- 17.6/19.3 MB 77.2 kB/s eta 0:00:24\n", "     ----------------------------------- --- 17.6/19.3 MB 77.2 kB/s eta 0:00:24\n", "     ----------------------------------- --- 17.6/19.3 MB 77.2 kB/s eta 0:00:24\n", "     ----------------------------------- --- 17.6/19.3 MB 77.2 kB/s eta 0:00:24\n", "     ----------------------------------- --- 17.8/19.3 MB 83.7 kB/s eta 0:00:19\n", "     ----------------------------------- --- 17.8/19.3 MB 83.7 kB/s eta 0:00:19\n", "     ----------------------------------- --- 17.8/19.3 MB 83.7 kB/s eta 0:00:19\n", "     ----------------------------------- --- 17.8/19.3 MB 83.7 kB/s eta 0:00:19\n", "     ------------------------------------ -- 18.1/19.3 MB 90.8 kB/s eta 0:00:14\n", "     ------------------------------------ -- 18.1/19.3 MB 90.8 kB/s eta 0:00:14\n", "     ------------------------------------ -- 18.1/19.3 MB 90.8 kB/s eta 0:00:14\n", "     ------------------------------------ -- 18.1/19.3 MB 90.8 kB/s eta 0:00:14\n", "     ------------------------------------ -- 18.1/19.3 MB 90.8 kB/s eta 0:00:14\n", "     ------------------------------------- - 18.4/19.3 MB 97.1 kB/s eta 0:00:11\n", "     ------------------------------------- - 18.4/19.3 MB 97.1 kB/s eta 0:00:11\n", "     ------------------------------------- - 18.4/19.3 MB 97.1 kB/s eta 0:00:11\n", "     ------------------------------------- - 18.4/19.3 MB 97.1 kB/s eta 0:00:11\n", "     ------------------------------------- - 18.4/19.3 MB 97.1 kB/s eta 0:00:11\n", "     ------------------------------------ - 18.6/19.3 MB 102.9 kB/s eta 0:00:08\n", "     ------------------------------------ - 18.6/19.3 MB 102.9 kB/s eta 0:00:08\n", "     ------------------------------------ - 18.6/19.3 MB 102.9 kB/s eta 0:00:08\n", "     ------------------------------------ - 18.6/19.3 MB 102.9 kB/s eta 0:00:08\n", "     ------------------------------------ - 18.6/19.3 MB 102.9 kB/s eta 0:00:08\n", "     -------------------------------------  18.9/19.3 MB 108.1 kB/s eta 0:00:05\n", "     -------------------------------------  18.9/19.3 MB 108.1 kB/s eta 0:00:05\n", "     -------------------------------------  18.9/19.3 MB 108.1 kB/s eta 0:00:05\n", "     -------------------------------------  18.9/19.3 MB 108.1 kB/s eta 0:00:05\n", "     -------------------------------------  19.1/19.3 MB 114.0 kB/s eta 0:00:02\n", "     -------------------------------------  19.1/19.3 MB 114.0 kB/s eta 0:00:02\n", "     -------------------------------------- 19.3/19.3 MB 123.4 kB/s eta 0:00:00\n", "Requirement already satisfied: build>=1.0.3 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=1.0.9->langchain_chroma) (1.2.2.post1)\n", "Requirement already satisfied: pydantic>=1.9 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=1.0.9->langchain_chroma) (2.11.5)\n", "Collecting pybase64>=1.4.1 (from chromadb>=1.0.9->langchain_chroma)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/c0/f5/a7eed9f3692209a9869a28bdd92deddf8cbffb06b40954f89f4577e5c96e/pybase64-1.4.1-cp313-cp313-win_amd64.whl (36 kB)\n", "Requirement already satisfied: uvicorn>=0.18.3 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from uvicorn[standard]>=0.18.3->chromadb>=1.0.9->langchain_chroma) (0.34.2)\n", "Collecting posthog>=2.4.0 (from chromadb>=1.0.9->langchain_chroma)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/ab/ec/7a44533c9fe7046ffcfe48ca0e7472ada2633854f474be633f4afed7b044/posthog-6.0.0-py3-none-any.whl (104 kB)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=1.0.9->langchain_chroma) (4.14.0)\n", "Collecting onnxruntime>=1.14.1 (from chromadb>=1.0.9->langchain_chroma)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/3e/89/2f64e250945fa87140fb917ba377d6d0e9122e029c8512f389a9b7f953f4/onnxruntime-1.22.0-cp313-cp313-win_amd64.whl (12.7 MB)\n", "     ---------------------------------------- 0.0/12.7 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/12.7 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/12.7 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/12.7 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/12.7 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/12.7 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/12.7 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/12.7 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/12.7 MB ? eta -:--:--\n", "     - ------------------------------------- 0.5/12.7 MB 257.7 kB/s eta 0:00:48\n", "     - ------------------------------------- 0.5/12.7 MB 257.7 kB/s eta 0:00:48\n", "     - ------------------------------------- 0.5/12.7 MB 257.7 kB/s eta 0:00:48\n", "     - ------------------------------------- 0.5/12.7 MB 257.7 kB/s eta 0:00:48\n", "     - ------------------------------------- 0.5/12.7 MB 257.7 kB/s eta 0:00:48\n", "     - ------------------------------------- 0.5/12.7 MB 257.7 kB/s eta 0:00:48\n", "     -- ------------------------------------ 0.8/12.7 MB 233.1 kB/s eta 0:00:52\n", "     -- ------------------------------------ 0.8/12.7 MB 233.1 kB/s eta 0:00:52\n", "     -- ------------------------------------ 0.8/12.7 MB 233.1 kB/s eta 0:00:52\n", "     -- ------------------------------------ 0.8/12.7 MB 233.1 kB/s eta 0:00:52\n", "     -- ------------------------------------ 0.8/12.7 MB 233.1 kB/s eta 0:00:52\n", "     --- ----------------------------------- 1.0/12.7 MB 238.4 kB/s eta 0:00:49\n", "     --- ----------------------------------- 1.0/12.7 MB 238.4 kB/s eta 0:00:49\n", "     --- ----------------------------------- 1.0/12.7 MB 238.4 kB/s eta 0:00:49\n", "     --- ----------------------------------- 1.0/12.7 MB 238.4 kB/s eta 0:00:49\n", "     --- ----------------------------------- 1.0/12.7 MB 238.4 kB/s eta 0:00:49\n", "     ---- ---------------------------------- 1.3/12.7 MB 248.8 kB/s eta 0:00:46\n", "     ---- ---------------------------------- 1.3/12.7 MB 248.8 kB/s eta 0:00:46\n", "     ---- ---------------------------------- 1.3/12.7 MB 248.8 kB/s eta 0:00:46\n", "     ---- ---------------------------------- 1.3/12.7 MB 248.8 kB/s eta 0:00:46\n", "     ---- ---------------------------------- 1.3/12.7 MB 248.8 kB/s eta 0:00:46\n", "     ---- ---------------------------------- 1.6/12.7 MB 240.3 kB/s eta 0:00:47\n", "     ---- ---------------------------------- 1.6/12.7 MB 240.3 kB/s eta 0:00:47\n", "     ---- ---------------------------------- 1.6/12.7 MB 240.3 kB/s eta 0:00:47\n", "     ---- ---------------------------------- 1.6/12.7 MB 240.3 kB/s eta 0:00:47\n", "     ---- ---------------------------------- 1.6/12.7 MB 240.3 kB/s eta 0:00:47\n", "     ---- ---------------------------------- 1.6/12.7 MB 240.3 kB/s eta 0:00:47\n", "     ---- ---------------------------------- 1.6/12.7 MB 240.3 kB/s eta 0:00:47\n", "     ----- --------------------------------- 1.8/12.7 MB 227.4 kB/s eta 0:00:48\n", "     ----- --------------------------------- 1.8/12.7 MB 227.4 kB/s eta 0:00:48\n", "     ----- --------------------------------- 1.8/12.7 MB 227.4 kB/s eta 0:00:48\n", "     ----- --------------------------------- 1.8/12.7 MB 227.4 kB/s eta 0:00:48\n", "     ----- --------------------------------- 1.8/12.7 MB 227.4 kB/s eta 0:00:48\n", "     ----- --------------------------------- 1.8/12.7 MB 227.4 kB/s eta 0:00:48\n", "     ------ -------------------------------- 2.1/12.7 MB 224.9 kB/s eta 0:00:48\n", "     ------ -------------------------------- 2.1/12.7 MB 224.9 kB/s eta 0:00:48\n", "     ------ -------------------------------- 2.1/12.7 MB 224.9 kB/s eta 0:00:48\n", "     ------ -------------------------------- 2.1/12.7 MB 224.9 kB/s eta 0:00:48\n", "     ------ -------------------------------- 2.1/12.7 MB 224.9 kB/s eta 0:00:48\n", "     ------ -------------------------------- 2.1/12.7 MB 224.9 kB/s eta 0:00:48\n", "     ------ -------------------------------- 2.1/12.7 MB 224.9 kB/s eta 0:00:48\n", "     ------ -------------------------------- 2.1/12.7 MB 224.9 kB/s eta 0:00:48\n", "     ------ -------------------------------- 2.1/12.7 MB 224.9 kB/s eta 0:00:48\n", "     ------ -------------------------------- 2.1/12.7 MB 224.9 kB/s eta 0:00:48\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     ------- ------------------------------- 2.4/12.7 MB 205.0 kB/s eta 0:00:51\n", "     -------- ------------------------------ 2.6/12.7 MB 181.1 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.6/12.7 MB 181.1 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.6/12.7 MB 181.1 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.6/12.7 MB 181.1 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.6/12.7 MB 181.1 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.6/12.7 MB 181.1 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.6/12.7 MB 181.1 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.6/12.7 MB 181.1 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.6/12.7 MB 181.1 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.9/12.7 MB 177.0 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.9/12.7 MB 177.0 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.9/12.7 MB 177.0 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.9/12.7 MB 177.0 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.9/12.7 MB 177.0 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.9/12.7 MB 177.0 kB/s eta 0:00:56\n", "     -------- ------------------------------ 2.9/12.7 MB 177.0 kB/s eta 0:00:56\n", "     --------- ----------------------------- 3.1/12.7 MB 177.3 kB/s eta 0:00:54\n", "     --------- ----------------------------- 3.1/12.7 MB 177.3 kB/s eta 0:00:54\n", "     --------- ----------------------------- 3.1/12.7 MB 177.3 kB/s eta 0:00:54\n", "     --------- ----------------------------- 3.1/12.7 MB 177.3 kB/s eta 0:00:54\n", "     --------- ----------------------------- 3.1/12.7 MB 177.3 kB/s eta 0:00:54\n", "     --------- ----------------------------- 3.1/12.7 MB 177.3 kB/s eta 0:00:54\n", "     --------- ----------------------------- 3.1/12.7 MB 177.3 kB/s eta 0:00:54\n", "     ---------- ---------------------------- 3.4/12.7 MB 176.3 kB/s eta 0:00:53\n", "     ---------- ---------------------------- 3.4/12.7 MB 176.3 kB/s eta 0:00:53\n", "     ---------- ---------------------------- 3.4/12.7 MB 176.3 kB/s eta 0:00:53\n", "     ---------- ---------------------------- 3.4/12.7 MB 176.3 kB/s eta 0:00:53\n", "     ---------- ---------------------------- 3.4/12.7 MB 176.3 kB/s eta 0:00:53\n", "     ---------- ---------------------------- 3.4/12.7 MB 176.3 kB/s eta 0:00:53\n", "     ---------- ---------------------------- 3.4/12.7 MB 176.3 kB/s eta 0:00:53\n", "     ---------- ---------------------------- 3.4/12.7 MB 176.3 kB/s eta 0:00:53\n", "     ---------- ---------------------------- 3.4/12.7 MB 176.3 kB/s eta 0:00:53\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ----------- --------------------------- 3.7/12.7 MB 173.6 kB/s eta 0:00:52\n", "     ------------ -------------------------- 3.9/12.7 MB 167.0 kB/s eta 0:00:53\n", "     ------------ -------------------------- 3.9/12.7 MB 167.0 kB/s eta 0:00:53\n", "     ------------ -------------------------- 3.9/12.7 MB 167.0 kB/s eta 0:00:53\n", "     ------------ -------------------------- 3.9/12.7 MB 167.0 kB/s eta 0:00:53\n", "     ------------ -------------------------- 3.9/12.7 MB 167.0 kB/s eta 0:00:53\n", "     ------------ -------------------------- 3.9/12.7 MB 167.0 kB/s eta 0:00:53\n", "     ------------ -------------------------- 3.9/12.7 MB 167.0 kB/s eta 0:00:53\n", "     ------------ -------------------------- 3.9/12.7 MB 167.0 kB/s eta 0:00:53\n", "     ------------ -------------------------- 4.2/12.7 MB 165.9 kB/s eta 0:00:52\n", "     ------------ -------------------------- 4.2/12.7 MB 165.9 kB/s eta 0:00:52\n", "     ------------ -------------------------- 4.2/12.7 MB 165.9 kB/s eta 0:00:52\n", "     ------------ -------------------------- 4.2/12.7 MB 165.9 kB/s eta 0:00:52\n", "     ------------ -------------------------- 4.2/12.7 MB 165.9 kB/s eta 0:00:52\n", "     ------------ -------------------------- 4.2/12.7 MB 165.9 kB/s eta 0:00:52\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     ------------- ------------------------- 4.5/12.7 MB 168.6 kB/s eta 0:00:49\n", "     -------------- ------------------------ 4.7/12.7 MB 161.1 kB/s eta 0:00:50\n", "     -------------- ------------------------ 4.7/12.7 MB 161.1 kB/s eta 0:00:50\n", "     -------------- ------------------------ 4.7/12.7 MB 161.1 kB/s eta 0:00:50\n", "     -------------- ------------------------ 4.7/12.7 MB 161.1 kB/s eta 0:00:50\n", "     -------------- ------------------------ 4.7/12.7 MB 161.1 kB/s eta 0:00:50\n", "     -------------- ------------------------ 4.7/12.7 MB 161.1 kB/s eta 0:00:50\n", "     -------------- ------------------------ 4.7/12.7 MB 161.1 kB/s eta 0:00:50\n", "     -------------- ------------------------ 4.7/12.7 MB 161.1 kB/s eta 0:00:50\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     --------------- ----------------------- 5.0/12.7 MB 160.6 kB/s eta 0:00:49\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.2/12.7 MB 142.2 kB/s eta 0:00:53\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ---------------- ---------------------- 5.5/12.7 MB 114.3 kB/s eta 0:01:03\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------ --------------------- 5.8/12.7 MB 98.9 kB/s eta 0:01:10\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.0/12.7 MB 88.3 kB/s eta 0:01:16\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     ------------------- -------------------- 6.3/12.7 MB 71.7 kB/s eta 0:01:30\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     -------------------- ------------------- 6.6/12.7 MB 54.2 kB/s eta 0:01:54\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     --------------------- ------------------ 6.8/12.7 MB 52.8 kB/s eta 0:01:52\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ---------------------- ----------------- 7.1/12.7 MB 55.2 kB/s eta 0:01:42\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.3/12.7 MB 60.2 kB/s eta 0:01:29\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ----------------------- ---------------- 7.6/12.7 MB 63.1 kB/s eta 0:01:21\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------ --------------- 7.9/12.7 MB 62.0 kB/s eta 0:01:18\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     ------------------------- -------------- 8.1/12.7 MB 61.8 kB/s eta 0:01:14\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     -------------------------- ------------- 8.4/12.7 MB 61.4 kB/s eta 0:01:11\n", "     --------------------------- ------------ 8.7/12.7 MB 63.0 kB/s eta 0:01:05\n", "     --------------------------- ------------ 8.7/12.7 MB 63.0 kB/s eta 0:01:05\n", "     --------------------------- ------------ 8.7/12.7 MB 63.0 kB/s eta 0:01:05\n", "     --------------------------- ------------ 8.7/12.7 MB 63.0 kB/s eta 0:01:05\n", "     --------------------------- ------------ 8.7/12.7 MB 63.0 kB/s eta 0:01:05\n", "     --------------------------- ------------ 8.7/12.7 MB 63.0 kB/s eta 0:01:05\n", "     ---------------------------- ----------- 8.9/12.7 MB 69.0 kB/s eta 0:00:55\n", "     ---------------------------- ----------- 8.9/12.7 MB 69.0 kB/s eta 0:00:55\n", "     ---------------------------- ----------- 8.9/12.7 MB 69.0 kB/s eta 0:00:55\n", "     ---------------------------- ----------- 8.9/12.7 MB 69.0 kB/s eta 0:00:55\n", "     ---------------------------- ----------- 8.9/12.7 MB 69.0 kB/s eta 0:00:55\n", "     ---------------------------- ----------- 8.9/12.7 MB 69.0 kB/s eta 0:00:55\n", "     ---------------------------- ----------- 8.9/12.7 MB 69.0 kB/s eta 0:00:55\n", "     ---------------------------- ----------- 8.9/12.7 MB 69.0 kB/s eta 0:00:55\n", "     ---------------------------- ----------- 9.2/12.7 MB 74.3 kB/s eta 0:00:48\n", "     ---------------------------- ----------- 9.2/12.7 MB 74.3 kB/s eta 0:00:48\n", "     ---------------------------- ----------- 9.2/12.7 MB 74.3 kB/s eta 0:00:48\n", "     ---------------------------- ----------- 9.2/12.7 MB 74.3 kB/s eta 0:00:48\n", "     ---------------------------- ----------- 9.2/12.7 MB 74.3 kB/s eta 0:00:48\n", "     ---------------------------- ----------- 9.2/12.7 MB 74.3 kB/s eta 0:00:48\n", "     ---------------------------- ----------- 9.2/12.7 MB 74.3 kB/s eta 0:00:48\n", "     ----------------------------- ---------- 9.4/12.7 MB 79.4 kB/s eta 0:00:41\n", "     ----------------------------- ---------- 9.4/12.7 MB 79.4 kB/s eta 0:00:41\n", "     ----------------------------- ---------- 9.4/12.7 MB 79.4 kB/s eta 0:00:41\n", "     ----------------------------- ---------- 9.4/12.7 MB 79.4 kB/s eta 0:00:41\n", "     ------------------------------ --------- 9.7/12.7 MB 87.5 kB/s eta 0:00:35\n", "     ------------------------------ --------- 9.7/12.7 MB 87.5 kB/s eta 0:00:35\n", "     ------------------------------ --------- 9.7/12.7 MB 87.5 kB/s eta 0:00:35\n", "     ------------------------------ --------- 9.7/12.7 MB 87.5 kB/s eta 0:00:35\n", "     ------------------------------ --------- 9.7/12.7 MB 87.5 kB/s eta 0:00:35\n", "     ------------------------------ --------- 9.7/12.7 MB 87.5 kB/s eta 0:00:35\n", "     ------------------------------ --------- 9.7/12.7 MB 87.5 kB/s eta 0:00:35\n", "     ------------------------------ --------- 9.7/12.7 MB 87.5 kB/s eta 0:00:35\n", "     ------------------------------ --------- 9.7/12.7 MB 87.5 kB/s eta 0:00:35\n", "     ------------------------------ -------- 10.0/12.7 MB 91.2 kB/s eta 0:00:30\n", "     ------------------------------ -------- 10.0/12.7 MB 91.2 kB/s eta 0:00:30\n", "     ------------------------------ -------- 10.0/12.7 MB 91.2 kB/s eta 0:00:30\n", "     ------------------------------ -------- 10.0/12.7 MB 91.2 kB/s eta 0:00:30\n", "     ------------------------------ -------- 10.0/12.7 MB 91.2 kB/s eta 0:00:30\n", "     ------------------------------ -------- 10.0/12.7 MB 91.2 kB/s eta 0:00:30\n", "     ------------------------------ -------- 10.0/12.7 MB 91.2 kB/s eta 0:00:30\n", "     ------------------------------ -------- 10.0/12.7 MB 91.2 kB/s eta 0:00:30\n", "     ------------------------------ -------- 10.0/12.7 MB 91.2 kB/s eta 0:00:30\n", "     ------------------------------- ------- 10.2/12.7 MB 94.2 kB/s eta 0:00:27\n", "     ------------------------------- ------- 10.2/12.7 MB 94.2 kB/s eta 0:00:27\n", "     ------------------------------- ------- 10.2/12.7 MB 94.2 kB/s eta 0:00:27\n", "     ------------------------------- ------- 10.2/12.7 MB 94.2 kB/s eta 0:00:27\n", "     ------------------------------- ------- 10.2/12.7 MB 94.2 kB/s eta 0:00:27\n", "     ------------------------------- ------- 10.2/12.7 MB 94.2 kB/s eta 0:00:27\n", "     -------------------------------- ------ 10.5/12.7 MB 99.2 kB/s eta 0:00:23\n", "     -------------------------------- ------ 10.5/12.7 MB 99.2 kB/s eta 0:00:23\n", "     -------------------------------- ------ 10.5/12.7 MB 99.2 kB/s eta 0:00:23\n", "     -------------------------------- ------ 10.5/12.7 MB 99.2 kB/s eta 0:00:23\n", "     -------------------------------- ------ 10.5/12.7 MB 99.2 kB/s eta 0:00:23\n", "     -------------------------------- ------ 10.5/12.7 MB 99.2 kB/s eta 0:00:23\n", "     -------------------------------- ------ 10.5/12.7 MB 99.2 kB/s eta 0:00:23\n", "     -------------------------------- ------ 10.5/12.7 MB 99.2 kB/s eta 0:00:23\n", "     -------------------------------- ----- 10.7/12.7 MB 113.7 kB/s eta 0:00:18\n", "     -------------------------------- ----- 10.7/12.7 MB 113.7 kB/s eta 0:00:18\n", "     -------------------------------- ----- 10.7/12.7 MB 113.7 kB/s eta 0:00:18\n", "     -------------------------------- ----- 10.7/12.7 MB 113.7 kB/s eta 0:00:18\n", "     -------------------------------- ----- 10.7/12.7 MB 113.7 kB/s eta 0:00:18\n", "     -------------------------------- ----- 10.7/12.7 MB 113.7 kB/s eta 0:00:18\n", "     -------------------------------- ----- 10.7/12.7 MB 113.7 kB/s eta 0:00:18\n", "     -------------------------------- ----- 11.0/12.7 MB 116.7 kB/s eta 0:00:15\n", "     -------------------------------- ----- 11.0/12.7 MB 116.7 kB/s eta 0:00:15\n", "     -------------------------------- ----- 11.0/12.7 MB 116.7 kB/s eta 0:00:15\n", "     -------------------------------- ----- 11.0/12.7 MB 116.7 kB/s eta 0:00:15\n", "     -------------------------------- ----- 11.0/12.7 MB 116.7 kB/s eta 0:00:15\n", "     -------------------------------- ----- 11.0/12.7 MB 116.7 kB/s eta 0:00:15\n", "     -------------------------------- ----- 11.0/12.7 MB 116.7 kB/s eta 0:00:15\n", "     -------------------------------- ----- 11.0/12.7 MB 116.7 kB/s eta 0:00:15\n", "     --------------------------------- ---- 11.3/12.7 MB 119.2 kB/s eta 0:00:12\n", "     --------------------------------- ---- 11.3/12.7 MB 119.2 kB/s eta 0:00:12\n", "     --------------------------------- ---- 11.3/12.7 MB 119.2 kB/s eta 0:00:12\n", "     --------------------------------- ---- 11.3/12.7 MB 119.2 kB/s eta 0:00:12\n", "     --------------------------------- ---- 11.3/12.7 MB 119.2 kB/s eta 0:00:12\n", "     --------------------------------- ---- 11.3/12.7 MB 119.2 kB/s eta 0:00:12\n", "     --------------------------------- ---- 11.3/12.7 MB 119.2 kB/s eta 0:00:12\n", "     --------------------------------- ---- 11.3/12.7 MB 119.2 kB/s eta 0:00:12\n", "     --------------------------------- ---- 11.3/12.7 MB 119.2 kB/s eta 0:00:12\n", "     --------------------------------- ---- 11.3/12.7 MB 119.2 kB/s eta 0:00:12\n", "     ---------------------------------- --- 11.5/12.7 MB 136.3 kB/s eta 0:00:09\n", "     ---------------------------------- --- 11.5/12.7 MB 136.3 kB/s eta 0:00:09\n", "     ---------------------------------- --- 11.5/12.7 MB 136.3 kB/s eta 0:00:09\n", "     ---------------------------------- --- 11.5/12.7 MB 136.3 kB/s eta 0:00:09\n", "     ---------------------------------- --- 11.5/12.7 MB 136.3 kB/s eta 0:00:09\n", "     ---------------------------------- --- 11.5/12.7 MB 136.3 kB/s eta 0:00:09\n", "     ---------------------------------- --- 11.5/12.7 MB 136.3 kB/s eta 0:00:09\n", "     ----------------------------------- -- 11.8/12.7 MB 137.9 kB/s eta 0:00:07\n", "     ----------------------------------- -- 11.8/12.7 MB 137.9 kB/s eta 0:00:07\n", "     ----------------------------------- -- 11.8/12.7 MB 137.9 kB/s eta 0:00:07\n", "     ----------------------------------- -- 11.8/12.7 MB 137.9 kB/s eta 0:00:07\n", "     ----------------------------------- -- 11.8/12.7 MB 137.9 kB/s eta 0:00:07\n", "     ----------------------------------- -- 11.8/12.7 MB 137.9 kB/s eta 0:00:07\n", "     ------------------------------------ - 12.1/12.7 MB 141.0 kB/s eta 0:00:05\n", "     ------------------------------------ - 12.1/12.7 MB 141.0 kB/s eta 0:00:05\n", "     ------------------------------------ - 12.1/12.7 MB 141.0 kB/s eta 0:00:05\n", "     ------------------------------------ - 12.1/12.7 MB 141.0 kB/s eta 0:00:05\n", "     ------------------------------------ - 12.1/12.7 MB 141.0 kB/s eta 0:00:05\n", "     ------------------------------------ - 12.1/12.7 MB 141.0 kB/s eta 0:00:05\n", "     ------------------------------------ - 12.1/12.7 MB 141.0 kB/s eta 0:00:05\n", "     ------------------------------------ - 12.3/12.7 MB 143.4 kB/s eta 0:00:03\n", "     ------------------------------------ - 12.3/12.7 MB 143.4 kB/s eta 0:00:03\n", "     ------------------------------------ - 12.3/12.7 MB 143.4 kB/s eta 0:00:03\n", "     ------------------------------------ - 12.3/12.7 MB 143.4 kB/s eta 0:00:03\n", "     ------------------------------------ - 12.3/12.7 MB 143.4 kB/s eta 0:00:03\n", "     ------------------------------------ - 12.3/12.7 MB 143.4 kB/s eta 0:00:03\n", "     -------------------------------------  12.6/12.7 MB 160.1 kB/s eta 0:00:01\n", "     -------------------------------------  12.6/12.7 MB 160.1 kB/s eta 0:00:01\n", "     -------------------------------------  12.6/12.7 MB 160.1 kB/s eta 0:00:01\n", "     -------------------------------------  12.6/12.7 MB 160.1 kB/s eta 0:00:01\n", "     -------------------------------------  12.6/12.7 MB 160.1 kB/s eta 0:00:01\n", "     -------------------------------------- 12.7/12.7 MB 158.8 kB/s eta 0:00:00\n", "Collecting opentelemetry-api>=1.2.0 (from chromadb>=1.0.9->langchain_chroma)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a5/3a/2ba85557e8dc024c0842ad22c570418dc02c36cbd1ab4b832a93edf071b8/opentelemetry_api-1.34.1-py3-none-any.whl (65 kB)\n", "Collecting opentelemetry-exporter-otlp-proto-grpc>=1.2.0 (from chromadb>=1.0.9->langchain_chroma)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/b4/42/0a4dd47e7ef54edf670c81fc06a83d68ea42727b82126a1df9dd0477695d/opentelemetry_exporter_otlp_proto_grpc-1.34.1-py3-none-any.whl (18 kB)\n", "Collecting opentelemetry-sdk>=1.2.0 (from chromadb>=1.0.9->langchain_chroma)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/07/1b/def4fe6aa73f483cabf4c748f4c25070d5f7604dcc8b52e962983491b29e/opentelemetry_sdk-1.34.1-py3-none-any.whl (118 kB)\n", "Collecting tokenizers>=0.13.2 (from chromadb>=1.0.9->langchain_chroma)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/13/c3/cc2755ee10be859c4338c962a35b9a663788c0c0b50c0bdd8078fb6870cf/tokenizers-0.21.2-cp39-abi3-win_amd64.whl (2.5 MB)\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     -------- ------------------------------- 0.5/2.5 MB 123.6 kB/s eta 0:00:17\n", "     -------- ------------------------------- 0.5/2.5 MB 123.6 kB/s eta 0:00:17\n", "     -------- ------------------------------- 0.5/2.5 MB 123.6 kB/s eta 0:00:17\n", "     -------- ------------------------------- 0.5/2.5 MB 123.6 kB/s eta 0:00:17\n", "     -------- ------------------------------- 0.5/2.5 MB 123.6 kB/s eta 0:00:17\n", "     -------- ------------------------------- 0.5/2.5 MB 123.6 kB/s eta 0:00:17\n", "     -------- ------------------------------- 0.5/2.5 MB 123.6 kB/s eta 0:00:17\n", "     -------- ------------------------------- 0.5/2.5 MB 123.6 kB/s eta 0:00:17\n", "     -------- ------------------------------- 0.5/2.5 MB 123.6 kB/s eta 0:00:17\n", "     ------------ --------------------------- 0.8/2.5 MB 132.8 kB/s eta 0:00:13\n", "     ------------ --------------------------- 0.8/2.5 MB 132.8 kB/s eta 0:00:13\n", "     ------------ --------------------------- 0.8/2.5 MB 132.8 kB/s eta 0:00:13\n", "     ------------ --------------------------- 0.8/2.5 MB 132.8 kB/s eta 0:00:13\n", "     ------------ --------------------------- 0.8/2.5 MB 132.8 kB/s eta 0:00:13\n", "     ------------ --------------------------- 0.8/2.5 MB 132.8 kB/s eta 0:00:13\n", "     ------------ --------------------------- 0.8/2.5 MB 132.8 kB/s eta 0:00:13\n", "     ------------ --------------------------- 0.8/2.5 MB 132.8 kB/s eta 0:00:13\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     ---------------- ----------------------- 1.0/2.5 MB 136.4 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     -------------------- ------------------- 1.3/2.5 MB 110.9 kB/s eta 0:00:11\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ------------------------- -------------- 1.6/2.5 MB 63.8 kB/s eta 0:00:15\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n", "     ----------------------------- ---------- 1.8/2.5 MB 37.0 kB/s eta 0:00:19\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  WARNING: Connection timed out while downloading.\n", "ERROR: Could not install packages due to an OSError: [WinError 32] 另一个程序正在使用此文件，进程无法访问。: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\pip-unpack-61xrkwzl\\\\tokenizers-0.21.2-cp39-abi3-win_amd64.whl'\n", "Consider using the `--user` option or check the permissions.\n", "\n"]}], "source": ["! pip install langchain_chroma"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "import os\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key = \"sk-khjeakixzbxqaraibcdenuhfrlostygebnqkwvyrtbrxhwmu\",\n", "    base_url = \"https://api.siliconflow.cn/v1\",\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["USER_AGENT environment variable not set, consider setting it to identify your requests.\n"]}], "source": ["# Build a sample vectorDB\n", "from langchain_chroma import Chroma\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "# Load blog post\n", "loader = WebBaseLoader(\"https://python.langchain.com/docs/how_to/MultiQueryRetriever/\")\n", "data = loader.load()\n", "\n", "# Split\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=0)\n", "splits = text_splitter.split_documents(data)\n", "\n", "# VectorDB\n", "vectordb = Chroma.from_documents(documents=splits, embedding=embeddings_model)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langchain.retrievers.multi_query import MultiQueryRetriever\n", "from langchain_deepseek import ChatDeepSeek\n", "import os\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "question = \"如何让用户查询更准确？\"\n", "retriever_from_llm = MultiQueryRetriever.fro m_llm(\n", "    retriever=vectordb.as_retriever(), llm=llm\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Set logging for the queries\n", "import logging\n", "\n", "logging.basicConfig()\n", "logging.getLogger(\"langchain.retrievers.multi_query\").setLevel(logging.INFO)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:langchain.retrievers.multi_query:Generated queries: ['1. 有哪些方法可以提高用户查询的精准度？  ', '2. 如何优化用户查询以获得更准确的结果？  ', '3. 在用户查询过程中，有哪些策略可以减少误差并提高准确性？']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[Document(id='8bd0d17d-6163-4531-b1e1-ea38ea2fb1ae', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó Lang<PERSON>hain'}, page_content='to use the MultiQueryRetriever'), Document(id='3fc6770c-56e4-4a25-8776-1032a76fa1ec', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó LangChain'}, page_content='OpenAIEmbeddings()vectordb = Chroma.from_documents(documents=splits, embedding=embedding)API Reference:WebBaseLoader | OpenAIEmbeddings | RecursiveCharacterTextSplitter'), Document(id='6bc31d43-6505-4d32-a3b3-65543849fdf2', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó LangChain'}, page_content='LineListOutputParser()QUERY_PROMPT = PromptTemplate(    input_variables=[\"question\"],    template=\"\"\"You are an AI language model assistant. Your task is to generate five     different versions of the given user question to retrieve relevant documents from a vector     database. By generating multiple perspectives on the user question, your goal is to help    the user overcome some of the limitations of the distance-based similarity search.     Provide these alternative questions separated by'), Document(id='b02b567d-3bd7-495b-9a94-8ce3c837bc8e', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó LangChain'}, page_content=\"analysisHow to add values to a chain's stateHow to construct filters for query analysisHow to configure runtime chain internalsHow deal with high cardinality categoricals when doing query analysisCustom Document LoaderHow to use the MultiQueryRetrieverHow to add scores to retriever resultsCachingHow to use callbacks in async environmentsHow to attach callbacks to a runnableHow to propagate callbacks  constructorHow to dispatch custom callback eventsHow to pass callbacks in at runtimeHow to\"), Document(id='5a3c680b-de82-4fd9-8edf-a6918fcd263f', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó LangChain'}, page_content='through iterative refinementHow to summarize text in a single LLM callHow to use toolkitsHow to add ad-hoc tool calling capability to LLMs and Chat ModelsBuild an Agent with AgentExecutor (Legacy)How to construct knowledge graphsHow to partially format prompt templatesHow to handle multiple queries when doing query analysisHow to use built-in tools and toolkitsHow to pass through arguments from one step to the nextHow to compose prompts togetherHow to handle multiple retrievers when doing query'), Document(id='7f341e52-ecc4-4328-8d6a-bb21a8e2ec08', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó LangChain'}, page_content='runnablesHow to save and load LangChain objectsHow to split text by tokensHow to split HTMLHow to do question answering over CSVsHow to deal with large databases when doing SQL question-answeringHow to better prompt when doing SQL question-answeringHow to do query validation as part of SQL question-answeringHow to stream runnablesHow to stream responses from an LLMHow to use a time-weighted vector store retrieverHow to return artifacts from a toolHow to use chat models to call toolsHow to'), Document(id='d99ea786-482b-445c-8d3c-e08ffe4a1482', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó LangChain'}, page_content='when doing extractionHow to handle long text when doing extractionHow to use prompting alone (no tool calling) to do extractionHow to add fallbacks to a runnableHow to filter messagesHybrid SearchHow to use the LangChain indexing APIHow to inspect runnablesLangChain Expression Language CheatsheetHow to cache LLM responsesHow to track token usage for LLMsRun models locallyHow to get log probabilitiesHow to reorder retrieved results to mitigate the \"lost in the middle\" effectHow to split Markdown'), Document(id='bde71a57-89bf-44ba-b8a2-ed4af28bfa8d', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó LangChain'}, page_content='the Parent Document RetrieverHow to use LangChain with different Pydantic versionsHow to add chat historyHow to get a RAG application to add citationsHow to do per-user retrievalHow to get your RAG application to return sourcesHow to stream results from your RAG applicationHow to split JSON dataHow to recursively split text by charactersResponse metadataHow to pass runtime secrets to runnablesHow to do \"self-querying\" retrievalHow to split text based on semantic similarityHow to chain'), Document(id='679e0c48-4504-4320-b1c4-2d44c79caa1c', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó LangChain'}, page_content='# Build a sample vectorDBfrom langchain_chroma import Chromafrom langchain_community.document_loaders import WebBaseLoaderfrom langchain_openai import OpenAIEmbeddingsfrom langchain_text_splitters import RecursiveCharacterTextSplitter# Load blog postloader = WebBaseLoader(\"https://lilianweng.github.io/posts/2023-06-23-agent/\")data = loader.load()# Splittext_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=0)splits = text_splitter.split_documents(data)# VectorDBembedding ='), Document(id='c151cd27-4272-4889-ba17-9e2727edb2bd', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó LangChain'}, page_content='9Edit this pageWas this page helpful?PreviousCustom Document LoaderNextHow to add scores to retriever resultsCommunityTwitterGitHubOrganizationPythonJS/TSMoreHomepageBlogYouTubeCopyright ¬© 2025 LangChain, Inc.'), Document(id='47b442b9-5650-4f2e-a4a0-58d887595188', metadata={'description': 'Distance-based vector database retrieval embeds (represents) queries in high-dimensional space and finds similar embedded documents based on a distance metric. But, retrieval may produce different results with subtle changes in query wording, or if the embeddings do not capture the semantics of the data well. Prompt engineering / tuning is sometimes done to manually address these problems, but can be tedious.', 'language': 'en', 'source': 'https://python.langchain.com/docs/how_to/MultiQueryRetriever/', 'title': 'How to use the MultiQueryRetriever | \\uf8ffü¶úÔ∏è\\uf8ffüîó LangChain'}, page_content='by HeadersHow to merge consecutive messages of the same typeHow to add message historyHow to migrate from legacy LangChain agents to LangGraphHow to retrieve using multiple vectors per documentHow to pass multimodal data directly to modelsHow to use multimodal promptsHow to create a custom Output ParserHow to use the output-fixing parserHow to parse JSON outputHow to retry when a parsing error occursHow to parse text from message objectsHow to parse XML outputHow to parse YAML outputHow to use')]\n"]}, {"data": {"text/plain": ["11"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["unique_docs = retriever_from_llm.invoke(question)\n", "print(unique_docs)\n", "len(unique_docs)"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}