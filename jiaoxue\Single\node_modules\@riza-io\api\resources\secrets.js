"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecretsSecretsPagination = exports.Secrets = void 0;
const resource_1 = require("../resource.js");
const core_1 = require("../core.js");
const pagination_1 = require("../pagination.js");
class Secrets extends resource_1.APIResource {
    /**
     * Create a secret in your project.
     */
    create(body, options) {
        return this._client.post('/v1/secrets', { body, ...options });
    }
    list(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/secrets', SecretsSecretsPagination, { query, ...options });
    }
}
exports.Secrets = Secrets;
class SecretsSecretsPagination extends pagination_1.SecretsPagination {
}
exports.SecretsSecretsPagination = SecretsSecretsPagination;
Secrets.SecretsSecretsPagination = SecretsSecretsPagination;
//# sourceMappingURL=secrets.js.map