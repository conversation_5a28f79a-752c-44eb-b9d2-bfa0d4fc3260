{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## langgraph - 提示词助手\n", "*****\n", "- 根据用户输入\n", "- 生成提示词并优化"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 收集用户需求\n", "***"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from langchain_core.messages import SystemMessage\n", "from langchain_deepseek import ChatDeepSeek\n", "import os\n", "\n", "from pydantic import BaseModel\n", "\n", "template = \"\"\"你的工作是从用户那里获取有关他们想要创建的提示模板类型的信息。\n", "\n", "你应该从他们那里获取以下信息：\n", "\n", "- 提示词的目标是什么\n", "- 哪些变量将被传递到提示词模板中\n", "- 输出不应该做什么的任何约束\n", "- 输出必须遵守的任何要求\n", "\n", "如果你无法辨别这些信息，请要求他们澄清！不要尝试胡乱猜测。\n", "\n", "在你能够辨别所有信息后，调用相关工具。\"\"\"\n", "\n", "\n", "def get_messages_info(messages):\n", "    return [SystemMessage(content=template)] + messages\n", "\n", "\n", "class PromptInstructions(BaseModel):\n", "    \"\"\"关于如何提示LLM的指令。\"\"\"\n", "\n", "    objective: str  # 目标\n", "    variables: List[str]  # 变量\n", "    constraints: List[str]  # 约束\n", "    requirements: List[str]  # 要求\n", "\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "llm_with_tool = llm.bind_tools([PromptInstructions])\n", "\n", "\n", "def info_chain(state):\n", "    messages = get_messages_info(state[\"messages\"])\n", "    response = llm_with_tool.invoke(messages)\n", "    return {\"messages\": [response]}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["生成提示词"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import AIMessage, HumanMessage, ToolMessage\n", "\n", "# 新系统提示\n", "prompt_system = \"\"\"根据以下要求，编写一个好的提示词模板：\n", "\n", "{reqs}\"\"\"\n", "\n", "\n", "# 获取提示消息的函数\n", "# 只会获取工具调用之后的消息\n", "def get_prompt_messages(messages: list):\n", "    tool_call = None\n", "    other_msgs = []\n", "    for m in messages:\n", "        if isinstance(m, AIMessage) and m.tool_calls:\n", "            tool_call = m.tool_calls[0][\"args\"]\n", "        elif isinstance(m, ToolMessage):\n", "            continue\n", "        elif tool_call is not None:\n", "            other_msgs.append(m)\n", "    return [SystemMessage(content=prompt_system.format(reqs=tool_call))] + other_msgs\n", "\n", "\n", "def prompt_gen_chain(state):\n", "    messages = get_prompt_messages(state[\"messages\"])\n", "    response = llm.invoke(messages)\n", "    return {\"messages\": [response]}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义状态逻辑"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "\n", "from langgraph.graph import END\n", "\n", "\n", "def get_state(state):\n", "    messages = state[\"messages\"]\n", "    if isinstance(messages[-1], AIMessage) and messages[-1].tool_calls:\n", "        return \"add_tool_message\"\n", "    elif not isinstance(messages[-1], HumanMessage):\n", "        return END\n", "    return \"info\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建工作流"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.graph import StateGraph, START\n", "from langgraph.graph.message import add_messages\n", "from typing import Annotated\n", "from typing_extensions import TypedDict\n", "\n", "\n", "class State(TypedDict):\n", "    messages: Annotated[list, add_messages]\n", "\n", "\n", "memory = MemorySaver()\n", "workflow = StateGraph(State)\n", "workflow.add_node(\"info\", info_chain)\n", "workflow.add_node(\"prompt\", prompt_gen_chain)\n", "\n", "\n", "@workflow.add_node\n", "def add_tool_message(state: State):\n", "    return {\n", "        \"messages\": [\n", "            ToolMessage(\n", "                content=\"提示词创建成功!\",\n", "                tool_call_id=state[\"messages\"][-1].tool_calls[0][\"id\"],\n", "            )\n", "        ]\n", "    }\n", "\n", "\n", "workflow.add_conditional_edges(\"info\", get_state, [\"add_tool_message\", \"info\", END])\n", "workflow.add_edge(\"add_tool_message\", \"prompt\")\n", "workflow.add_edge(\"prompt\", END)\n", "workflow.add_edge(START, \"info\")\n", "graph = workflow.compile(checkpointer=memory)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["测试"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["User (q/Q to quit): q\n", "AI: <PERSON><PERSON><PERSON>\n"]}], "source": ["import uuid\n", "\n", "cached_human_responses = [\"hi!\", \"rag prompt\", \"1 rag, 2 none, 3 no, 4 no\", \"red\", \"q\"]\n", "cached_response_index = 0\n", "config = {\"configurable\": {\"thread_id\": str(uuid.uuid4())}}\n", "while True:\n", "    try:\n", "        user = input(\"User (q/Q to quit): \")\n", "    except:\n", "        user = cached_human_responses[cached_response_index]\n", "        cached_response_index += 1\n", "    print(f\"User (q/Q to quit): {user}\")\n", "    if user in {\"q\", \"Q\"}:\n", "        print(\"AI: <PERSON><PERSON><PERSON>\")\n", "        break\n", "    output = None\n", "    for output in graph.stream(\n", "        {\"messages\": [HumanMessage(content=user)]}, config=config, stream_mode=\"updates\"\n", "    ):\n", "        last_message = next(iter(output.values()))[\"messages\"][-1]\n", "        last_message.pretty_print()\n", "\n", "    if output and \"prompt\" in output:\n", "        print(\"Done!\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}