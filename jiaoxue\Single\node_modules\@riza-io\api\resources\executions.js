"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExecutionsDefaultPagination = exports.Executions = void 0;
const resource_1 = require("../resource.js");
const core_1 = require("../core.js");
const pagination_1 = require("../pagination.js");
class Executions extends resource_1.APIResource {
    list(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/executions', ExecutionsDefaultPagination, { query, ...options });
    }
    /**
     * Retrieves an execution.
     */
    get(id, options) {
        return this._client.get(`/v1/executions/${id}`, options);
    }
}
exports.Executions = Executions;
class ExecutionsDefaultPagination extends pagination_1.DefaultPagination {
}
exports.ExecutionsDefaultPagination = ExecutionsDefaultPagination;
Executions.ExecutionsDefaultPagination = ExecutionsDefaultPagination;
//# sourceMappingURL=executions.js.map