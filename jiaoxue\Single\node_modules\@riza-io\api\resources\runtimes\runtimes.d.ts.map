{"version": 3, "file": "runtimes.d.ts", "sourceRoot": "", "sources": ["../../src/resources/runtimes/runtimes.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAE7C,OAAO,KAAK,IAAI,MAAM,YAAY,CAAC;AACnC,OAAO,KAAK,YAAY,MAAM,aAAa,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,oBAAoB,EAAE,oBAAoB,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAC9F,OAAO,EAAE,kBAAkB,EAAE,KAAK,wBAAwB,EAAE,MAAM,kBAAkB,CAAC;AAErF,qBAAa,QAAS,SAAQ,WAAW;IACvC,SAAS,EAAE,YAAY,CAAC,SAAS,CAA4C;IAE7E;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IAI1F;;OAEG;IACH,IAAI,CACF,KAAK,CAAC,EAAE,iBAAiB,EACzB,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAC5B,IAAI,CAAC,WAAW,CAAC,0BAA0B,EAAE,OAAO,CAAC;IACxD,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,0BAA0B,EAAE,OAAO,CAAC;IAW1F;;OAEG;IACH,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC;IAIzF;;OAEG;IACH,GAAG,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;CAGzE;AAED,qBAAa,0BAA2B,SAAQ,kBAAkB,CAAC,OAAO,CAAC;CAAG;AAE9E,MAAM,WAAW,OAAO;IACtB,EAAE,EAAE,MAAM,CAAC;IAEX,MAAM,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;IAElC,QAAQ,EAAE,QAAQ,GAAG,YAAY,CAAC;IAElC,IAAI,EAAE,MAAM,CAAC;IAEb,WAAW,EAAE,MAAM,CAAC;IAEpB,MAAM,EAAE,SAAS,GAAG,UAAU,GAAG,WAAW,GAAG,QAAQ,GAAG,WAAW,CAAC;IAEtE,yBAAyB,CAAC,EAAE,MAAM,CAAC;IAEnC,aAAa,CAAC,EAAE,OAAO,CAAC,YAAY,CAAC;CACtC;AAED,yBAAiB,OAAO,CAAC;IACvB,UAAiB,YAAY;QAC3B,QAAQ,EAAE,MAAM,CAAC;QAEjB,IAAI,EAAE,kBAAkB,GAAG,cAAc,CAAC;KAC3C;CACF;AAED,MAAM,WAAW,qBAAqB;IACpC,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ,OAAO,CAAC,EAAE,OAAO,CAAC;CACnB;AAED,MAAM,WAAW,mBAAmB;IAClC,QAAQ,EAAE,QAAQ,GAAG,YAAY,CAAC;IAElC,aAAa,EAAE,mBAAmB,CAAC,YAAY,CAAC;IAEhD,IAAI,EAAE,MAAM,CAAC;IAEb,yBAAyB,CAAC,EAAE,MAAM,CAAC;IAEnC,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC;CACpC;AAED,yBAAiB,mBAAmB,CAAC;IACnC,UAAiB,YAAY;QAC3B,QAAQ,EAAE,MAAM,CAAC;QAEjB,IAAI,EAAE,kBAAkB,GAAG,cAAc,CAAC;KAC3C;CACF;AAED,MAAM,WAAW,iBAAkB,SAAQ,wBAAwB;CAAG;AAKtE,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,OAAO,EACL,KAAK,OAAO,IAAI,OAAO,EACvB,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,0BAA0B,IAAI,0BAA0B,EACxD,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,iBAAiB,IAAI,iBAAiB,GAC5C,CAAC;IAEF,OAAO,EACL,SAAS,IAAI,SAAS,EACtB,KAAK,QAAQ,IAAI,QAAQ,EACzB,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,oBAAoB,IAAI,oBAAoB,GAClD,CAAC;CACH"}