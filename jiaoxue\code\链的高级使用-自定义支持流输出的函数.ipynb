{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 自定义支持流输出的函数\n", "****\n", "- 当链被使用stream或astream调用的时候\n", "- 如何在链中增加自定义函数"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_deepseek import ChatDeepSeek\n", "import os\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 一个简单的链支持流调用"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["熊猫, 浣熊, 北极熊, 棕熊, 马来熊"]}], "source": ["from typing import Iterator, List\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "# 创建一个聊天提示模板，要求生成5个与给定动物相似的动物名称，以逗号分隔\n", "prompt = ChatPromptTemplate.from_template(\n", "    \"请列出5个与以下动物相似的动物名称，用逗号分隔：{animal}。不要包含数字\"\n", ")\n", "\n", "# 创建一个处理链：提示模板 -> 模型 -> 字符串输出解析器\n", "str_chain = prompt | llm | StrOutputParser()\n", "\n", "# 流式输出结果，输入为\"熊\"\n", "for chunk in str_chain.stream({\"animal\": \"熊\"}):\n", "    print(chunk, end=\"\", flush=True)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 增加自定义函数\n", "****\n", "- 聚合当前流传输的输出\n", "- 在生成下一个逗号的时候组合\n", "- 注意：使用了yield"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['熊猫']\n", "['浣熊']\n", "['树懒']\n", "['袋熊']\n", "['蜜獾']\n"]}], "source": ["# 这是一个自定义解析器，将LLM输出的标记迭代器\n", "# 按逗号分隔转换为字符串列表\n", "def split_into_list(input: Iterator[str]) -> Iterator[List[str]]:\n", "    # 保存部分输入直到遇到逗号\n", "    buffer = \"\"\n", "    for chunk in input:\n", "        # 将当前块添加到缓冲区\n", "        buffer += chunk\n", "        # 当缓冲区中有逗号时\n", "        while \",\" in buffer:\n", "            # 在逗号处分割缓冲区\n", "            comma_index = buffer.index(\",\")\n", "            # 输出逗号之前的所有内容\n", "            yield [buffer[:comma_index].strip()]\n", "            # 保存剩余部分用于下一次迭代\n", "            buffer = buffer[comma_index + 1 :]\n", "    # 输出最后一块\n", "    yield [buffer.strip()]\n", "\n", "\n", "list_chain = str_chain | split_into_list\n", "\n", "for chunk in list_chain.stream({\"animal\": \"熊\"}):\n", "    print(chunk, flush=True)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### yeild与return区别\n", "****\n", "- return 函数立即计算并返回所有结果，而 yield 函数按需计算结果\n", "- return 函数返回一个数据结构（如列表），yield 函数返回一个生成器对象\n", "- yield 函数可以处理潜在的无限序列，而 return 函数必须在有限时间内完成\n", "- 生成器对象是一次性的，遍历完后就被消耗完毕，而 return 返回的数据结构可以重复使用\n", "- yield 特别适合处理大数据集或流式数据，因为它不需要一次性将所有数据加载到内存中"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["使用 return 的结果: [0, 1, 4, 9, 16]\n", "类型: <class 'list'>\n", "0\n", "1\n", "4\n", "9\n", "16\n", "-------\n", "0\n", "1\n", "4\n", "9\n", "16\n"]}], "source": ["# 使用retun\n", "def get_squares_return(n):\n", "    \"\"\"返回包含 0 到 n-1 的平方的列表\"\"\"\n", "    result = []\n", "    for i in range(n):\n", "        result.append(i * i)\n", "    return result  # 一次性返回所有结果\n", "\n", "# 使用 return 函数\n", "squares = get_squares_return(5)\n", "print(\"使用 return 的结果:\", squares)  # 一次性获取所有结果\n", "print(\"类型:\", type(squares))  # 返回类型是列表\n", "\n", "# 遍历结果\n", "for num in squares:\n", "    print(num)\n", "#再次遍历\n", "print(\"-------\")\n", "for num in squares:\n", "    print(num)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["使用 yield 的结果: <generator object get_squares_yield at 0x107f17850>\n", "类型: <class 'generator'>\n", "0\n", "1\n", "4\n", "9\n", "16\n", "再次遍历:\n"]}], "source": ["# 使用 yield\n", "def get_squares_yield(n):\n", "    \"\"\"生成 0 到 n-1 的平方的生成器\"\"\"\n", "    for i in range(n):\n", "        yield i * i  # 每次生成一个结果并暂停\n", "\n", "# 使用 yield 函数\n", "squares_gen = get_squares_yield(5)\n", "print(\"使用 yield 的结果:\", squares_gen)  # 返回一个生成器对象\n", "print(\"类型:\", type(squares_gen))  # 返回类型是生成器\n", "\n", "# 遍历生成器\n", "for num in squares_gen:\n", "    print(num)  # 每次迭代时才计算下一个值\n", "\n", "# 再次遍历生成器\n", "print(\"再次遍历:\")\n", "for num in squares_gen:\n", "    print(num)  # 不会输出任何内容，因为生成器已经被消耗完毕"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}