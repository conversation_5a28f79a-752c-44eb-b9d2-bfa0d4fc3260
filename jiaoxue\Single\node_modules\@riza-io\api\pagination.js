"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecretsPagination = exports.ToolsPagination = exports.RuntimesPagination = exports.DefaultPagination = void 0;
const core_1 = require("./core.js");
class DefaultPagination extends core_1.AbstractPage {
    constructor(client, response, body, options) {
        super(client, response, body, options);
        this.data = body.data || [];
    }
    getPaginatedItems() {
        return this.data ?? [];
    }
    // @deprecated Please use `nextPageInfo()` instead
    nextPageParams() {
        const info = this.nextPageInfo();
        if (!info)
            return null;
        if ('params' in info)
            return info.params;
        const params = Object.fromEntries(info.url.searchParams);
        if (!Object.keys(params).length)
            return null;
        return params;
    }
    nextPageInfo() {
        const data = this.getPaginatedItems();
        if (!data.length) {
            return null;
        }
        const id = data[data.length - 1]?.id;
        if (!id) {
            return null;
        }
        return { params: { starting_after: id } };
    }
}
exports.DefaultPagination = DefaultPagination;
class RuntimesPagination extends core_1.AbstractPage {
    constructor(client, response, body, options) {
        super(client, response, body, options);
        this.runtimes = body.runtimes || [];
    }
    getPaginatedItems() {
        return this.runtimes ?? [];
    }
    // @deprecated Please use `nextPageInfo()` instead
    nextPageParams() {
        const info = this.nextPageInfo();
        if (!info)
            return null;
        if ('params' in info)
            return info.params;
        const params = Object.fromEntries(info.url.searchParams);
        if (!Object.keys(params).length)
            return null;
        return params;
    }
    nextPageInfo() {
        const runtimes = this.getPaginatedItems();
        if (!runtimes.length) {
            return null;
        }
        const id = runtimes[runtimes.length - 1]?.id;
        if (!id) {
            return null;
        }
        return { params: { starting_after: id } };
    }
}
exports.RuntimesPagination = RuntimesPagination;
class ToolsPagination extends core_1.AbstractPage {
    constructor(client, response, body, options) {
        super(client, response, body, options);
        this.tools = body.tools || [];
    }
    getPaginatedItems() {
        return this.tools ?? [];
    }
    // @deprecated Please use `nextPageInfo()` instead
    nextPageParams() {
        const info = this.nextPageInfo();
        if (!info)
            return null;
        if ('params' in info)
            return info.params;
        const params = Object.fromEntries(info.url.searchParams);
        if (!Object.keys(params).length)
            return null;
        return params;
    }
    nextPageInfo() {
        const tools = this.getPaginatedItems();
        if (!tools.length) {
            return null;
        }
        const id = tools[tools.length - 1]?.id;
        if (!id) {
            return null;
        }
        return { params: { starting_after: id } };
    }
}
exports.ToolsPagination = ToolsPagination;
class SecretsPagination extends core_1.AbstractPage {
    constructor(client, response, body, options) {
        super(client, response, body, options);
        this.secrets = body.secrets || [];
    }
    getPaginatedItems() {
        return this.secrets ?? [];
    }
    // @deprecated Please use `nextPageInfo()` instead
    nextPageParams() {
        const info = this.nextPageInfo();
        if (!info)
            return null;
        if ('params' in info)
            return info.params;
        const params = Object.fromEntries(info.url.searchParams);
        if (!Object.keys(params).length)
            return null;
        return params;
    }
    nextPageInfo() {
        const secrets = this.getPaginatedItems();
        if (!secrets.length) {
            return null;
        }
        const id = secrets[secrets.length - 1]?.id;
        if (!id) {
            return null;
        }
        return { params: { starting_after: id } };
    }
}
exports.SecretsPagination = SecretsPagination;
//# sourceMappingURL=pagination.js.map