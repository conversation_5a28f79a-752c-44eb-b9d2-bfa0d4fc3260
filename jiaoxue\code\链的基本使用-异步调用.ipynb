{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 链的流式调用\n", "*******\n", "- 异步调用astream\n", "- JSON流输出\n", "- 使用流事件\n", "- 注意：不是所有的组件都支持流式输出，当不支持的组件被封装在chain中后，最后的结果依旧可以使用流输出"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "llm = ChatOpenAI(\n", "    model=\"gpt-4\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 异步调用astream\n", "****"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["||Why| don|'t| par|rots| use| cell| phones|?\n", "\n", "|Because| they| already| have| their| own| \"|tweet|-|er|\"|!|||"]}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_template(\"tell me a joke about {topic}\")\n", "parser = StrOutputParser()\n", "chain = prompt | llm | parser\n", "\n", "async for chunk in chain.astream({\"topic\": \"parrot\"}):\n", "    print(chunk, end=\"|\", flush=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### JSON流输出\n", "****"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{}\n", "{'countries': []}\n", "{'countries': [{}]}\n", "{'countries': [{'name': ''}]}\n", "{'countries': [{'name': 'France'}]}\n", "{'countries': [{'name': 'France', 'population': 670}]}\n", "{'countries': [{'name': 'France', 'population': 670810}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': ''}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': 'Spain'}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': 'Spain', 'population': 469}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': 'Spain', 'population': 469400}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': 'Spain', 'population': 46940000}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': 'Spain', 'population': 46940000}, {}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': 'Spain', 'population': 46940000}, {'name': ''}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': 'Spain', 'population': 46940000}, {'name': 'Japan'}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': 'Spain', 'population': 46940000}, {'name': 'Japan', 'population': 126}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': 'Spain', 'population': 46940000}, {'name': 'Japan', 'population': 126300}]}\n", "{'countries': [{'name': 'France', 'population': 67081000}, {'name': 'Spain', 'population': 46940000}, {'name': 'Japan', 'population': 126300000}]}\n"]}], "source": ["from langchain_core.output_parsers import JsonOutputParser\n", "\n", "# 创建一个链，将LLM的输出通过JsonOutputParser进行解析\n", "# 注意：由于旧版本Langchain的一个bug，JsonOutputParser可能无法正确流式处理某些模型的结果\n", "chain = (\n", "    llm | JsonOutputParser()\n", ")  \n", "\n", "# 使用astream方法进行异步流式处理\n", "# 发送提示要求模型以JSON格式输出法国、西班牙和日本的国家及其人口信息\n", "async for text in chain.astream(\n", "    \"以JSON格式输出法国、西班牙和日本的国家列表及其人口数量。\"\n", "    '使用一个字典，包含外层键名为\"countries\"的国家列表。'\n", "    \"每个国家应该有键名`name`和`population`\"\n", "):\n", "    print(text, flush=True)  # 打印每个流式返回的文本片段，并立即刷新输出缓冲区"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 事件流\n", "*******\n", "- 这是一个测试事件\n", "- 可以将流的过程进行分解，从而事件更细颗粒度的控制\n", "- langchain-core >= 0.2\n", "- 事件流的颗粒度："]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](events.png)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 注意对于版本langchain-core<0.3.37，需要显式地指定事件流版本\n", "events = []\n", "async for event in llm.astream_events(\"hello\",version=\"v2\"):\n", "    events.append(event)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["查看前三个events"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'event': 'on_chat_model_start',\n", "  'data': {'input': 'hello'},\n", "  'name': 'ChatOpenAI',\n", "  'tags': [],\n", "  'run_id': '5cf4fe00-8cbf-4bd2-a3b7-509078e5aaaf',\n", "  'metadata': {'ls_provider': 'openai',\n", "   'ls_model_name': 'gpt-4',\n", "   'ls_model_type': 'chat',\n", "   'ls_temperature': 0.0},\n", "  'parent_ids': []},\n", " {'event': 'on_chat_model_stream',\n", "  'run_id': '5cf4fe00-8cbf-4bd2-a3b7-509078e5aaaf',\n", "  'name': 'ChatOpenAI',\n", "  'tags': [],\n", "  'metadata': {'ls_provider': 'openai',\n", "   'ls_model_name': 'gpt-4',\n", "   'ls_model_type': 'chat',\n", "   'ls_temperature': 0.0},\n", "  'data': {'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run-5cf4fe00-8cbf-4bd2-a3b7-509078e5aaaf')},\n", "  'parent_ids': []},\n", " {'event': 'on_chat_model_stream',\n", "  'run_id': '5cf4fe00-8cbf-4bd2-a3b7-509078e5aaaf',\n", "  'name': 'ChatOpenAI',\n", "  'tags': [],\n", "  'metadata': {'ls_provider': 'openai',\n", "   'ls_model_name': 'gpt-4',\n", "   'ls_model_type': 'chat',\n", "   'ls_temperature': 0.0},\n", "  'data': {'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run-5cf4fe00-8cbf-4bd2-a3b7-509078e5aaaf')},\n", "  'parent_ids': []}]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["events[:3]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 事件过滤 - 按name\n", "***\n", "按照运行时配置的name来过滤事件"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'event': 'on_parser_start', 'data': {'input': 'output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key `name` and `population`'}, 'name': 'my_parser', 'tags': ['seq:step:2'], 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'metadata': {}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "{'event': 'on_parser_stream', 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'name': 'my_parser', 'tags': ['seq:step:2'], 'metadata': {}, 'data': {'chunk': {}}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "{'event': 'on_parser_stream', 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'name': 'my_parser', 'tags': ['seq:step:2'], 'metadata': {}, 'data': {'chunk': {'countries': []}}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "{'event': 'on_parser_stream', 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'name': 'my_parser', 'tags': ['seq:step:2'], 'metadata': {}, 'data': {'chunk': {'countries': [{}]}}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "{'event': 'on_parser_stream', 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'name': 'my_parser', 'tags': ['seq:step:2'], 'metadata': {}, 'data': {'chunk': {'countries': [{'name': ''}]}}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "{'event': 'on_parser_stream', 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'name': 'my_parser', 'tags': ['seq:step:2'], 'metadata': {}, 'data': {'chunk': {'countries': [{'name': 'France'}]}}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "{'event': 'on_parser_stream', 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'name': 'my_parser', 'tags': ['seq:step:2'], 'metadata': {}, 'data': {'chunk': {'countries': [{'name': 'France', 'population': 670}]}}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "{'event': 'on_parser_stream', 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'name': 'my_parser', 'tags': ['seq:step:2'], 'metadata': {}, 'data': {'chunk': {'countries': [{'name': 'France', 'population': 670810}]}}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "{'event': 'on_parser_stream', 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'name': 'my_parser', 'tags': ['seq:step:2'], 'metadata': {}, 'data': {'chunk': {'countries': [{'name': 'France', 'population': 67081000}]}}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "{'event': 'on_parser_stream', 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'name': 'my_parser', 'tags': ['seq:step:2'], 'metadata': {}, 'data': {'chunk': {'countries': [{'name': 'France', 'population': 67081000}, {}]}}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "{'event': 'on_parser_stream', 'run_id': '696f5f97-d770-48da-91d5-54f9c9d5b1ef', 'name': 'my_parser', 'tags': ['seq:step:2'], 'metadata': {}, 'data': {'chunk': {'countries': [{'name': 'France', 'population': 67081000}, {'name': ''}]}}, 'parent_ids': ['048cc5a3-e3d8-400d-ad88-5782d3930a5d']}\n", "...\n"]}], "source": ["chain = llm.with_config({\"run_name\": \"model\"}) | JsonOutputParser().with_config(\n", "    {\"run_name\": \"my_parser\"}\n", ")\n", "\n", "max_events = 0\n", "async for event in chain.astream_events(\n", "    \"output a list of the countries france, spain and japan and their populations in JSON format. \"\n", "    'Use a dict with an outer key of \"countries\" which contains a list of countries. '\n", "    \"Each country should have the key `name` and `population`\",\n", "    include_names=[\"my_parser\"],version=\"v2\"\n", "):\n", "    print(event)\n", "    max_events += 1\n", "    if max_events > 10:\n", "        # Truncate output\n", "        print(\"...\")\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 事件过滤 - 按tag\n", "***\n", "按照tag来过滤事件"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'event': 'on_chain_start', 'data': {'input': 'output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key `name` and `population`'}, 'name': 'RunnableSequence', 'tags': ['my_chain'], 'run_id': '462eefd2-9b86-45ac-bc36-cfd7d1644eb0', 'metadata': {}, 'parent_ids': []}\n", "{'event': 'on_chat_model_start', 'data': {'input': {'messages': [[HumanMessage(content='output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key `name` and `population`', additional_kwargs={}, response_metadata={})]]}}, 'name': 'ChatOpenAI', 'tags': ['seq:step:1', 'my_chain'], 'run_id': '74bad696-af1f-4c97-848d-feacaf1dd1e1', 'metadata': {'ls_provider': 'openai', 'ls_model_name': 'gpt-4', 'ls_model_type': 'chat', 'ls_temperature': 0.0}, 'parent_ids': ['462eefd2-9b86-45ac-bc36-cfd7d1644eb0']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run-74bad696-af1f-4c97-848d-feacaf1dd1e1')}, 'run_id': '74bad696-af1f-4c97-848d-feacaf1dd1e1', 'name': 'ChatOpenAI', 'tags': ['seq:step:1', 'my_chain'], 'metadata': {'ls_provider': 'openai', 'ls_model_name': 'gpt-4', 'ls_model_type': 'chat', 'ls_temperature': 0.0}, 'parent_ids': ['462eefd2-9b86-45ac-bc36-cfd7d1644eb0']}\n", "{'event': 'on_parser_start', 'data': {}, 'name': 'JsonOutputParser', 'tags': ['seq:step:2', 'my_chain'], 'run_id': 'b5880ece-8d46-4a76-b8b3-4f26ecf1598e', 'metadata': {}, 'parent_ids': ['462eefd2-9b86-45ac-bc36-cfd7d1644eb0']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run-74bad696-af1f-4c97-848d-feacaf1dd1e1')}, 'run_id': '74bad696-af1f-4c97-848d-feacaf1dd1e1', 'name': 'ChatOpenAI', 'tags': ['seq:step:1', 'my_chain'], 'metadata': {'ls_provider': 'openai', 'ls_model_name': 'gpt-4', 'ls_model_type': 'chat', 'ls_temperature': 0.0}, 'parent_ids': ['462eefd2-9b86-45ac-bc36-cfd7d1644eb0']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='{\\n', additional_kwargs={}, response_metadata={}, id='run-74bad696-af1f-4c97-848d-feacaf1dd1e1')}, 'run_id': '74bad696-af1f-4c97-848d-feacaf1dd1e1', 'name': 'ChatOpenAI', 'tags': ['seq:step:1', 'my_chain'], 'metadata': {'ls_provider': 'openai', 'ls_model_name': 'gpt-4', 'ls_model_type': 'chat', 'ls_temperature': 0.0}, 'parent_ids': ['462eefd2-9b86-45ac-bc36-cfd7d1644eb0']}\n", "{'event': 'on_parser_stream', 'run_id': 'b5880ece-8d46-4a76-b8b3-4f26ecf1598e', 'name': 'JsonOutputParser', 'tags': ['seq:step:2', 'my_chain'], 'metadata': {}, 'data': {'chunk': {}}, 'parent_ids': ['462eefd2-9b86-45ac-bc36-cfd7d1644eb0']}\n", "{'event': 'on_chain_stream', 'run_id': '462eefd2-9b86-45ac-bc36-cfd7d1644eb0', 'name': 'RunnableSequence', 'tags': ['my_chain'], 'metadata': {}, 'data': {'chunk': {}}, 'parent_ids': []}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' ', additional_kwargs={}, response_metadata={}, id='run-74bad696-af1f-4c97-848d-feacaf1dd1e1')}, 'run_id': '74bad696-af1f-4c97-848d-feacaf1dd1e1', 'name': 'ChatOpenAI', 'tags': ['seq:step:1', 'my_chain'], 'metadata': {'ls_provider': 'openai', 'ls_model_name': 'gpt-4', 'ls_model_type': 'chat', 'ls_temperature': 0.0}, 'parent_ids': ['462eefd2-9b86-45ac-bc36-cfd7d1644eb0']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content=' \"', additional_kwargs={}, response_metadata={}, id='run-74bad696-af1f-4c97-848d-feacaf1dd1e1')}, 'run_id': '74bad696-af1f-4c97-848d-feacaf1dd1e1', 'name': 'ChatOpenAI', 'tags': ['seq:step:1', 'my_chain'], 'metadata': {'ls_provider': 'openai', 'ls_model_name': 'gpt-4', 'ls_model_type': 'chat', 'ls_temperature': 0.0}, 'parent_ids': ['462eefd2-9b86-45ac-bc36-cfd7d1644eb0']}\n", "{'event': 'on_chat_model_stream', 'data': {'chunk': AIMessageChunk(content='countries', additional_kwargs={}, response_metadata={}, id='run-74bad696-af1f-4c97-848d-feacaf1dd1e1')}, 'run_id': '74bad696-af1f-4c97-848d-feacaf1dd1e1', 'name': 'ChatOpenAI', 'tags': ['seq:step:1', 'my_chain'], 'metadata': {'ls_provider': 'openai', 'ls_model_name': 'gpt-4', 'ls_model_type': 'chat', 'ls_temperature': 0.0}, 'parent_ids': ['462eefd2-9b86-45ac-bc36-cfd7d1644eb0']}\n", "...\n"]}], "source": ["chain = (llm | JsonOutputParser()).with_config({\"tags\": [\"my_chain\"]})\n", "\n", "max_events = 0\n", "async for event in chain.astream_events(\n", "    'output a list of the countries france, spain and japan and their populations in JSON format. Use a dict with an outer key of \"countries\" which contains a list of countries. Each country should have the key `name` and `population`',\n", "    include_tags=[\"my_chain\"],version=\"v2\"\n", "):\n", "    print(event)\n", "    max_events += 1\n", "    if max_events > 10:\n", "        # Truncate output\n", "        print(\"...\")\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 事件阶段过滤\n", "***"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Chat model chunk: ''\n", "Chat model chunk: ''\n", "Chat model chunk: '{\\n'\n", "Parser chunk: {}\n", "Chat model chunk: ' '\n", "Chat model chunk: ' \"'\n", "Chat model chunk: 'countries'\n", "Chat model chunk: '\":'\n", "Chat model chunk: ' [\\n'\n", "Parser chunk: {'countries': []}\n", "Chat model chunk: '   '\n", "Chat model chunk: ' {\\n'\n", "Parser chunk: {'countries': [{}]}\n", "Chat model chunk: '     '\n", "Chat model chunk: ' \"'\n", "Chat model chunk: 'name'\n", "Chat model chunk: '\":'\n", "Chat model chunk: ' \"'\n", "Parser chunk: {'countries': [{'name': ''}]}\n", "Chat model chunk: 'France'\n", "Parser chunk: {'countries': [{'name': 'France'}]}\n", "Chat model chunk: '\",\\n'\n", "Chat model chunk: '     '\n", "...\n"]}], "source": ["num_events = 0\n", "\n", "async for event in chain.astream_events(\n", "    \"output a list of the countries france, spain and japan and their populations in JSON format. \"\n", "    'Use a dict with an outer key of \"countries\" which contains a list of countries. '\n", "    \"Each country should have the key `name` and `population`\",version=\"v2\"\n", "):\n", "    kind = event[\"event\"]\n", "    if kind == \"on_chat_model_stream\":\n", "        print(\n", "            f\"Chat model chunk: {repr(event['data']['chunk'].content)}\",\n", "            flush=True,\n", "        )\n", "    if kind == \"on_parser_stream\":\n", "        print(f\"Parser chunk: {event['data']['chunk']}\", flush=True)\n", "    num_events += 1\n", "    if num_events > 30:\n", "        # Truncate the output\n", "        print(\"...\")\n", "        break"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}