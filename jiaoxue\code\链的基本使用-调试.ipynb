{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_deepseek import ChatDeepSeek\n", "import os\n", "llm1 = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "\n", "llm2 = ChatOpenAI(\n", "    model=\"gpt-4\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    )"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'joke': AIMessage(content='## 《代码与咖啡》\\n\\n\"叮铃铃——\"\\n\\n凌晨三点的闹钟准时响起，我猛地从床上弹起来，揉了揉惺忪的睡眼。电脑屏幕还亮着，上面密密麻麻的代码像是一群蚂蚁在爬行。\\n\\n\"又睡着了......\"我懊恼地拍了拍自己的脸，端起已经凉透的咖啡一饮而尽。苦涩的液体滑过喉咙，让我稍微清醒了一些。\\n\\n这是我连续工作的第36个小时。为了赶在deadline之前完成这个项目，我已经不知道喝了多少杯咖啡，熬了多少个通宵。眼睛干涩得像是被砂纸磨过，手指在键盘上机械地敲击着，大脑却已经有些迟钝。\\n\\n\"再坚持一下，就快完成了......\"我喃喃自语，又打开了一个新的代码文件。\\n\\n突然，屏幕上的代码开始扭曲，像是被什么东西吸了进去。我使劲揉了揉眼睛，以为是自己太累出现了幻觉。但下一秒，我整个人都被吸进了屏幕里！\\n\\n\"啊——\"\\n\\n我尖叫着，感觉自己在一个由代码组成的世界里飞速下坠。周围是无数闪烁的0和1，还有各种编程语言的符号在飞舞。我试图抓住什么，但手指只能穿过这些虚幻的代码。\\n\\n\"砰！\"\\n\\n我重重地摔在了一个由大括号{}组成的地面上。抬头望去，眼前是一个由代码构建的奇幻世界：if-else语句像树枝一样在空中伸展，for循环像摩天轮一样旋转，函数像城堡一样矗立。\\n\\n\"欢迎来到代码世界，程序员先生。\"一个声音从身后传来。\\n\\n我转身一看，差点没被吓晕过去——一个由代码组成的人形生物正站在我面前。它的身体是由各种编程语言符号拼接而成，眼睛是两个分号，嘴巴是一个括号。\\n\\n\"这...这是哪里？\"我结结巴巴地问道。\\n\\n\"这里是代码世界，是所有程序员的梦想之地。\"代码人说道，\"在这里，你可以实现任何编程梦想。\"\\n\\n我还没来得及反应，代码人就打了个响指。瞬间，我周围出现了无数个电脑屏幕，每个屏幕上都显示着不同的项目需求。\\n\\n\"这些是等待你完成的项目，\"代码人说，\"在代码世界，你永远不用担心失业。\"\\n\\n我惊恐地看着那些屏幕：一个要求在一小时内完成一个完整的操作系统，另一个要求在十分钟内写出一个完美的人工智能，还有一个要求在五分钟内解决所有已知的编程难题......\\n\\n\"不，这不是我的梦想！\"我大喊着，\"我要回去！\"\\n\\n\"回去？\"代码人歪着头，它的分号眼睛闪烁着疑惑的光芒，\"你不是一直在追求完美的代码吗？在这里，你可以永远写代码，永远不用休息，永远不用面对现实世界的烦恼......\"\\n\\n\"不！\"我打断它，\"我虽然热爱编程，但我也是人！我需要休息，需要生活，需要......需要上厕所！\"\\n\\n说到最后，我几乎是在尖叫了。突然，我感觉一阵天旋地转，再次被吸入了代码的漩涡中。\\n\\n\"啊——\"\\n\\n我猛地从椅子上惊醒，发现自己还在办公室里。电脑屏幕上的代码依然在闪烁，旁边的咖啡杯已经空了。\\n\\n\"原来是个梦......\"我长舒一口气，看了看时间，已经是早上六点了。\\n\\n我站起身，伸了个懒腰，决定去洗把脸清醒一下。走到洗手间，我看着镜子里的自己：黑眼圈深得像是被人打了两拳，头发乱得像鸟窝，整个人看起来像是刚从代码地狱里爬出来。\\n\\n\"也许我该休息一下了......\"我自言自语道。\\n\\n就在这时，我的手机响了。是老板发来的消息：\"项目进度怎么样了？客户要求提前交付，今天中午之前必须完成。\"\\n\\n我盯着那条消息看了足足十秒钟，然后默默地走回工位，又给自己冲了一杯咖啡。\\n\\n\"看来，代码世界和现实世界，其实也没什么区别......\"我苦笑着，继续在键盘上敲击起来。\\n\\n至少，在现实世界，我还能喝到真正的咖啡。虽然它很苦，但至少比代码世界的虚拟咖啡要好得多。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 818, 'prompt_tokens': 10, 'total_tokens': 828, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-d3351597-7a8f-42b7-9dea-5c2500ef591f-0', usage_metadata={'input_tokens': 10, 'output_tokens': 818, 'total_tokens': 828, 'input_token_details': {}, 'output_token_details': {}}),\n", " 'poem': AIMessage(content='代码如诗，夜深人静独斟酌。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 20, 'prompt_tokens': 21, 'total_tokens': 41, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-8fe95492-5014-4596-9be9-4ad44055dbc0-0', usage_metadata={'input_tokens': 21, 'output_tokens': 20, 'total_tokens': 41, 'input_token_details': {}, 'output_token_details': {}})}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnableParallel\n", "\n", "joke_chain = ChatPromptTemplate.from_template(\"给我讲一个关于{topic}的笑话\") | llm1\n", "poem_chain = (\n", "    ChatPromptTemplate.from_template(\"给我写一首关于{topic}的绝句\") | llm2\n", ")\n", "\n", "map_chain = RunnableParallel(joke=joke_chain, poem=poem_chain)\n", "\n", "map_chain.invoke({\"topic\": \"程序员\"})"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["Graph(nodes={'9317976ffe214303a5a221ae7729dee1': Node(id='9317976ffe214303a5a221ae7729dee1', name='<PERSON>llel<joke,poem>Input', data=<class 'langchain_core.utils.pydantic.RunnableParallel<joke,poem>Input'>, metadata=None), 'fc06c994fc1b46c0b48249de2d6d2205': Node(id='fc06c994fc1b46c0b48249de2d6d2205', name='Parallel<joke,poem>Output', data=<class 'langchain_core.utils.pydantic.RunnableParallel<joke,poem>Output'>, metadata=None), '894e9da5780e41fd8218cf45982c0f6f': Node(id='894e9da5780e41fd8218cf45982c0f6f', name='ChatPromptTemplate', data=ChatPromptTemplate(input_variables=['topic'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['topic'], input_types={}, partial_variables={}, template='给我讲一个关于{topic}的笑话'), additional_kwargs={})]), metadata=None), 'e4ca2c88b964418a8ab231100e36e0b1': Node(id='e4ca2c88b964418a8ab231100e36e0b1', name='ChatDeepSeek', data=ChatDeepSeek(client=<openai.resources.chat.completions.completions.Completions object at 0x131d34050>, async_client=<openai.resources.chat.completions.completions.AsyncCompletions object at 0x131d37cb0>, model_name='Pro/deepseek-ai/DeepSeek-V3', temperature=0.0, model_kwargs={}, openai_api_key=SecretStr('**********'), api_key=SecretStr('**********'), api_base='https://api.siliconflow.cn'), metadata=None), '3be7abddc1874552b45fd8bc8d54d32c': Node(id='3be7abddc1874552b45fd8bc8d54d32c', name='ChatPromptTemplate', data=ChatPromptTemplate(input_variables=['topic'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['topic'], input_types={}, partial_variables={}, template='给我写一首关于{topic}的绝句'), additional_kwargs={})]), metadata=None), '7a0577018bb745fca512f18761c34114': Node(id='7a0577018bb745fca512f18761c34114', name='ChatOpenAI', data=ChatOpenAI(client=<openai.resources.chat.completions.completions.Completions object at 0x131d3bed0>, async_client=<openai.resources.chat.completions.completions.AsyncCompletions object at 0x131d534d0>, root_client=<openai.OpenAI object at 0x120729d10>, root_async_client=<openai.AsyncOpenAI object at 0x131d50050>, model_name='gpt-4', temperature=0.0, model_kwargs={}, openai_api_key=SecretStr('**********'), openai_api_base='https://api.openai-proxy.org/v1'), metadata=None)}, edges=[Edge(source='894e9da5780e41fd8218cf45982c0f6f', target='e4ca2c88b964418a8ab231100e36e0b1', data=None, conditional=False), Edge(source='9317976ffe214303a5a221ae7729dee1', target='894e9da5780e41fd8218cf45982c0f6f', data=None, conditional=False), Edge(source='e4ca2c88b964418a8ab231100e36e0b1', target='fc06c994fc1b46c0b48249de2d6d2205', data=None, conditional=False), Edge(source='3be7abddc1874552b45fd8bc8d54d32c', target='7a0577018bb745fca512f18761c34114', data=None, conditional=False), Edge(source='9317976ffe214303a5a221ae7729dee1', target='3be7abddc1874552b45fd8bc8d54d32c', data=None, conditional=False), Edge(source='7a0577018bb745fca512f18761c34114', target='fc06c994fc1b46c0b48249de2d6d2205', data=None, conditional=False)])"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["map_chain.get_graph()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! pip install grandalf"]}, {"cell_type": "markdown", "metadata": {}, "source": ["查看图"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["               +--------------------------+                \n", "               | Parallel<joke,poem>Input |                \n", "               +--------------------------+                \n", "                   ***               ***                   \n", "                ***                     ***                \n", "              **                           **              \n", "+--------------------+              +--------------------+ \n", "| ChatPromptTemplate |              | ChatPromptTemplate | \n", "+--------------------+              +--------------------+ \n", "           *                                   *           \n", "           *                                   *           \n", "           *                                   *           \n", "   +--------------+                     +------------+     \n", "   | ChatDeepSeek |                     | ChatOpenAI |     \n", "   +--------------+                     +------------+     \n", "                   ***               ***                   \n", "                      ***         ***                      \n", "                         **     **                         \n", "              +---------------------------+                \n", "              | Parallel<joke,poem>Output |                \n", "              +---------------------------+                \n"]}], "source": ["map_chain.get_graph().print_ascii()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["查看提示词"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[ChatPromptTemplate(input_variables=['topic'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['topic'], input_types={}, partial_variables={}, template='给我讲一个关于{topic}的笑话'), additional_kwargs={})]),\n", " ChatPromptTemplate(input_variables=['topic'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['topic'], input_types={}, partial_variables={}, template='给我写一首关于{topic}的绝句'), additional_kwargs={})])]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["map_chain.get_prompts()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}