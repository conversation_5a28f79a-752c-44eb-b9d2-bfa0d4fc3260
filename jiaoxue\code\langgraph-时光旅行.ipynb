{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## langgraph-时光旅行\n", "*****\n", "- 重放\n", "- 分叉\n", "- 注意经过实际测试deepseek的tool calling能力还是不如openai"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# 设置工具\n", "from langchain_openai import ChatOpenAI\n", "from langchain_core.tools import tool\n", "from langgraph.graph import MessagesState, START\n", "from langgraph.prebuilt import ToolNode\n", "from langgraph.graph import END, StateGraph\n", "from langgraph.checkpoint.memory import MemorySaver\n", "\n", "\n", "@tool\n", "def play_song_on_qq(song: str):\n", "    \"\"\"在qq音乐上播放歌曲\"\"\"\n", "    # 调用QQ音乐 API...\n", "    return f\"成功在QQ音乐上播放了{song}！\"\n", "\n", "\n", "@tool\n", "def play_song_on_163(song: str):\n", "    \"\"\"在网易云上播放歌曲\"\"\"\n", "    # 调用网易云 API...\n", "    return f\"成功在网易云上播放了{song}！\"\n", "\n", "\n", "tools = [play_song_on_qq, play_song_on_163]\n", "tool_node = ToolNode(tools)\n", "\n", "# 设置模型\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "deepseek = ChatOpenAI(\n", "    model=\"gpt-4o\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_BASE_URL\"),\n", ")\n", "\n", "model = deepseek.bind_tools(tools, parallel_tool_calls=False)\n", "\n", "\n", "# 定义节点和条件边\n", "\n", "\n", "# 定义确定是否继续的函数\n", "def should_continue(state):\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    # 如果没有函数调用，则结束\n", "    if not last_message.tool_calls:\n", "        return \"end\"\n", "    # 否则如果有，我们继续\n", "    else:\n", "        return \"continue\"\n", "\n", "\n", "# 定义调用模型的函数\n", "def call_model(state):\n", "    messages = state[\"messages\"]\n", "    response = model.invoke(messages)\n", "    # 我们返回一个列表，因为这将被添加到现有列表中\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# 定义一个新图\n", "workflow = StateGraph(MessagesState)\n", "\n", "# 定义我们将循环的两个节点\n", "workflow.add_node(\"agent\", call_model)\n", "workflow.add_node(\"action\", tool_node)\n", "\n", "# 将入口点设置为`agent`\n", "# 这意味着这个节点是第一个被调用的\n", "workflow.add_edge(START, \"agent\")\n", "\n", "# 现在添加一个条件边\n", "workflow.add_conditional_edges(\n", "    # 首先，我们定义起始节点。我们使用`agent`。\n", "    # 这意味着这些是在调用`agent`节点后采取的边。\n", "    \"agent\",\n", "    # 接下来，我们传入将确定下一个调用哪个节点的函数。\n", "    should_continue,\n", "    # 最后我们传入一个映射。\n", "    # 键是字符串，值是其他节点。\n", "    # END是一个特殊节点，标记图应该结束。\n", "    # 将会发生的是我们调用`should_continue`，然后该函数的输出\n", "    # 将与此映射中的键匹配。\n", "    # 根据匹配的键，然后调用相应的节点。\n", "    {\n", "        # 如果是`tools`，则调用工具节点。\n", "        \"continue\": \"action\",\n", "        # 否则我们结束。\n", "        \"end\": END,\n", "    },\n", ")\n", "\n", "# 现在我们从`tools`到`agent`添加一个普通边。\n", "# 这意味着在调用`tools`之后，下一步调用`agent`节点。\n", "workflow.add_edge(\"action\", \"agent\")\n", "\n", "# 设置内存\n", "memory = MemorySaver()\n", "\n", "# 最后，我们编译它！\n", "# 这将它编译成一个LangChain Runnable，\n", "# 意味着你可以像使用任何其他runnable一样使用它\n", "\n", "# 我们添加`interrupt_before=[\"action\"]`\n", "# 这将在调用`action`节点之前添加一个断点\n", "app = workflow.compile(checkpointer=memory)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["进行简单交互，要求播放周董的歌曲"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "你能播放一首周杰伦播放量最高的歌曲吗?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  play_song_on_qq (call_jXjBMwXhQdWGVwmzCG607Dg3)\n", " Call ID: call_jXjBMwXhQdWGVwmzCG607Dg3\n", "  Args:\n", "    song: 周杰伦\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: play_song_on_qq\n", "\n", "成功在QQ音乐上播放了周杰伦！\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "我已经在QQ音乐上播放了周杰伦的歌曲！你可以去QQ音乐欣赏他的音乐。\n"]}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "config = {\"configurable\": {\"thread_id\": \"1\"}}\n", "input_message = HumanMessage(content=\"你能播放一首周杰伦播放量最高的歌曲吗?\")\n", "for event in app.stream({\"messages\": [input_message]}, config, stream_mode=\"values\"):\n", "    event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["查看记录并重放"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[HumanMessage(content='你能播放一首周杰伦播放量最高的歌曲吗?', additional_kwargs={}, response_metadata={}, id='e2a5acbb-4b30-44d9-9253-04e2248dd617'),\n", " AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'function': {'arguments': '{\"song\":\"周杰伦\"}', 'name': 'play_song_on_qq'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 20, 'prompt_tokens': 86, 'total_tokens': 106, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-aa8fe78a-f8a8-4edd-9f17-729d5dc6fb92-0', tool_calls=[{'name': 'play_song_on_qq', 'args': {'song': '周杰伦'}, 'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'type': 'tool_call'}], usage_metadata={'input_tokens': 86, 'output_tokens': 20, 'total_tokens': 106, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}),\n", " ToolMessage(content='成功在QQ音乐上播放了周杰伦！', name='play_song_on_qq', id='2216abd2-e0d4-48d4-ad6c-d7418ce15a4c', tool_call_id='call_jXjBMwXhQdWGVwmzCG607Dg3'),\n", " AIMessage(content='我已经在QQ音乐上播放了周杰伦的歌曲！你可以去QQ音乐欣赏他的音乐。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 26, 'prompt_tokens': 127, 'total_tokens': 153, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'stop', 'logprobs': None}, id='run-460a30ad-44b4-4eb5-aefb-0887034245a1-0', usage_metadata={'input_tokens': 127, 'output_tokens': 26, 'total_tokens': 153, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["app.get_state(config).values[\"messages\"]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["StateSnapshot(values={'messages': [HumanMessage(content='你能播放一首周杰伦播放量最高的歌曲吗?', additional_kwargs={}, response_metadata={}, id='e2a5acbb-4b30-44d9-9253-04e2248dd617'), AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'function': {'arguments': '{\"song\":\"周杰伦\"}', 'name': 'play_song_on_qq'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 20, 'prompt_tokens': 86, 'total_tokens': 106, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-aa8fe78a-f8a8-4edd-9f17-729d5dc6fb92-0', tool_calls=[{'name': 'play_song_on_qq', 'args': {'song': '周杰伦'}, 'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'type': 'tool_call'}], usage_metadata={'input_tokens': 86, 'output_tokens': 20, 'total_tokens': 106, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}), ToolMessage(content='成功在QQ音乐上播放了周杰伦！', name='play_song_on_qq', id='2216abd2-e0d4-48d4-ad6c-d7418ce15a4c', tool_call_id='call_jXjBMwXhQdWGVwmzCG607Dg3'), AIMessage(content='我已经在QQ音乐上播放了周杰伦的歌曲！你可以去QQ音乐欣赏他的音乐。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 26, 'prompt_tokens': 127, 'total_tokens': 153, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'stop', 'logprobs': None}, id='run-460a30ad-44b4-4eb5-aefb-0887034245a1-0', usage_metadata={'input_tokens': 127, 'output_tokens': 26, 'total_tokens': 153, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}, next=(), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f00d746-8a6f-6604-8003-cc9091cb1bba'}}, metadata={'source': 'loop', 'writes': {'agent': {'messages': [AIMessage(content='我已经在QQ音乐上播放了周杰伦的歌曲！你可以去QQ音乐欣赏他的音乐。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 26, 'prompt_tokens': 127, 'total_tokens': 153, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'stop', 'logprobs': None}, id='run-460a30ad-44b4-4eb5-aefb-0887034245a1-0', usage_metadata={'input_tokens': 127, 'output_tokens': 26, 'total_tokens': 153, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}}, 'thread_id': '1', 'step': 3, 'parents': {}}, created_at='2025-03-30T14:36:47.162479+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f00d746-7dbe-6e78-8002-5ab97c692b2a'}}, tasks=())\n", "--\n", "StateSnapshot(values={'messages': [HumanMessage(content='你能播放一首周杰伦播放量最高的歌曲吗?', additional_kwargs={}, response_metadata={}, id='e2a5acbb-4b30-44d9-9253-04e2248dd617'), AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'function': {'arguments': '{\"song\":\"周杰伦\"}', 'name': 'play_song_on_qq'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 20, 'prompt_tokens': 86, 'total_tokens': 106, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-aa8fe78a-f8a8-4edd-9f17-729d5dc6fb92-0', tool_calls=[{'name': 'play_song_on_qq', 'args': {'song': '周杰伦'}, 'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'type': 'tool_call'}], usage_metadata={'input_tokens': 86, 'output_tokens': 20, 'total_tokens': 106, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}}), ToolMessage(content='成功在QQ音乐上播放了周杰伦！', name='play_song_on_qq', id='2216abd2-e0d4-48d4-ad6c-d7418ce15a4c', tool_call_id='call_jXjBMwXhQdWGVwmzCG607Dg3')]}, next=('agent',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f00d746-7dbe-6e78-8002-5ab97c692b2a'}}, metadata={'source': 'loop', 'writes': {'action': {'messages': [ToolMessage(content='成功在QQ音乐上播放了周杰伦！', name='play_song_on_qq', id='2216abd2-e0d4-48d4-ad6c-d7418ce15a4c', tool_call_id='call_jXjBMwXhQdWGVwmzCG607Dg3')]}}, 'thread_id': '1', 'step': 2, 'parents': {}}, created_at='2025-03-30T14:36:45.831930+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f00d746-7db8-6e74-8001-05ee090c0477'}}, tasks=(PregelTask(id='395e8a1d-2175-b839-e471-e0b48a2c7f6a', name='agent', path=('__pregel_pull', 'agent'), error=None, interrupts=(), state=None, result={'messages': [AIMessage(content='我已经在QQ音乐上播放了周杰伦的歌曲！你可以去QQ音乐欣赏他的音乐。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 26, 'prompt_tokens': 127, 'total_tokens': 153, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'stop', 'logprobs': None}, id='run-460a30ad-44b4-4eb5-aefb-0887034245a1-0', usage_metadata={'input_tokens': 127, 'output_tokens': 26, 'total_tokens': 153, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}),))\n", "--\n", "StateSnapshot(values={'messages': [HumanMessage(content='你能播放一首周杰伦播放量最高的歌曲吗?', additional_kwargs={}, response_metadata={}, id='e2a5acbb-4b30-44d9-9253-04e2248dd617'), AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'function': {'arguments': '{\"song\":\"周杰伦\"}', 'name': 'play_song_on_qq'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 20, 'prompt_tokens': 86, 'total_tokens': 106, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-aa8fe78a-f8a8-4edd-9f17-729d5dc6fb92-0', tool_calls=[{'name': 'play_song_on_qq', 'args': {'song': '周杰伦'}, 'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'type': 'tool_call'}], usage_metadata={'input_tokens': 86, 'output_tokens': 20, 'total_tokens': 106, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}, next=('action',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f00d746-7db8-6e74-8001-05ee090c0477'}}, metadata={'source': 'loop', 'writes': {'agent': {'messages': [AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'function': {'arguments': '{\"song\":\"周杰伦\"}', 'name': 'play_song_on_qq'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 20, 'prompt_tokens': 86, 'total_tokens': 106, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-aa8fe78a-f8a8-4edd-9f17-729d5dc6fb92-0', tool_calls=[{'name': 'play_song_on_qq', 'args': {'song': '周杰伦'}, 'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'type': 'tool_call'}], usage_metadata={'input_tokens': 86, 'output_tokens': 20, 'total_tokens': 106, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}}, 'thread_id': '1', 'step': 1, 'parents': {}}, created_at='2025-03-30T14:36:45.829453+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f00d746-58ef-6688-8000-210d5da842b8'}}, tasks=(PregelTask(id='5bd0f54b-4ad4-b4ac-1138-a8b15388e06b', name='action', path=('__pregel_pull', 'action'), error=None, interrupts=(), state=None, result={'messages': [ToolMessage(content='成功在QQ音乐上播放了周杰伦！', name='play_song_on_qq', tool_call_id='call_jXjBMwXhQdWGVwmzCG607Dg3')]}),))\n", "--\n", "StateSnapshot(values={'messages': [HumanMessage(content='你能播放一首周杰伦播放量最高的歌曲吗?', additional_kwargs={}, response_metadata={}, id='e2a5acbb-4b30-44d9-9253-04e2248dd617')]}, next=('agent',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f00d746-58ef-6688-8000-210d5da842b8'}}, metadata={'source': 'loop', 'writes': None, 'thread_id': '1', 'step': 0, 'parents': {}}, created_at='2025-03-30T14:36:41.972076+00:00', parent_config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f00d746-58ec-6b4a-bfff-1a7149ea45f7'}}, tasks=(PregelTask(id='072970fd-2207-62d7-a28c-207ee7ccefa5', name='agent', path=('__pregel_pull', 'agent'), error=None, interrupts=(), state=None, result={'messages': [AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'function': {'arguments': '{\"song\":\"周杰伦\"}', 'name': 'play_song_on_qq'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 20, 'prompt_tokens': 86, 'total_tokens': 106, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-aa8fe78a-f8a8-4edd-9f17-729d5dc6fb92-0', tool_calls=[{'name': 'play_song_on_qq', 'args': {'song': '周杰伦'}, 'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'type': 'tool_call'}], usage_metadata={'input_tokens': 86, 'output_tokens': 20, 'total_tokens': 106, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}),))\n", "--\n", "StateSnapshot(values={'messages': []}, next=('__start__',), config={'configurable': {'thread_id': '1', 'checkpoint_ns': '', 'checkpoint_id': '1f00d746-58ec-6b4a-bfff-1a7149ea45f7'}}, metadata={'source': 'input', 'writes': {'__start__': {'messages': [HumanMessage(content='你能播放一首周杰伦播放量最高的歌曲吗?', additional_kwargs={}, response_metadata={})]}}, 'thread_id': '1', 'step': -1, 'parents': {}}, created_at='2025-03-30T14:36:41.970971+00:00', parent_config=None, tasks=(PregelTask(id='261c039a-696e-a498-4000-21d24c19f9c0', name='__start__', path=('__pregel_pull', '__start__'), error=None, interrupts=(), state=None, result={'messages': [HumanMessage(content='你能播放一首周杰伦播放量最高的歌曲吗?', additional_kwargs={}, response_metadata={}, id='e2a5acbb-4b30-44d9-9253-04e2248dd617')]}),))\n", "--\n"]}], "source": ["all_states = []\n", "for state in app.get_state_history(config):\n", "    print(state)\n", "    all_states.append(state)\n", "    print(\"--\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["我们可以返回任何一个状态节点，并从那个时候重新开始操作"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["to_replay = all_states[2]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='你能播放一首周杰伦播放量最高的歌曲吗?', additional_kwargs={}, response_metadata={}, id='e2a5acbb-4b30-44d9-9253-04e2248dd617'),\n", "  AIMessage(content='', additional_kwargs={'tool_calls': [{'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'function': {'arguments': '{\"song\":\"周杰伦\"}', 'name': 'play_song_on_qq'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 20, 'prompt_tokens': 86, 'total_tokens': 106, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-aa8fe78a-f8a8-4edd-9f17-729d5dc6fb92-0', tool_calls=[{'name': 'play_song_on_qq', 'args': {'song': '周杰伦'}, 'id': 'call_jXjBMwXhQdWGVwmzCG607Dg3', 'type': 'tool_call'}], usage_metadata={'input_tokens': 86, 'output_tokens': 20, 'total_tokens': 106, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["to_replay.values"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["('action',)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["to_replay.next"]}, {"cell_type": "markdown", "metadata": {}, "source": ["如果想从这个状态节点重播，只需这样"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'messages': [ToolMessage(content='成功在QQ音乐上播放了周杰伦！', name='play_song_on_qq', id='fa758237-ccab-470c-a26a-c342116c990e', tool_call_id='call_jXjBMwXhQdWGVwmzCG607Dg3')]}\n", "{'messages': [AIMessage(content='我已经在QQ音乐上播放了周杰伦的歌曲！你可以去QQ音乐欣赏他的音乐。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 26, 'prompt_tokens': 127, 'total_tokens': 153, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'stop', 'logprobs': None}, id='run-7ddaa00e-a24f-4b2d-ac3e-f33db67a9618-0', usage_metadata={'input_tokens': 127, 'output_tokens': 26, 'total_tokens': 153, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}\n"]}], "source": ["for event in app.stream(None, to_replay.config):\n", "    for v in event.values():\n", "        print(v)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 分叉操作\n", "***\n", "从某个节点开始操作，对执行数据进行分叉"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# 修改最后一个消息的工具调用\n", "# 我们将其从`play_song_on_qq`更改为`play_song_on_163`\n", "last_message = to_replay.values[\"messages\"][-1]\n", "last_message.tool_calls[0][\"name\"] = \"play_song_on_163\"\n", "\n", "branch_config = app.update_state(\n", "    to_replay.config,\n", "    {\"messages\": [last_message]},\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["此时整个图的流就进行了分叉处理"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'messages': [ToolMessage(content='成功在网易云上播放了周杰伦！', name='play_song_on_163', id='cbbd78da-810b-473d-975e-e6a13e361074', tool_call_id='call_jXjBMwXhQdWGVwmzCG607Dg3')]}\n", "{'messages': [AIMessage(content='我已经在网易云音乐上播放了周杰伦的歌曲！如果需要其他平台播放，请告诉我哦。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 27, 'prompt_tokens': 127, 'total_tokens': 154, 'completion_tokens_details': {'accepted_prediction_tokens': 0, 'audio_tokens': 0, 'reasoning_tokens': 0, 'rejected_prediction_tokens': 0}, 'prompt_tokens_details': {'audio_tokens': 0, 'cached_tokens': 0}}, 'model_name': 'gpt-4o-2024-11-20', 'system_fingerprint': 'fp_ded0d14823', 'finish_reason': 'stop', 'logprobs': None}, id='run-1e7b9931-6d84-4940-a065-66d4402f0241-0', usage_metadata={'input_tokens': 127, 'output_tokens': 27, 'total_tokens': 154, 'input_token_details': {'audio': 0, 'cache_read': 0}, 'output_token_details': {'audio': 0, 'reasoning': 0}})]}\n"]}], "source": ["for event in app.stream(None, branch_config):\n", "    for v in event.values():\n", "        print(v)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}