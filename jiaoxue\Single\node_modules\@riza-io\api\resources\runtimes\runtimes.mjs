// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../../resource.mjs";
import { isRequestOptions } from "../../core.mjs";
import * as RevisionsAPI from "./revisions.mjs";
import { Revisions } from "./revisions.mjs";
import { RuntimesPagination } from "../../pagination.mjs";
export class Runtimes extends APIResource {
    constructor() {
        super(...arguments);
        this.revisions = new RevisionsAPI.Revisions(this._client);
    }
    /**
     * Creates a runtime.
     */
    create(body, options) {
        return this._client.post('/v1/runtimes', { body, ...options });
    }
    list(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/runtimes', RuntimesRuntimesPagination, { query, ...options });
    }
    /**
     * Deletes a runtime.
     */
    delete(id, options) {
        return this._client.delete(`/v1/runtimes/${id}`, options);
    }
    /**
     * Retrieves a runtime.
     */
    get(id, options) {
        return this._client.get(`/v1/runtimes/${id}`, options);
    }
}
export class RuntimesRuntimesPagination extends RuntimesPagination {
}
Runtimes.RuntimesRuntimesPagination = RuntimesRuntimesPagination;
Runtimes.Revisions = Revisions;
//# sourceMappingURL=runtimes.mjs.map