{"version": 3, "file": "pagination.js", "sourceRoot": "", "sources": ["src/pagination.ts"], "names": [], "mappings": ";AAAA,sFAAsF;;;AAEtF,oCAA0F;AAY1F,MAAa,iBACX,SAAQ,mBAAkB;IAK1B,YACE,MAAiB,EACjB,QAAkB,EAClB,IAAqC,EACrC,OAA4B;QAE5B,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED,iBAAiB;QACf,OAAO,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC;IACzB,CAAC;IAED,kDAAkD;IAClD,cAAc;QACZ,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QACvB,IAAI,QAAQ,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC;QACzC,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACzD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAC7C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,YAAY;QACV,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,OAAO,IAAI,CAAC;SACb;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QACrC,IAAI,CAAC,EAAE,EAAE;YACP,OAAO,IAAI,CAAC;SACb;QAED,OAAO,EAAE,MAAM,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5C,CAAC;CACF;AA5CD,8CA4CC;AAYD,MAAa,kBACX,SAAQ,mBAAkB;IAK1B,YACE,MAAiB,EACjB,QAAkB,EAClB,IAAsC,EACtC,OAA4B;QAE5B,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;IACtC,CAAC;IAED,iBAAiB;QACf,OAAO,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;IAC7B,CAAC;IAED,kDAAkD;IAClD,cAAc;QACZ,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QACvB,IAAI,QAAQ,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC;QACzC,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACzD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAC7C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,YAAY;QACV,MAAM,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACpB,OAAO,IAAI,CAAC;SACb;QAED,MAAM,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QAC7C,IAAI,CAAC,EAAE,EAAE;YACP,OAAO,IAAI,CAAC;SACb;QAED,OAAO,EAAE,MAAM,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5C,CAAC;CACF;AA5CD,gDA4CC;AAYD,MAAa,eACX,SAAQ,mBAAkB;IAK1B,YACE,MAAiB,EACjB,QAAkB,EAClB,IAAmC,EACnC,OAA4B;QAE5B,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;IAChC,CAAC;IAED,iBAAiB;QACf,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED,kDAAkD;IAClD,cAAc;QACZ,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QACvB,IAAI,QAAQ,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC;QACzC,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACzD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAC7C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,YAAY;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,OAAO,IAAI,CAAC;SACb;QAED,MAAM,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QACvC,IAAI,CAAC,EAAE,EAAE;YACP,OAAO,IAAI,CAAC;SACb;QAED,OAAO,EAAE,MAAM,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5C,CAAC;CACF;AA5CD,0CA4CC;AAYD,MAAa,iBACX,SAAQ,mBAAkB;IAK1B,YACE,MAAiB,EACjB,QAAkB,EAClB,IAAqC,EACrC,OAA4B;QAE5B,KAAK,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QAEvC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;IACpC,CAAC;IAED,iBAAiB;QACf,OAAO,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;IAC5B,CAAC;IAED,kDAAkD;IAClD,cAAc;QACZ,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI;YAAE,OAAO,IAAI,CAAC;QACvB,IAAI,QAAQ,IAAI,IAAI;YAAE,OAAO,IAAI,CAAC,MAAM,CAAC;QACzC,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACzD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC;QAC7C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,YAAY;QACV,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,OAAO,IAAI,CAAC;SACb;QAED,MAAM,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QAC3C,IAAI,CAAC,EAAE,EAAE;YACP,OAAO,IAAI,CAAC;SACb;QAED,OAAO,EAAE,MAAM,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,EAAE,CAAC;IAC5C,CAAC;CACF;AA5CD,8CA4CC"}