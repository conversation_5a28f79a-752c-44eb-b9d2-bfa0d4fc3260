#!/usr/bin/env python3
"""
MongoDB 连接测试脚本
用于验证 MongoDB 容器是否正常工作
"""

from pymongo import MongoClient
from langgraph.store.mongodb import MongoDBStore
import uuid
from datetime import datetime

def test_basic_connection():
    """测试基本的 MongoDB 连接"""
    try:
        # 连接到 MongoDB
        client = MongoClient("mongodb://localhost:27017/")
        
        # 测试连接
        client.admin.command('ping')
        print("✅ MongoDB 基本连接成功！")
        
        # 获取数据库信息
        db_names = client.list_database_names()
        print(f"📊 可用数据库: {db_names}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ MongoDB 基本连接失败: {e}")
        return False

def test_langgraph_store():
    """测试 LangGraph MongoDB Store"""
    try:
        # 创建 MongoDB 存储
        store = MongoDBStore(
            connection_string="mongodb://localhost:27017/",
            database_name="langgraph_test"
        )
        
        # 测试存储操作
        test_data = {
            "message": "Hello MongoDB!",
            "timestamp": datetime.now().isoformat(),
            "user": "test_user"
        }
        
        # 存储数据
        key = str(uuid.uuid4())
        store.put("test_namespace", key, test_data)
        print("✅ 数据存储成功！")
        
        # 读取数据
        retrieved_data = store.get("test_namespace", key)
        print(f"✅ 数据读取成功: {retrieved_data}")
        
        # 搜索测试（如果支持）
        try:
            results = store.search("test_namespace", query="Hello")
            print(f"✅ 搜索功能测试成功，找到 {len(results)} 条结果")
        except Exception as search_error:
            print(f"⚠️  搜索功能不可用: {search_error}")
        
        # 清理测试数据
        store.delete("test_namespace", key)
        print("✅ 测试数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ LangGraph Store 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始 MongoDB 连接测试...")
    print("=" * 50)
    
    # 测试基本连接
    basic_ok = test_basic_connection()
    print()
    
    # 测试 LangGraph Store
    if basic_ok:
        langgraph_ok = test_langgraph_store()
    else:
        print("⚠️  跳过 LangGraph Store 测试（基本连接失败）")
        langgraph_ok = False
    
    print()
    print("=" * 50)
    if basic_ok and langgraph_ok:
        print("🎉 所有测试通过！MongoDB 已准备就绪！")
    elif basic_ok:
        print("⚠️  基本连接正常，但 LangGraph Store 有问题")
    else:
        print("❌ MongoDB 连接失败，请检查容器状态")

if __name__ == "__main__":
    main()
