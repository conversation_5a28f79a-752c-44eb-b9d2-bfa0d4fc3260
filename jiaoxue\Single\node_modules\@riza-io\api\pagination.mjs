// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { AbstractPage } from "./core.mjs";
export class DefaultPagination extends AbstractPage {
    constructor(client, response, body, options) {
        super(client, response, body, options);
        this.data = body.data || [];
    }
    getPaginatedItems() {
        return this.data ?? [];
    }
    // @deprecated Please use `nextPageInfo()` instead
    nextPageParams() {
        const info = this.nextPageInfo();
        if (!info)
            return null;
        if ('params' in info)
            return info.params;
        const params = Object.fromEntries(info.url.searchParams);
        if (!Object.keys(params).length)
            return null;
        return params;
    }
    nextPageInfo() {
        const data = this.getPaginatedItems();
        if (!data.length) {
            return null;
        }
        const id = data[data.length - 1]?.id;
        if (!id) {
            return null;
        }
        return { params: { starting_after: id } };
    }
}
export class RuntimesPagination extends AbstractPage {
    constructor(client, response, body, options) {
        super(client, response, body, options);
        this.runtimes = body.runtimes || [];
    }
    getPaginatedItems() {
        return this.runtimes ?? [];
    }
    // @deprecated Please use `nextPageInfo()` instead
    nextPageParams() {
        const info = this.nextPageInfo();
        if (!info)
            return null;
        if ('params' in info)
            return info.params;
        const params = Object.fromEntries(info.url.searchParams);
        if (!Object.keys(params).length)
            return null;
        return params;
    }
    nextPageInfo() {
        const runtimes = this.getPaginatedItems();
        if (!runtimes.length) {
            return null;
        }
        const id = runtimes[runtimes.length - 1]?.id;
        if (!id) {
            return null;
        }
        return { params: { starting_after: id } };
    }
}
export class ToolsPagination extends AbstractPage {
    constructor(client, response, body, options) {
        super(client, response, body, options);
        this.tools = body.tools || [];
    }
    getPaginatedItems() {
        return this.tools ?? [];
    }
    // @deprecated Please use `nextPageInfo()` instead
    nextPageParams() {
        const info = this.nextPageInfo();
        if (!info)
            return null;
        if ('params' in info)
            return info.params;
        const params = Object.fromEntries(info.url.searchParams);
        if (!Object.keys(params).length)
            return null;
        return params;
    }
    nextPageInfo() {
        const tools = this.getPaginatedItems();
        if (!tools.length) {
            return null;
        }
        const id = tools[tools.length - 1]?.id;
        if (!id) {
            return null;
        }
        return { params: { starting_after: id } };
    }
}
export class SecretsPagination extends AbstractPage {
    constructor(client, response, body, options) {
        super(client, response, body, options);
        this.secrets = body.secrets || [];
    }
    getPaginatedItems() {
        return this.secrets ?? [];
    }
    // @deprecated Please use `nextPageInfo()` instead
    nextPageParams() {
        const info = this.nextPageInfo();
        if (!info)
            return null;
        if ('params' in info)
            return info.params;
        const params = Object.fromEntries(info.url.searchParams);
        if (!Object.keys(params).length)
            return null;
        return params;
    }
    nextPageInfo() {
        const secrets = this.getPaginatedItems();
        if (!secrets.length) {
            return null;
        }
        const id = secrets[secrets.length - 1]?.id;
        if (!id) {
            return null;
        }
        return { params: { starting_after: id } };
    }
}
//# sourceMappingURL=pagination.mjs.map