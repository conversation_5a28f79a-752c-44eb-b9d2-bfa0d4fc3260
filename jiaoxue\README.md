# 课程代码重要说明

****

## 课程概述

本课程专注于 LangChain 和 LangGraph 等智能体框架的实战应用，通过两个渐进式项目帮助学习者掌握 AI Agent 开发技能。课程内容紧跟技术发展，使用最新版本的相关框架和工具，确保学习内容与行业实践同步。

## 开发环境说明

课程提供两种开发环境选择，以适应不同学习场景：

### 1. 在线 IDE (1goto.ai)
* **零配置启动**：无需环境搭建，适合初学者快速上手
* **内置 API 额度**：每位学员提供 150 次免费 API 调用，无需自行申请密钥
* **集成学习资源**：包含课程笔记和参考资料
* **集成智能评测智能体**：像打怪一样升级学习
* **使用限制**：
  * 部分涉及系统安全的代码片段不支持在线运行
  * 这些受限代码均已整合到本地 Jupyter 笔记本中

### 2. 本地 Jupyter 笔记本（下载地址见1goto.ai）
* **适用场景**：数据库操作、异步处理等需要本地环境的高级功能
* **结构化设计**：所有笔记本按模块分步组织，便于理解和实践
* **环境要求**：请严格按照文档中指定的版本要求配置环境

****

## 版本兼容性提示

> **旧版课程用户注意**：LangChain 生态系统更新迭代迅速，旧版课程中的 API、依赖包和语法可能已发生显著变化。强烈建议迁移至最新版本学习。

若需运行旧版内容，请参考固定版本的开源代码库：[https://github.com/neilzhangpro/langchain_course](https://github.com/neilzhangpro/langchain_course)

****

## 技术栈版本规范

课程演示代码基于以下核心依赖版本：
* **langchain**: v0.3
* **langgraph**: v0.3.11
* **openai**: v1.66
* **crewai**: v0.105

****

## 本仓库包含内容：

### 项目一：小浪助手 (单智能体架构)

基于 LangChain 框架构建的单智能体 AI 助手，作为 Agent 开发入门项目。

#### 核心特性
* 🤖 **基础交互系统**：实现人机对话的基本框架
* 📚 **RAG 知识检索**：构建和查询私有知识库
* 🔍 **在线搜索能力**：获取实时信息的搜索工具集成
* 📅 **日历与待办集成**：钉钉日历自然语言交互接口
* 🎭 **情绪感知对话**：基于情绪分析的多轮对话策略
* ⚡ **任务优先级管理**：智能任务调度与优先级分配

### 项目二：小浪助手 (多智能体架构)

基于 LangGraph 框架开发的高级多智能体系统，展示复杂 AI 应用的构建方法。

#### 核心特性
* 🧠 **多智能体协作框架**：基于 LangGraph 的智能体编排与协作机制
* 🗣️ **实时语音合成**：TTS 技术集成，实现自然语音交互
* 👤 **数字人界面**：WebRTC 技术驱动的可视化数字人前端
* 💭 **情感分析链**：深度情绪分析与响应策略调整
* ⏱️ **异步流处理**：高效并发任务处理架构
* 🔧 **扩展工具生态**：模块化工具接入框架设计
* 🔄 **状态管理系统**：会话状态持久化与上下文连贯性保障

****

## 学习路径指南

1. **基础阶段**：完成单智能体项目，掌握 Agent 开发基础
2. **进阶阶段**：学习多智能体架构，理解复杂系统设计原则
3. **专项深化**：根据个人兴趣和需求，深入特定技术模块
   * RAG 系统优化
   * 情感分析与用户体验
   * 多模态交互设计
   * 异步流处理架构
4. **实践拓展**：基于课程框架进行个性化开发与创新

****

## 技术支持资源

* **社区交流**：加入课程官方交流群，与同学和导师互动
* **问题解答**：在课程问答区提交技术疑问，获取专业指导
* **资源共享**：定期更新的补充材料和最佳实践分享
* **每日AI前沿** : 汇聚最新AI前沿技术
* **点击链接加入社群** https://work.weixin.qq.com/ca/cawcdebec30a98cc0d

