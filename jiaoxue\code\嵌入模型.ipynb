{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 嵌入模型\n", "***\n", "- embed_documents\n", "- embed_query\n", "- 嵌入缓存\n", "- 使用国产的嵌入模型"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### embed_documents\n", "****"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/plain": ["(5, 1024)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_openai import OpenAIEmbeddings\n", "embeddings_model = OpenAIEmbeddings(\n", "    model = \"BAAI/bge-m3\",\n", "    api_key = \"sk-khjeakixzbxqaraibcdenuhfrlostygebnqkwvyrtbrxhwmu\",\n", "    base_url = \"https://api.siliconflow.cn/v1\",\n", ")\n", "embeddings = embeddings_model.embed_documents(\n", "    [\n", "        \"Hi there!\",\n", "        \"Oh, hello!\",\n", "        \"What's your name?\",\n", "        \"My friends call me World\",\n", "        \"Hello World!\"\n", "    ]\n", ")\n", "len(embeddings), len(embeddings[0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### embed_query\n", "****"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.005314720794558525, 0.01741221360862255, -0.03883834183216095, 0.02300567924976349, -0.011177637614309788, -0.0512889139354229, 0.017588751390576363, 0.015219427645206451, -0.017588751390576363, 0.0176816675812006, -0.01839710958302021, -0.032390061765909195, 0.030476020649075508, -0.03198123723268509, 0.014299571514129639, -0.02187211997807026, -0.0016910474514588714, -0.018945306539535522, 0.010053370147943497, -0.008543506264686584, 0.0010510975262150168, 0.027168257161974907, -0.017588751390576363, -0.016566690057516098, 0.01975366473197937, 0.020775726065039635, 0.011159054934978485, 0.013026640750467777, -0.019400589168071747, 0.05987422913312912, 0.03318912908434868, 0.028376149013638496, -0.05396485701203346, -0.001130655757151544, -0.006452925503253937, 0.001897782669402659, 0.005848980043083429, 0.029509708285331726, -0.05363036319613457, 0.011642211116850376, -0.02012532390654087, -0.011985995806753635, -0.012450569309294224, -0.01258994173258543, -0.010517943650484085, -0.005124245770275593, 0.017988285049796104, -0.05563732236623764, 0.0020627062767744064, 0.02246677316725254, 0.012515610083937645, 0.024715309962630272, 0.0037258793599903584, -0.010508651845157146, 0.0007334453985095024, -0.002824606839567423, -0.04857580363750458, -0.03748178854584694, -0.029286712408065796, -0.022373858839273453, -0.006987185217440128, 0.015823373571038246, -0.055600155144929886, -0.01701268181204796, -0.021816371008753777, 0.04140279069542885, -0.005519133061170578, 0.015869829803705215, 0.012116076424717903, -0.0012218282790854573, -0.006169535685330629, -0.033133380115032196, 0.012199699878692627, -0.01891743205487728, -0.006332136690616608, -0.004682900849729776, -7.226295565487817e-05, -0.03671988844871521, 0.03447135165333748, 0.008130036294460297, 0.021537626162171364, 0.0011985995806753635, 0.01191166415810585, 0.0044343541376292706, 0.0022508585825562477, 0.07730502635240555, -0.001161433756351471, 0.04493354633450508, -0.000671308662276715, -0.016269363462924957, -0.01642731763422489, -0.009997621178627014, 0.012654981575906277, -0.03400677815079689, 0.009830375202000141, 0.013584128580987453, -0.01191166415810585, -0.028004489839076996, 0.026053281500935555, -0.03183257579803467, -0.02850622870028019, 0.02648068778216839, -0.015572503209114075, 0.0018954598344862461, -0.00603480963036418, -0.006787418853491545, -0.007962789386510849, 0.05069425702095032, 0.009435487911105156, -0.015024306252598763, 0.03664555773139, 0.007679399568587542, 0.009904706850647926, 0.017096303403377533, -0.010954642668366432, -0.004864084534347057, -0.019289091229438782, -0.03746320679783821, 0.00212077796459198, -0.0284133143723011, 0.047535158693790436, 0.056046146899461746, 0.016362277790904045, 0.0018095137784257531, 0.02005099132657051, 0.025960367172956467, -0.029825618490576744, 0.002682911930605769, -0.025049801915884018, -0.0038815115112811327, 0.013426173478364944, 0.01906609535217285, -0.009407613426446915, -0.005022039171308279, 0.018044034019112587, -0.02962120622396469, -0.004034820944070816, 0.05192073434591293, 0.02811598777770996, 0.006792064290493727, 0.022634020075201988, 0.031330835074186325, 0.019586417824029922, -0.009644545614719391, 0.0009047568892128766, -0.022949930280447006, -0.01836923509836197, -0.02027398720383644, 0.029602622613310814, -0.05931674316525459, 0.016306528821587563, -0.015581795014441013, 0.015182261355221272, -0.0075353821739554405, 0.04597419127821922, 0.0140208275988698, 0.021128801628947258, -0.031330835074186325, -0.017700249329209328, -0.020032409578561783, -0.0012287969002500176, -0.0094819450750947, 0.023693248629570007, 0.05768144503235817, -0.01982799731194973, 0.04779532179236412, -0.017821038141846657, 0.047683823853731155, 0.003003467572852969, -0.01573045738041401, 0.006020872388035059, -0.021519044414162636, 0.08154194056987762, -0.011642211116850376, 0.05901941657066345, -0.0170405562967062, 0.011744418181478977, 0.017282133921980858, -0.0003124256618320942, -0.008673586882650852, -0.036534059792757034, 0.03686855360865593, 0.03514033928513527, -0.02367466501891613, 0.003365834942087531, -0.02058989740908146, -0.0020719978492707014, -0.013277510181069374, 0.004576048813760281, -0.0014808280393481255, -0.03190690651535988, -0.004685223568230867, 0.004687546286731958, 0.025384295731782913, 0.04155145213007927, -0.019883744418621063, 0.016390152275562286, -0.02700101025402546, 0.008729335851967335, 0.0031381938606500626, -0.030420271679759026, -0.044227395206689835, 0.003293826011940837, 0.007479633204638958, 0.052069395780563354, -0.002097549382597208, 0.0013588774017989635, -0.0015783883864060044, 0.04441322386264801, -0.027056759223341942, -0.01953066885471344, -0.011688669212162495, -0.060654714703559875, -0.05173490196466446, -0.031163590028882027, 0.04619718715548515, -0.002836221130564809, -0.02646210603415966, -0.010508651845157146, 0.007939561270177364, 0.01708701252937317, -0.010685190558433533, 0.001112072728574276, -0.0035609558690339327, 0.017152052372694016, -0.006192764732986689, 0.037593286484479904, 0.0009262433741241693, -0.017923245206475258, -0.011855915188789368, -0.003240399993956089, 0.019735081121325493, 0.015386673621833324, -0.01836923509836197, -0.00818578526377678, 0.01936342380940914, -0.04471055045723915, 0.02391624264419079, -0.005584173370152712, 0.017179926857352257, 0.0017328590620309114, 0.004327502101659775, 0.002634131582453847, 0.010991808958351612, -0.047981150448322296, 0.0029337815940380096, -0.005495904479175806, 0.009830375202000141, 0.0021753653418272734, -0.017625918611884117, 0.016380861401557922, -0.010164868086576462, -0.033374957740306854, -0.0016051013953983784, 0.0037653681356459856, 0.027484167367219925, -0.02337733842432499, -0.0005272909183986485, 0.02781866118311882, -0.009337927214801311, 0.0037258793599903584, 0.060654714703559875, -0.007851292379200459, 0.020162489265203476, 0.0019256571540609002, -0.04794398322701454, 0.03318912908434868, 0.012376237660646439, -0.005351886618882418, 0.025440044701099396, 0.02391624264419079, -0.019586417824029922, -0.01847144216299057, 0.003015081863850355, -0.012654981575906277, 0.01953066885471344, 0.0015818726969882846, -0.012376237660646439, 0.05675229802727699, -0.0006927952053956687, -0.04385573789477348, -0.029379626736044884, -0.008060350082814693, 0.03136800229549408, -0.0348244272172451, 0.028320400044322014, -0.015284467488527298, 0.02202078327536583, -0.015749040991067886, -0.009913998655974865, 0.0032241400331258774, -0.044450391083955765, 0.043484076857566833, -0.03196265548467636, -0.0410311296582222, 0.02956545725464821, 0.0008217143476940691, -0.15490739047527313, -0.0012613170547410846, 0.011279844678938389, 0.0007828063098713756, 0.017848912626504898, -0.008543506264686584, 0.0023832619190216064, -0.00831121951341629, 0.02532854676246643, -0.06139803305268288, -0.0117258345708251, -0.10161150991916656, -0.007893103174865246, -0.03696146607398987, -0.03082909621298313, 0.008380905725061893, 0.003389063524082303, -0.012952309101819992, 0.042034607380628586, -0.07894032448530197, 0.009342572651803493, 0.030420271679759026, 0.0663410946726799, -0.014336737804114819, 0.013054515235126019, 0.01800686866044998, 0.047237832099199295, -0.03278030455112457, -0.010425029322504997, -0.004450614098459482, -0.010164868086576462, 0.03348645567893982, 0.004348407965153456, -0.013481922447681427, 0.008157910779118538, -0.056046146899461746, 0.005955832079052925, 0.0251055508852005, -0.02231810986995697, 0.0011184606701135635, 0.0028780328575521708, 0.0031172879971563816, -0.01160504575818777, 0.005426218267530203, -0.007159077562391758, 0.006648046430200338, -0.02034831792116165, 0.013100972399115562, -0.007800188846886158, 0.012524900957942009, 0.014169490896165371, 0.016483066603541374, -0.035846490412950516, -0.007210180629044771, -0.013621294870972633, -0.025068385526537895, 0.010768813081085682, 0.03266880661249161, 0.010369280353188515, -0.02737266942858696, -0.037518955767154694, -0.047163501381874084, 0.0007700305432081223, -0.04054797440767288, -0.0036817449145019054, -0.011186929419636726, 0.004531914368271828, -0.0017781549831852317, 0.0377233661711216, 0.0016492358408868313, 0.009904706850647926, 0.0019349486101418734, -0.01699409820139408, -0.02887788787484169, -0.021667707711458206, 0.011028974317014217, -0.011056848801672459, 0.030067196115851402, -0.01078739669173956, -0.07321678102016449, -0.01990232802927494, 0.012850102968513966, 0.015135804191231728, 0.035772159695625305, 0.019697915762662888, -0.010053370147943497, 0.010499360971152782, 0.020627062767744064, -0.027205422520637512, 0.5429191589355469, 0.0241949874907732, -0.0011260099709033966, -0.02140754647552967, 0.01945633813738823, -0.014420361258089542, -0.0026527144946157932, -0.049430619925260544, -0.01799757592380047, -0.023488836362957954, -0.018257737159729004, 0.021370381116867065, 0.029528290033340454, -0.024901138618588448, -0.0032334313727915287, 0.03391386568546295, -0.011242678388953209, 0.04675467684864998, -0.0069221449084579945, 0.012933725491166115, 0.0008170686196535826, 0.009848957881331444, 0.03287322074174881, 0.01888955757021904, -0.018034743145108223, 0.02677801623940468, 0.029305296018719673, 0.01228332333266735, 0.029807034879922867, 0.004464551340788603, 0.007605067919939756, 0.024863973259925842, -0.017913954332470894, -0.00880366750061512, -0.013546963222324848, -0.005444801412522793, -0.022485356777906418, -0.06281033903360367, -0.0039047400932759047, 0.027112508192658424, -0.026276275515556335, 0.002181172603741288, -0.04088246822357178, 0.0018095137784257531, -0.0006591136334463954, 0.0002973270311485976, -0.001064453972503543, 0.019567836076021194, -0.019474919885396957, 0.0038304084446281195, -0.023358754813671112, -0.021816371008753777, -0.03302188217639923, 0.023841911926865578, 0.01575833186507225, -0.029918532818555832, -0.04363274201750755, -0.04578836262226105, 0.004113798029720783, 0.000426246173446998, -0.044747717678546906, 0.03802069276571274, -0.020924389362335205, 0.02029256895184517, 0.0016980160726234317, 0.028338981792330742, -0.026889514178037643, -0.057718608528375626, -0.014643356204032898, 0.028673475608229637, 0.02352600172162056, 0.03473151475191116, -0.02872922457754612, 0.028338981792330742, -0.010378571227192879, 0.0050359764136374, 0.015228718519210815, 0.016798976808786392, -0.005017393734306097, 0.029974281787872314, 0.03060610219836235, 0.018796643242239952, 0.003377449233084917, -0.04337257891893387, 0.015944162383675575, -0.028692059218883514, -0.004536560270935297, -0.021816371008753777, -0.011800166219472885, -0.027465583756566048, 0.0059000831097364426, -0.0013437788002192974, 0.019920911639928818, 0.022894181311130524, 0.0267594326287508, 0.004048757720738649, 0.008678232319653034, -0.014346029609441757, 0.03729595988988876, -0.0075586107559502125, -0.012301906011998653, -0.021054470911622047, 0.019251925870776176, -0.01605566032230854, 0.016761811450123787, 0.07061517238616943, 0.018833808600902557, -0.04779532179236412, 0.019697915762662888, -0.0110847232863307, 0.008613192476332188, 0.0010609696619212627, -0.00018292581080459058, -0.01405799388885498, 0.010861728340387344, 0.024176403880119324, 0.03343070670962334, -0.0245666466653347, -0.023879077285528183, -0.02571878768503666, -0.027725744992494583, 0.0053007835522294044, -0.006527257617563009, 0.028989385813474655, -0.03134942054748535, -0.04597419127821922, 0.012850102968513966, 0.06314482539892197, -0.0035400500055402517, 0.00035104333073832095, 0.004362345207482576, 0.029453959316015244, -0.023284422233700752, 0.04021348059177399, 0.03237148001790047, 0.023879077285528183, 0.031999822705984116, 0.0330033004283905, -0.05102875083684921, 0.02534712851047516, 0.0054633840918540955, -0.00701505970209837, -0.02956545725464821, -0.053333036601543427, 0.02813456952571869, 0.01723567582666874, -0.018480733036994934, 0.0012218282790854573, -0.021611958742141724, -0.019177593290805817, 0.020868640393018723, 0.014689813368022442, -0.0036283188965171576, 0.021816371008753777, 0.03694288432598114, -0.002224145457148552, -0.011772291734814644, 0.004209036007523537, -0.03731454163789749, 0.01160504575818777, 0.002209046855568886, 0.019567836076021194, -0.018257737159729004, 0.055154163390398026, 0.034880176186561584, -0.04378140717744827, 0.011930246837437153, -0.01671535335481167, 0.008306574076414108, 0.04170011729001999, 0.02895222045481205, 0.014940683729946613, 0.014801311306655407, 0.0050963712856173515, -0.08919811248779297, 0.00853421539068222, -0.02542146109044552, 0.004933770280331373, -0.06214134767651558, 0.04891029745340347, 0.03651547431945801, -0.021091636270284653, -0.006699149496853352, -0.012320488691329956, 0.008729335851967335, 0.007414592895656824, 0.0025807057972997427, 0.0032311086542904377, 0.015228718519210815, -0.0007886135135777295, -0.00109871628228575, 0.0392100028693676, -0.01455044187605381, -0.010564400814473629, 0.04039930924773216, 0.017282133921980858, -0.0015644512604922056, 0.06429696828126907, -0.014355320483446121, 0.04537953808903694, 0.005965123418718576, -0.05088008940219879, 0.010425029322504997, -0.02231810986995697, 0.026146195828914642, -0.0091102859005332, -0.013314676471054554, 0.05849909409880638, -0.01967933215200901, 0.021203134208917618, -0.020719977095723152, 0.005965123418718576, 0.012469151988625526, 0.021611958742141724, 0.0028919698670506477, -0.06106353923678398, 0.02389766089618206, 0.0040743094868958, -0.007902394980192184, -0.02073856070637703, -0.008650358766317368, -0.008190430700778961, 0.016613148152828217, -3.745623689610511e-05, 0.020459815859794617, 0.03499167412519455, -0.04552820324897766, -0.007619005162268877, -0.015386673621833324, -0.0019303028238937259, -0.013779249973595142, -0.011186929419636726, 0.008092870004475117, -0.03237148001790047, -0.04207177460193634, 0.0010592275066301227, 0.009384384378790855, 0.035623494535684586, -0.00015200264169834554, 0.0091102859005332, -0.0005179994623176754, -0.003173036966472864, 0.017077721655368805, 0.00900343433022499, -0.03452710062265396, 0.0230800099670887, -0.04500788077712059, 0.0267594326287508, -0.02246677316725254, -0.016418026760220528, -0.0004602181143127382, -0.015303051099181175, -0.018880266696214676, -0.03157241269946098, -0.029063716530799866, 0.019196176901459694, -0.014578316360712051, -0.036980047821998596, 0.0024111364036798477, -0.030476020649075508, 0.007628296501934528, -0.0016387830255553126, 0.02806023880839348, 0.004952353425323963, -0.01432744599878788, -0.025755954906344414, 0.021686289459466934, 0.027093926444649696, 0.016464484855532646, -0.05270121619105339, -0.04385573789477348, -0.02322867512702942, -0.016631729900836945, -0.06128653511404991, 0.007823417894542217, 0.011493547819554806, 0.021128801628947258, 0.06325632333755493, 0.019326256588101387, -0.04552820324897766, -0.07908899337053299, -7.19363015377894e-05, -0.006754898466169834, 0.047758154571056366, -0.027948740869760513, -0.007804834749549627, -0.0016016170848160982, -0.0032473686151206493, -0.022355275228619576, 0.02532854676246643, -0.03265022486448288, 0.02209511585533619, 0.021073052659630775, 0.006806001532822847, 0.01647377572953701, 0.008994142524898052, 0.003377449233084917, 0.005435509607195854, 0.002233437029644847, 0.026257693767547607, -0.015303051099181175, -0.0192333422601223, 0.0030383106786757708, -0.01350050512701273, -0.0021962712053209543, -0.008492403663694859, 0.003333314787596464, 0.035474833101034164, 0.011224095709621906, 0.026146195828914642, -0.009932581335306168, -0.01975366473197937, -0.027911575511097908, -0.054113518446683884, -0.010369280353188515, -0.009756043553352356, -0.005361177958548069, 0.01772812381386757, 0.007075454108417034, 0.009300760924816132, -0.01432744599878788, -0.009068474173545837, -0.05292421206831932, 0.0010749069042503834, -0.010192742571234703, -0.009570213966071606, -0.013073097914457321, -0.022596854716539383, 0.03696146607398987, -0.02707534283399582, -0.01271073054522276, 0.006452925503253937, 0.027242589741945267, -0.002336804522201419, 0.016269363462924957, 0.019939493387937546, 0.016538815572857857, 0.013472630642354488, -0.04051080718636513, -0.05452234297990799, 0.0057607111521065235, 0.011326301842927933, 0.05188356712460518, 0.004485457204282284, -0.003077799454331398, -0.010081244632601738, -0.05890791863203049, 0.002847835421562195, 0.00556094478815794, 0.004970936104655266, -0.011800166219472885, 0.0009297276847064495, -0.012208990752696991, -0.014095159247517586, -0.008069641888141632, 0.004785106983035803, 0.017393631860613823, -0.004225295968353748, 0.055005501955747604, 0.016956932842731476, 0.0039813946932554245, 0.01029494870454073, -0.013760666362941265, -0.03326345980167389, 0.0017874464392662048, -0.0048036896623671055, 0.019028929993510246, 0.046866174787282944, 0.040362145751714706, 0.017848912626504898, 0.02926812879741192, 0.03138658404350281, -0.01228332333266735, 0.009240366518497467, -0.008678232319653034, -0.026220528408885002, -0.0051428284496068954, 0.013268218375742435, -0.023767579346895218, 0.01642731763422489, 0.012887268327176571, 0.018359944224357605, -0.01608353480696678, 0.009412258863449097, 0.026257693767547607, -0.04032497853040695, 0.005793231539428234, 0.03883834183216095, 0.030067196115851402, 0.0032984716817736626, -0.012757187709212303, -0.013277510181069374, 0.01500572357326746, -0.019419170916080475, -0.07061517238616943, -0.012292614206671715, -0.042629264295101166, 0.0012171824928373098, -0.004975582007318735, 0.025086969137191772, -0.008696815930306911, -0.020719977095723152, -0.006067329552024603, -0.0014959266409277916, 0.050954420119524, -0.01078739669173956, 0.011177637614309788, -0.04170011729001999, -0.013258927501738071, 0.00976533442735672, -0.05140041187405586, 0.02729833871126175, 0.02848764695227146, -0.016287947073578835, -0.018880266696214676, -0.038801178336143494, -0.04578836262226105, -0.04571403190493584, -0.011484256945550442, -0.015488879755139351, -0.04876163229346275, -0.008464529179036617, 0.025588707998394966, 0.0003690455632749945, 0.02707534283399582, -0.036608390510082245, 0.006745607126504183, 0.039172835648059845, 0.05088008940219879, -0.004095215350389481, -0.0010133509058505297, 0.031200755387544632, -0.006508674472570419, 0.003965134732425213, 0.03527041897177696, -0.021611958742141724, -0.03694288432598114, -0.021611958742141724, 0.037574704736471176, 0.03755611926317215, -0.0039140316657722, -0.0162972379475832, -0.008576026186347008, -0.00036323838867247105, 0.029435375705361366, 0.05147474259138107, 0.0276699960231781, 0.05195789784193039, -0.0009924451587721705, 0.011177637614309788, -0.005588818807154894, 0.002097549382597208, 0.01689189113676548, 0.05262688547372818, -0.022503940388560295, 0.014949974603950977, -0.027651414275169373, -0.029881367459893227, -0.037351708859205246, -0.013584128580987453, -0.055005501955747604, 0.03413686156272888, 0.006648046430200338, -0.017319299280643463, -0.0402878113090992, -0.013370424509048462, -0.006462217308580875, 0.03883834183216095, 0.007145140320062637, -0.06173252314329147, 0.004685223568230867, 0.004387896507978439, 0.015414548106491566, -0.022225195541977882, -0.03748178854584694, -0.010248491540551186, 0.027762912213802338, -0.00635071936994791, 0.012562067247927189, -0.008315864950418472, -0.011865206994116306, -0.001993020297959447, 0.04002765193581581, -0.018536482006311417, 0.02534712851047516, -0.045119378715753555, -0.042926590889692307, -0.004952353425323963, 0.008510986343026161, -0.0271310918033123, 0.01187449786812067, -0.008692169561982155, -0.0059186662547290325, -0.05548865720629692, -0.021147385239601135, -0.0013135814806446433, -0.025830285623669624, 0.0012160211335867643, -0.03365370258688927, 0.021593375131487846, -0.021983617916703224, -0.008566735312342644, 0.024715309962630272, 0.012738605029881, -0.05887075141072273, 0.01652023382484913, -0.042480599135160446, 0.010499360971152782, -0.03541908413171768, -0.003686390584334731, -0.02413923852145672, 0.005509841721504927, -0.013649169355630875, 0.015238010324537754, -0.0146619388833642, 0.03430410474538803, 0.005444801412522793, -0.05496833473443985, 0.027781493961811066, -0.013983662240207195, -0.02532854676246643, -0.025886034592986107, -0.013816415332257748, -0.022503940388560295, 0.013258927501738071, -0.00633678212761879, -0.010573692619800568, 0.02239244244992733, -0.009412258863449097, 0.005649213679134846, 0.007312386762350798, 0.033523622900247574, -0.027354087680578232, 0.0007264767773449421, 0.03315196558833122, 0.04969077929854393, -0.030513186007738113, 0.00016448805399704725, 0.0006051069940440357, 0.05950257182121277, 0.006959310732781887, 0.013779249973595142, -0.014383194968104362, -0.06139803305268288, -0.027056759223341942, -0.0029779160395264626, -0.009960455819964409, 0.04456188902258873, 0.0024692080914974213, 0.019549252465367317, -0.018434276804327965, 0.006229930557310581, 0.006406468339264393, 0.0100719528272748, -0.019920911639928818, -0.03422977402806282, -0.005430864170193672, 0.02865489199757576, -0.046568844467401505, 0.03309621661901474, -0.012264739722013474, -0.010824562050402164, 0.020552730187773705, 0.012980183586478233, 0.005537715740501881, 0.0013182272668927908, -0.024306485429406166, -0.013890746980905533, 0.002803700976073742, -0.008366968482732773, 0.0005783939850516617, -0.03038310632109642, -0.011744418181478977, -0.007888457737863064, 0.001416949089616537, 0.00030632814741693437, 0.03173965960741043, 0.004083600826561451, -0.0035563099663704634, 0.05225522443652153, -0.032390061765909195, 0.01580478996038437, 0.009486590512096882, -0.006657338235527277, -0.015544628724455833, 0.02187211997807026, 0.015739750117063522, -0.013844289816915989, 0.018685145303606987, -0.015080055221915245, 0.04578836262226105, -0.014875642955303192, 0.00490125035867095, 0.0025528313126415014, -0.03943299874663353, 0.0013379716547206044, 0.026127612218260765, -0.007437821477651596, -0.05634347349405289, -0.0402878113090992, -0.023042844608426094, -0.020571313798427582, -0.0018780382815748453, 0.019121844321489334, -0.039247166365385056, 0.0070104137994349, 0.04586269333958626, 0.03711013123393059, 0.009319344535470009, 0.016956932842731476, 0.021203134208917618, -0.05526566132903099, -0.003272920148447156, 0.012943017296493053, 0.004970936104655266, 0.032761722803115845, 0.005133537109941244, -0.032761722803115845, 0.016585273668169975, -0.0315166637301445, -0.02150046080350876, 0.014689813368022442, 0.01271073054522276, 0.00096108642173931, -0.05255255103111267, -0.06307049840688705, -0.04619718715548515, 0.004766523838043213, 0.01161433756351471, -0.0019976659677922726, -0.014262406155467033, 0.02426931820809841, -0.03528900071978569, 0.0053007835522294044, -0.003939582966268063, 0.009189263917505741, -0.05292421206831932, -0.00618347292765975]\n"]}], "source": ["query_embedding = embeddings_model.embed_query(\"What is the meaning of life?\")\n", "print(query_embedding)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 缓存嵌入结果\n", "****\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["! pip install --upgrade --quiet  langchain-openai faiss-cpu"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langchain.embeddings import CacheBackedEmbeddings\n", "from langchain.storage import LocalFileStore\n", "from langchain_community.document_loaders import TextLoader\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_text_splitters import CharacterTextSplitter\n", "\n", "underlying_embeddings = OpenAIEmbeddings()\n", "\n", "store = LocalFileStore(\"/tmp/langchain_cache\")\n", "\n", "cached_embedder = CacheBackedEmbeddings.from_bytes_store(\n", "    underlying_embeddings, store, namespace=underlying_embeddings.model\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["list(store.yield_keys())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["raw_documents = TextLoader(\"meow.txt\").load()\n", "text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)\n", "documents = text_splitter.split_documents(raw_documents)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建向量存储"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 21.3 ms, sys: 11.5 ms, total: 32.8 ms\n", "Wall time: 2.58 s\n"]}], "source": ["%%time\n", "db = FAISS.from_documents(documents, cached_embedder)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["再次创建将读取缓存，从而加快速度降低成本"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 1.75 ms, sys: 1.28 ms, total: 3.03 ms\n", "Wall time: 2.49 ms\n"]}], "source": ["%%time\n", "db2 = FAISS.from_documents(documents, cached_embedder)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["查看缓存"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["['text-embedding-ada-00263c0fca5-f3da-5691-8a86-2beceea627f5']"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["list(store.yield_keys())[:5]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 使用国产嵌入模型\n", "\n", "****"]}, {"cell_type": "markdown", "metadata": {}, "source": ["筛选模型： https://cloud.siliconflow.cn/models?types=embedding"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "import os\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\")+\"/v1\",\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["(5, 1024)"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["embeddings = embeddings_model.embed_documents(\n", "    [\n", "        \"床前明月光\",\n", "        \"疑是地上霜\",\n", "        \"举头望明月\",\n", "        \"低头思故乡\",\n", "        \"李白《静夜思》\"\n", "    ]\n", ")\n", "len(embeddings), len(embeddings[0])"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}