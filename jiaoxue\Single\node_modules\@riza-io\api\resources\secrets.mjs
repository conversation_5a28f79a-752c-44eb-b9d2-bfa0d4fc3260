// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../resource.mjs";
import { isRequestOptions } from "../core.mjs";
import { SecretsPagination } from "../pagination.mjs";
export class Secrets extends APIResource {
    /**
     * Create a secret in your project.
     */
    create(body, options) {
        return this._client.post('/v1/secrets', { body, ...options });
    }
    list(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/secrets', SecretsSecretsPagination, { query, ...options });
    }
}
export class SecretsSecretsPagination extends SecretsPagination {
}
Secrets.SecretsSecretsPagination = SecretsSecretsPagination;
//# sourceMappingURL=secrets.mjs.map