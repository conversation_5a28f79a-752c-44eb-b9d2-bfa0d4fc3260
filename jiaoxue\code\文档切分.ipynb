{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 文档切分\n", "***\n", "- 按照长度切分\n", "- 按照文本架构进行切分（句子、段落）\n", "- 按照文档格式切分\n", "- 基于语义进行切分"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 长度切分\n", "****"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! pip install langchain-text-splitters"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["file_path='deepseek.pdf'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Ignoring wrong pointing object 10 0 (offset 0)\n", "Ignoring wrong pointing object 21 0 (offset 0)\n", "Ignoring wrong pointing object 32 0 (offset 0)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2. 算⼒需求分析\n", "模型 参数规\n", "模\n", "计算精\n", "度\n", "最低显存需\n", "求 最低算⼒需求\n", "DeepSeek-R1 (671B)671B FP8 ≥890GB 2*XE9680（16*H20\n", "GPU）\n", "DeepSeek-R1-<PERSON><PERSON><PERSON>-\n", "70B 70B BF16 ≥180GB 4*L20 或 2*H20 GPU\n", "三、国产芯⽚与硬件适配⽅案\n", "1. 国内⽣态合作伙伴动态\n", "企业 适配内容 性能对标（vs\n", "NVIDIA）\n", "华为昇\n", "腾\n", "昇腾910B原⽣⽀持R1全系列，提供端到端推理优化\n", "⽅案 等效A100（FP16）\n", "沐曦\n", "GPU\n", "MXN系列⽀持70B模型BF16推理，显存利⽤率提升\n", "30% 等效RTX 3090\n", "海光\n", "DCU 适配V3/R1模型，性能对标NVIDIA A100 等效A100（BF16）\n", "2. 国产硬件推荐配置\n", "模型参数 推荐⽅案 适⽤场景\n", "1.5B 太初T100加速卡 个⼈开发者原型验证\n", "14B 昆仑芯K200集群 企业级复杂任务推理\n", "32B 壁彻算⼒平台+昇腾910B集群 科研计算与多模态处理\n", "四、云端部署替代⽅案\n", "1. 国内云服务商推荐\n", "平台 核⼼优势 适⽤场景\n"]}], "source": ["from langchain_community.document_loaders import PyPDFLoader\n", "\n", "loader = PyPDFLoader(file_path)\n", "pages = []\n", "async for page in loader.alazy_load():\n", "    pages.append(page)\n", "\n", "print(pages[1].page_content)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['2. 算⼒需求分析\\n模型 参数规\\n模\\n计算精\\n度\\n最低显存需\\n求 最低算⼒需求\\nDeepSeek-R1 (671B)671B FP8 ≥890GB 2*XE9680（16*H20\\nGPU）\\nDeepSeek-R1-Distill-\\n70B 70B BF16 ≥180GB 4*L20 或 2*H20 GPU\\n三、国产芯⽚与硬件适配⽅案\\n1. 国内⽣态合作伙伴动态\\n企业 适配内容 性能对标（vs\\nNVIDIA）\\n华为昇\\n腾\\n昇腾910B原⽣⽀持R1全系列，提供端到端推理优化\\n⽅案 等效A100（FP16）\\n沐曦\\nGPU\\nMXN系列⽀持70B模型BF16推理，显存利⽤率提升\\n30% 等效RTX 3090\\n海光\\nDCU 适配V3/R1模型，性能对标NVIDIA A100 等效A100（BF16）\\n2. 国产硬件推荐配置\\n模型参数 推荐⽅案 适⽤场景\\n1.5B 太初T100加速卡 个⼈开发者原型验证\\n14B 昆仑芯K200集群 企业级复杂任务推理\\n32B 壁彻算⼒平台+昇腾910B集群 科研计算与多模态处理\\n四、云端部署替代⽅案\\n1. 国内云服务商推荐\\n平台 核⼼优势 适⽤场景']\n"]}], "source": ["from langchain_text_splitters import CharacterTextSplitter\n", "text_splitter = CharacterTextSplitter.from_tiktoken_encoder(\n", "    encoding_name=\"cl100k_base\", chunk_size=50, chunk_overlap=10\n", ")\n", "texts = text_splitter.split_text(pages[1].page_content)\n", "print(texts)\n", "# docs = text_splitter.create_documents([pages[2].page_content,pages[3].page_content])\n", "# print(docs)\n", "# docs = text_splitter.create_documents([page.page_content for page in pages])\n", "# print(docs)\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 基于文本架构\n", "****"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['2. 算⼒需求分析\\n模型 参数规\\n模\\n计算精\\n度\\n最低显存需\\n求 最低算⼒需求', 'DeepSeek-R1 (671B)671B FP8 ≥890GB 2*XE9680（16*H20', 'GPU）\\nDeepSeek-R1-Distill-', '70B 70B BF16 ≥180GB 4*L20 或 2*H20 GPU', '三、国产芯⽚与硬件适配⽅案\\n1. 国内⽣态合作伙伴动态\\n企业 适配内容 性能对标（vs', 'NVIDIA）\\n华为昇\\n腾\\n昇腾910B原⽣⽀持R1全系列，提供端到端推理优化', '⽅案 等效A100（FP16）\\n沐曦\\nGPU\\nMXN系列⽀持70B模型BF16推理，显存利⽤率提升', '30% 等效RTX 3090\\n海光', 'DCU 适配V3/R1模型，性能对标NVIDIA A100 等效A100（BF16）', '2. 国产硬件推荐配置\\n模型参数 推荐⽅案 适⽤场景', '1.5B 太初T100加速卡 个⼈开发者原型验证\\n14B 昆仑芯K200集群 企业级复杂任务推理', '32B 壁彻算⼒平台+昇腾910B集群 科研计算与多模态处理\\n四、云端部署替代⽅案', '1. 国内云服务商推荐\\n平台 核⼼优势 适⽤场景']\n"]}], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "text_splitter = RecursiveCharacterTextSplitter(chunk_size=50, chunk_overlap=0)\n", "texts = text_splitter.split_text(pages[1].page_content)\n", "print(texts)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 基于文档架构\n", "****\n", "- markdown 根据标题拆分（例如，#、##、###）\n", "- JSON：按对象或数组元素拆分"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["! pip install -qU langchain-text-splitters"]}, {"cell_type": "markdown", "metadata": {}, "source": ["基于markdown格式进行切分"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'Header 1': 'Foo', 'Header 2': 'Bar'}, page_content='Hi this is <PERSON>  \\nHi this is <PERSON>'),\n", " Document(metadata={'Header 1': 'Foo', 'Header 2': 'Bar', 'Header 3': 'Boo'}, page_content='Hi this is <PERSON>'),\n", " Document(metadata={'Header 1': 'Foo', 'Header 2': 'Baz'}, page_content='Hi this is <PERSON>')]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_text_splitters import MarkdownHeaderTextSplitter\n", "markdown_document = \"# Foo\\n\\n    ## Bar\\n\\nHi this is <PERSON>\\n\\nHi this is <PERSON>\\n\\n ### Boo \\n\\n Hi this is <PERSON> \\n\\n ## Baz\\n\\n Hi this is Molly\"\n", "\n", "headers_to_split_on = [\n", "    (\"#\", \"Header 1\"),\n", "    (\"##\", \"Header 2\"),\n", "    (\"###\", \"Header 3\"),\n", "]\n", "\n", "markdown_splitter = MarkdownHeaderTextSplitter(headers_to_split_on)\n", "md_header_splits = markdown_splitter.split_text(markdown_document)\n", "md_header_splits"]}, {"cell_type": "markdown", "metadata": {}, "source": ["基于JSON格式进行切分"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import json\n", "import requests\n", "json_data = requests.get(\"https://api.smith.langchain.com/openapi.json\").json()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'openapi': '3.1.0', 'info': {'title': '<PERSON><PERSON><PERSON>', 'version': '0.1.0'}, 'paths': {'/api/v1/sessions/{session_id}': {'get': {'tags': ['tracer-sessions'], 'summary': 'Read Tracer Session', 'description': 'Get a specific session.'}}}}\n", "{'paths': {'/api/v1/sessions/{session_id}': {'get': {'operationId': 'read_tracer_session_api_v1_sessions__session_id__get', 'security': [{'API Key': []}, {'Tenant ID': []}, {'Bearer Auth': []}]}}}}\n", "{'paths': {'/api/v1/sessions/{session_id}': {'get': {'parameters': [{'name': 'session_id', 'in': 'path', 'required': True, 'schema': {'type': 'string', 'format': 'uuid', 'title': 'Session Id'}}, {'name': 'include_stats', 'in': 'query', 'required': False, 'schema': {'type': 'boolean', 'default': False, 'title': 'Include Stats'}}, {'name': 'accept', 'in': 'header', 'required': False, 'schema': {'anyOf': [{'type': 'string'}, {'type': 'null'}], 'title': 'Accept'}}]}}}}\n"]}], "source": ["from langchain_text_splitters import RecursiveJsonSplitter\n", "\n", "splitter = RecursiveJsonSplitter(max_chunk_size=300)\n", "json_chunks = splitter.split_json(json_data=json_data)\n", "\n", "for chunk in json_chunks[:3]:\n", "    print(chunk)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["page_content='{\"openapi\": \"3.1.0\", \"info\": {\"title\": \"Lang<PERSON>mith\", \"version\": \"0.1.0\"}, \"paths\": {\"/api/v1/sessions/{session_id}\": {\"get\": {\"tags\": [\"tracer-sessions\"], \"summary\": \"Read Tracer Session\", \"description\": \"Get a specific session.\"}}}}'\n", "page_content='{\"paths\": {\"/api/v1/sessions/{session_id}\": {\"get\": {\"operationId\": \"read_tracer_session_api_v1_sessions__session_id__get\", \"security\": [{\"API Key\": []}, {\"Tenant ID\": []}, {\"Bearer Auth\": []}]}}}}'\n", "page_content='{\"paths\": {\"/api/v1/sessions/{session_id}\": {\"get\": {\"parameters\": [{\"name\": \"session_id\", \"in\": \"path\", \"required\": true, \"schema\": {\"type\": \"string\", \"format\": \"uuid\", \"title\": \"Session Id\"}}, {\"name\": \"include_stats\", \"in\": \"query\", \"required\": false, \"schema\": {\"type\": \"boolean\", \"default\": false, \"title\": \"Include Stats\"}}, {\"name\": \"accept\", \"in\": \"header\", \"required\": false, \"schema\": {\"anyOf\": [{\"type\": \"string\"}, {\"type\": \"null\"}], \"title\": \"Accept\"}}]}}}}'\n"]}], "source": ["# 生成langchain Document\n", "docs = splitter.create_documents(texts=[json_data])\n", "\n", "for doc in docs[:3]:\n", "    print(doc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 基于语义切分\n", "*****"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["! pip install --quiet langchain_experimental langchain_openai"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["with open(\"meow.txt\") as f:\n", "    meow = f.read()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "OpenAIError", "evalue": "The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                               <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      2\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mlangchain_openai\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01membeddings\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mi<PERSON><PERSON>\u001b[39;00m OpenAIEmbeddings\n\u001b[32m      3\u001b[39m \u001b[38;5;66;03m# 使用OpenAIEmbeddings进行向量化\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m text_splitter = SemanticChunker(\u001b[43mOpenAIEmbeddings\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m)\n", "    \u001b[31m[... skipping hidden 1 frame]\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\Anaconda3\\envs\\py313\\Lib\\site-packages\\langchain_openai\\embeddings\\base.py:327\u001b[39m, in \u001b[36mOpenAIEmbeddings.validate_environment\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    325\u001b[39m         \u001b[38;5;28mself\u001b[39m.http_client = httpx.Client(proxy=\u001b[38;5;28mself\u001b[39m.openai_proxy)\n\u001b[32m    326\u001b[39m     sync_specific = {\u001b[33m\"\u001b[39m\u001b[33mhttp_client\u001b[39m\u001b[33m\"\u001b[39m: \u001b[38;5;28mself\u001b[39m.http_client}\n\u001b[32m--> \u001b[39m\u001b[32m327\u001b[39m     \u001b[38;5;28mself\u001b[39m.client = \u001b[43mopenai\u001b[49m\u001b[43m.\u001b[49m\u001b[43mOpenAI\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mclient_params\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43msync_specific\u001b[49m\u001b[43m)\u001b[49m.embeddings  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n\u001b[32m    328\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m.async_client:\n\u001b[32m    329\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.openai_proxy \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m.http_async_client:\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\Anaconda3\\envs\\py313\\Lib\\site-packages\\openai\\_client.py:130\u001b[39m, in \u001b[36mOpenAI.__init__\u001b[39m\u001b[34m(self, api_key, organization, project, webhook_secret, base_url, websocket_base_url, timeout, max_retries, default_headers, default_query, http_client, _strict_response_validation)\u001b[39m\n\u001b[32m    128\u001b[39m     api_key = os.environ.get(\u001b[33m\"\u001b[39m\u001b[33mOPENAI_API_KEY\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    129\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m api_key \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m130\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m OpenAIError(\n\u001b[32m    131\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mThe api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    132\u001b[39m     )\n\u001b[32m    133\u001b[39m \u001b[38;5;28mself\u001b[39m.api_key = api_key\n\u001b[32m    135\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m organization \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[31mOpenAIError\u001b[39m: The api_key client option must be set either by passing api_key to the client or by setting the OPENAI_API_KEY environment variable"]}], "source": ["from langchain_experimental.text_splitter import SemanticChunker\n", "from langchain_openai.embeddings import OpenAIEmbeddings\n", "# 使用OpenAIEmbeddings进行向量化\n", "text_splitter = SemanticChunker(OpenAIEmbeddings())"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["meow meow🐱 \n", " meow meow🐱 \n", " meow😻😻\n"]}], "source": ["docs = text_splitter.create_documents([meow])\n", "print(docs[0].page_content)"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}