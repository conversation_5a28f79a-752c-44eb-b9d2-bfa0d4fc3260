{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## LangSmith hub管理提示词\n", "********\n", "- 使用hub加载别人分享的提示词模板\n", "- 使用langsmith sdk来做提示词管理"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](langsmith.png)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install -qU langsmith -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 从hub上加载提示词\n", "*****"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- https://smith.langchain.com 注册并获取API KEY"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import os\n", "LANGCHAIN_API_KEY=os.environ.get(\"LANGCHAIN_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": [" - https://smith.langchain.com/hub 模板聚合广场"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["input_variables=['context', 'question'] input_types={} partial_variables={} metadata={'lc_hub_owner': 'rlm', 'lc_hub_repo': 'rag-prompt', 'lc_hub_commit_hash': '50442af133e61576e74536c6556cefe1fac147cad032f4377b60c436e6cdcb6e'} messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, template=\"You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Use three sentences maximum and keep the answer concise.\\nQuestion: {question} \\nContext: {context} \\nAnswer:\"), additional_kwargs={})]\n"]}], "source": ["from langchain import hub\n", "prompt = hub.pull(\"rlm/rag-prompt\")\n", "print(prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 使用langsmith来管理提示词\n", "*****\n", "- 创建提示词\n", "- 测试提示词\n", "- 迭代提示词"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 创建提示词后上传到langsmith"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["'https://smith.langchain.com/prompts/test1-prompt/467cf1f5?organizationId=793cca2d-5ae8-539f-8476-90d5dd479c7f'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from langsmith import Client\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "\n", "client = Client()\n", "\n", "# Define the prompt\n", "\n", "prompt = ChatPromptTemplate([\n", "(\"system\", \"You are a helpful chatbot.\"),\n", "(\"user\", \"{question}\"),\n", "])\n", "\n", "# Push the prompt\n", "\n", "client.push_prompt(\"test1-prompt\", object=prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 远程拉取自己的提示词并使用deepseek测试"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["messages=[SystemMessage(content='You are a helpful chatbot.', additional_kwargs={}, response_metadata={}), HumanMessage(content='天空是什么颜色的?', additional_kwargs={}, response_metadata={})]\n"]}, {"data": {"text/plain": ["AIMessage(content='天空的颜色主要取决于时间和天气条件：\\n\\n1. **晴朗白天**：呈现蓝色，这是由于阳光中的短波蓝光被大气分子散射（瑞利散射现象）。\\n\\n2. **日出/日落**：常出现橙红色，因为阳光穿透更厚的大气层时蓝光被散射殆尽，剩下长波红光到达地面。\\n\\n3. **阴天**：灰白色，云层中的水滴散射所有波长的可见光。\\n\\n4. **夜晚**：呈现黑色，仅有星光和大气辉光点缀。\\n\\n特殊现象：\\n- 极光出现时会有绿色/紫色光带\\n- 沙尘暴天气可能呈现橙黄色\\n- 月食期间可能出现\"血月\"的暗红色\\n\\n有趣的是，火星天空呈粉橙色，而金星天空是橙黄色，这都与不同行星的大气成分有关。地球的蓝色天空其实是大气层与阳光共同作用的独特光学效果。', additional_kwargs={'refusal': None, 'reasoning_content': '嗯，用户问“天空是什么颜色的？”，这个问题看起来简单，但其实可能有不同的情况需要考虑。首先，我得想想通常情况下的答案。在白天，晴朗的时候，天空是蓝色的，这是因为瑞利散射，阳光中的短波长光线（比如蓝色）更容易被大气中的分子散射，所以整个天空看起来是蓝色的。不过，如果是在日出或日落的时候，太阳的位置低，光线穿过更多的大气层，蓝光被散射掉更多，剩下的红光和黄光会让天空呈现橙色或红色。\\n\\n然后，用户可能是在不同的时间或天气条件下观察天空，所以需要考虑不同的情况。比如阴天的时候，云层比较厚，天空会显得灰蒙蒙的。夜晚的话，天空是黑色的，因为有星星和月亮的光，但大部分区域还是黑暗的。另外，如果是在其他星球上，比如火星，天空的颜色会因为大气成分不同而不同，但用户可能问的是地球上的情况。\\n\\n还要考虑用户的年龄和知识水平。如果是小孩子问的，可能需要用更简单的语言解释，比如“因为阳光和空气中的小颗粒让天空看起来是蓝色的”。如果是成年人，可能更愿意了解科学原理，比如瑞利散射和米氏散射的区别，或者不同天气现象对天空颜色的影响。\\n\\n另外，用户可能没有明确说明他们问的具体情境，比如是否在污染严重的城市，或者是否有特殊天气现象如沙尘暴、雾霾等，这些都会影响天空的颜色。这时候可能需要进一步询问用户的具体情况，或者给出一个全面的回答，涵盖各种可能性。\\n\\n也有可能用户的问题背后有更深层的意图，比如他们可能注意到天空颜色异常，比如灰蒙蒙的，担心环境污染，或者对自然现象感到好奇，想了解更多科学知识。这时候回答不仅要解释颜色原因，还可以扩展相关的知识，比如大气污染对天空颜色的影响，或者极光等特殊现象。\\n\\n还要注意回答的准确性，避免错误的信息。比如，不能简单地说“天空反射海洋的颜色”，这是一个常见的误解，实际上是因为散射。需要纠正这种可能的误解，确保用户得到正确的信息。\\n\\n最后，回答的结构要清晰，先给出直接答案，再分情况讨论不同情境下的颜色变化，最后可以加入一些有趣的事实或扩展知识，让回答更丰富有用。同时保持语言简洁明了，适合不同层次的读者理解。'}, response_metadata={'token_usage': {'completion_tokens': 656, 'prompt_tokens': 15, 'total_tokens': 671, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-R1', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-eb497d62-d4a1-4266-b882-de554b3f4b1c-0', usage_metadata={'input_tokens': 15, 'output_tokens': 656, 'total_tokens': 671, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from langsmith import Client\n", "from langchain_deepseek import ChatDeepSeek\n", "import os\n", "\n", "\n", "client = Client()\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-R1\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", "    )\n", "\n", "# Pull the prompt to use\n", "\n", "# You can also specify a specific commit by passing the commit hash \"my-prompt:<commit-hash>\"\n", "\n", "prompt = client.pull_prompt(\"test1-prompt\")\n", "\n", "# Since our prompt only has one variable we could also pass in the value directly\n", "\n", "# The code below is equivalent to formatted_prompt = prompt.invoke(\"What is the color of the sky?\")\n", "\n", "formatted_prompt = prompt.invoke({\"question\": \"天空是什么颜色的?\"})\n", "print(formatted_prompt)\n", "# Test the prompt\n", "\n", "llm.invoke(formatted_prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 团队迭代提示词"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["'https://smith.langchain.com/prompts/test1-prompt/3aa53ff1?organizationId=793cca2d-5ae8-539f-8476-90d5dd479c7f'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from langsmith import Client\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "# Connect to the LangSmith client\n", "\n", "client = Client()\n", "\n", "# Define the prompt to update\n", "\n", "new_prompt = ChatPromptTemplate([\n", "(\"system\", \"你是一个有趣的机器人. 使用中文回复.\"),\n", "(\"user\", \"{question}\"),\n", "])\n", "\n", "# Push the updated prompt making sure to use the correct prompt name\n", "\n", "# 注意tags可以更方便进行版本管理,你也可以打成v0.0.1这样\n", "\n", "client.push_prompt(\"test1-prompt\", object=new_prompt, tags=[\"Chinese\"])"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}