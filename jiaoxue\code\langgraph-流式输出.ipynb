{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## langgraph - 流式输出\n", "****\n", "- 几种不同的传输模式"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定一个最常见的图"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from typing import TypedDict\n", "from langgraph.graph import StateGraph, START\n", "\n", "\n", "class State(TypedDict):\n", "    topic: str\n", "    joke: str\n", "\n", "\n", "def refine_topic(state: State):\n", "    return {\"topic\": state[\"topic\"] + \"和小狗\"}\n", "\n", "\n", "def generate_joke(state: State):\n", "    return {\"joke\": f\"这是一个关于{state['topic']}的笑话\"}\n", "\n", "\n", "graph = (\n", "    StateGraph(State)\n", "    .add_node(refine_topic)\n", "    .add_node(generate_joke)\n", "    .add_edge(START, \"refine_topic\")\n", "    .add_edge(\"refine_topic\", \"generate_joke\")\n", "    .compile()\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### stream_mode=\"values\"\n", "****"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'topic': '冰激凌'}\n", "{'topic': '冰激凌和小狗'}\n", "{'topic': '冰激凌和小狗', 'joke': '这是一个关于冰激凌和小狗的笑话'}\n"]}], "source": ["for chunk in graph.stream(\n", "    {\"topic\": \"冰激凌\"},\n", "    stream_mode=\"values\",\n", "):\n", "    print(chunk)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### stream_mode=\"updates\"\n", "****"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'refine_topic': {'topic': '冰激凌和小狗'}}\n", "{'generate_joke': {'joke': '这是一个关于冰激凌和小狗的笑话'}}\n"]}], "source": ["for chunk in graph.stream(\n", "    {\"topic\": \"冰激凌\"},\n", "    stream_mode=\"updates\",\n", "):\n", "    print(chunk)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### stream_mode=\"debug\"\n", "****"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'type': 'task', 'timestamp': '2025-03-31T13:51:26.362608+00:00', 'step': 1, 'payload': {'id': '80703f54-1e33-beab-33a5-e919cdd2d07b', 'name': 'refine_topic', 'input': {'topic': '冰激凌'}, 'triggers': ('branch:to:refine_topic', 'start:refine_topic')}}\n", "{'type': 'task_result', 'timestamp': '2025-03-31T13:51:26.362941+00:00', 'step': 1, 'payload': {'id': '80703f54-1e33-beab-33a5-e919cdd2d07b', 'name': 'refine_topic', 'error': None, 'result': [('topic', '冰激凌和小狗')], 'interrupts': []}}\n", "{'type': 'task', 'timestamp': '2025-03-31T13:51:26.363213+00:00', 'step': 2, 'payload': {'id': '13543ef5-5006-ac7f-ebe2-21588197d3c0', 'name': 'generate_joke', 'input': {'topic': '冰激凌和小狗'}, 'triggers': ('branch:to:generate_joke', 'refine_topic')}}\n", "{'type': 'task_result', 'timestamp': '2025-03-31T13:51:26.363800+00:00', 'step': 2, 'payload': {'id': '13543ef5-5006-ac7f-ebe2-21588197d3c0', 'name': 'generate_joke', 'error': None, 'result': [('joke', '这是一个关于冰激凌和小狗的笑话')], 'interrupts': []}}\n"]}], "source": ["for chunk in graph.stream(\n", "    {\"topic\": \"冰激凌\"},\n", "    stream_mode=\"debug\",\n", "):\n", "    print(chunk)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["####  stream_mode=\"messages\"\n", "****"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from langchain_deepseek import ChatDeepSeek\n", "import os\n", "\n", "llm =  ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "\n", "def generate_joke(state: State):\n", "    llm_response = llm.invoke(\n", "        [\n", "            {\"role\": \"user\", \"content\": f\"生成一个关于 {state['topic']}的笑话\"}\n", "        ]\n", "    )\n", "    return {\"joke\": llm_response.content}\n", "\n", "\n", "graph = (\n", "    StateGraph(State)\n", "    .add_node(refine_topic)\n", "    .add_node(generate_joke)\n", "    .add_edge(START, \"refine_topic\")\n", "    .add_edge(\"refine_topic\", \"generate_joke\")\n", "    .compile()\n", ")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["好的|！|这是一个关于|冰淇淋|和小狗的|搞笑|小|笑话|：\n", "\n", "---\n", "\n", "|**笑话|：|贪|吃|的小狗|**\n", "\n", "|夏天|，小明|买了一|支超|大的巧克力|冰淇淋，|正美|滋滋地|舔着|。  \n", "|他家|的小狗|“|豆豆|”眼|巴巴地|坐在旁边|，尾巴|摇|得像螺旋|桨。|  \n", "\n", "小明|：“不行|哦|，狗狗|不能|吃巧克力|！”  \n", "|豆豆|一听|，突然|转身冲|进屋里|，叼|来|一张|**|狗狗|币|**（|Dog|ecoin|）放在|小明脚|边。|  \n", "\n", "小明|：“……|你这是|想|‘|付|钱|’买|冰淇淋？”|  \n", "豆|豆疯狂|点头，|还伸出|爪子指了指|冰淇淋上的|**|坚果碎|**，|汪|了一声|，|仿佛在|说：“|加|料|得|加钱|！”  \n", "\n", "|---\n", "\n", "|（|笑点|：小狗|用虚拟|货币“|付款|”，|还挑剔|冰淇淋配料|😂|）  \n", "\n", "|希望你喜欢|！|"]}], "source": ["for message_chunk, metadata in graph.stream(\n", "    {\"topic\": \"冰激凌\"},\n", "    stream_mode=\"messages\",\n", "):\n", "    if message_chunk.content:\n", "        print(message_chunk.content, end=\"|\", flush=True)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}