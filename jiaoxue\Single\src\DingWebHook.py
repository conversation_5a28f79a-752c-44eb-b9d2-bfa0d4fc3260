#!/usr/bin/env python
from dingtalk_stream import AckMessage, ChatbotMessage, DingTalkStreamClient, Credential,ChatbotHandler,CallbackMessage
from src.Agents import AgentClass
from src.Storage import add_user
from dotenv import load_dotenv as _load_dotenv
_load_dotenv()
import os
import logging


user_storage = {}

class EchoTextHandler(ChatbotHandler):
    def __init__(self):
        super(Chatbot<PERSON>and<PERSON>, self).__init__()

    async def process(self, callback: CallbackMessage):
        #从回调数据中提取聊天消息
        incoming_message = ChatbotMessage.from_dict(callback.data)
        print(incoming_message)
        print(callback.data)
        
        #提取消息文本内容并去除前后空白
        text = incoming_message.text.content.strip()

        #提取用户ID
        userid = callback.data['senderStaffId']

        #将用户ID添加到用户存储中
        add_user("userid",userid)

        #使用AI代理处理用户消息
        # msg = AgentClass().run_agent(text)
        # print(msg)

        #恢复处理后的消息
        # self.reply_text(msg['output'], incoming_message)

        #固定回声回复
        self.reply_text("你说的是："+text, incoming_message)


        #返回成功状态和消息
        return AckMessage.STATUS_OK, 'OK'



def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler("dingtalk_connection.log")
        ]
    )
    return logging.getLogger("DingTalk")

def main():
    logger = setup_logging() #定义日志
    logger.info("Starting DingTalk Stream Client")
    # 从环境变量中获取钉钉的app id和app secret
    logger.info(f"App ID: {os.getenv('DINGDING_ID')}")
    logger.info(f"Using credentials to connect to DingTalk")
    
    try:
        # 创建钉钉客户端
        credential = Credential(os.getenv("DINGDING_ID"), os.getenv("DINGDING_SECRET"))
        client = DingTalkStreamClient(credential)
        logger.info("DingTalk client created successfully")
        
        # 注册回调处理器
        client.register_callback_handler(ChatbotMessage.TOPIC, EchoTextHandler())
        logger.info("Registered callback handler for ChatbotMessage")
        
        # 启动客户端
        logger.info("Starting DingTalk client...")
        client.start_forever()
    except Exception as e:
        logger.error(f"Error connecting to DingTalk: {e}", exc_info=True)


if __name__ == '__main__':
    main()