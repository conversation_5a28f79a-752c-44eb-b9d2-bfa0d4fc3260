# Changelog

## 0.12.0 (2025-06-29)

Full Changelog: [v0.11.0...v0.12.0](https://github.com/riza-io/riza-api-node/compare/v0.11.0...v0.12.0)

### Features

* **api:** api update ([58e2120](https://github.com/riza-io/riza-api-node/commit/58e21202a97b52a6e24c187144f8e75b04989856))
* **api:** api update ([a27fe95](https://github.com/riza-io/riza-api-node/commit/a27fe95b1c33a9eeb4d496e0992ba9c01fbacada))
* **api:** api update ([c3c75d0](https://github.com/riza-io/riza-api-node/commit/c3c75d0866da0e3bf7d9a9dc7a9b23c93b2081c0))
* **client:** add support for endpoint-specific base URLs ([9a94bd6](https://github.com/riza-io/riza-api-node/commit/9a94bd6f17998dc98b5cbeb02127f9417b02857f))


### Bug Fixes

* publish script — handle NPM errors correctly ([2d116b2](https://github.com/riza-io/riza-api-node/commit/2d116b25b827a2b301b2400cae1cf94abca4d4bd))


### Chores

* **ci:** bump node version for release workflows ([17dd90c](https://github.com/riza-io/riza-api-node/commit/17dd90c62450abc18adaec342b32bfe06ffd4485))
* **ci:** enable for pull requests ([5c95bb6](https://github.com/riza-io/riza-api-node/commit/5c95bb68ce74b485b4842458293e71e4ff072b98))
* **docs:** grammar improvements ([1f3bd75](https://github.com/riza-io/riza-api-node/commit/1f3bd75a7c414e43de236a1e6c1f7f6bc50da5eb))
* **docs:** use top-level-await in example snippets ([9f0c5ba](https://github.com/riza-io/riza-api-node/commit/9f0c5ba4a9ef53097f85eee26be87f3d9316a71b))
* improve publish-npm script --latest tag logic ([742f381](https://github.com/riza-io/riza-api-node/commit/742f381a5373382f6bda61d08a7e450df381abe9))
* **internal:** make base APIResource abstract ([0fed38a](https://github.com/riza-io/riza-api-node/commit/0fed38a1c8f25bcb0ef76190f45fdefc7249bede))


### Documentation

* add examples to tsdocs ([abc5e09](https://github.com/riza-io/riza-api-node/commit/abc5e09cb87cd9f552897126b4c51345e7b8e3ef))
* **readme:** fix typo ([e815a96](https://github.com/riza-io/riza-api-node/commit/e815a96453b3de21518f6a4431bb40ac78197485))


### Refactors

* **types:** replace Record with mapped types ([796a521](https://github.com/riza-io/riza-api-node/commit/796a52145c94d2cf4917845701ea0eb36b78b97f))

## 0.11.0 (2025-04-24)

Full Changelog: [v0.10.0...v0.11.0](https://github.com/riza-io/riza-api-node/compare/v0.10.0...v0.11.0)

### Features

* **api:** api update ([cd5c2b3](https://github.com/riza-io/riza-api-node/commit/cd5c2b38a41ab34af5a3257cabcd95bdd93ea7d1))
* **api:** api update ([497bda0](https://github.com/riza-io/riza-api-node/commit/497bda042843a82e604ddbdd33bd741ea1058aef))
* **api:** api update ([2da397e](https://github.com/riza-io/riza-api-node/commit/2da397ed116972d02d427fc348da68f705f23bd4))
* **api:** api update ([1c78e66](https://github.com/riza-io/riza-api-node/commit/1c78e666985e0cd52173ddbdfe31a15a75eec8e4))
* **api:** api update ([#142](https://github.com/riza-io/riza-api-node/issues/142)) ([0eae574](https://github.com/riza-io/riza-api-node/commit/0eae57485bd552b9c79a6c84f76f5ec022e2659d))


### Bug Fixes

* **api:** improve type resolution when importing as a package ([#140](https://github.com/riza-io/riza-api-node/issues/140)) ([d721a53](https://github.com/riza-io/riza-api-node/commit/d721a5347e9c53d4b09ceba7c183a6e674fc517d))
* avoid type error in certain environments ([#136](https://github.com/riza-io/riza-api-node/issues/136)) ([2e06e0e](https://github.com/riza-io/riza-api-node/commit/2e06e0e5122e6c37b40f8be4b3f4be6bd94daffa))
* **client:** send `X-Stainless-Timeout` in seconds ([#138](https://github.com/riza-io/riza-api-node/issues/138)) ([6c12997](https://github.com/riza-io/riza-api-node/commit/6c12997919ece052da4e545304b01f38f7d46ed2))
* **internal:** work around https://github.com/vercel/next.js/issues/76881 ([#137](https://github.com/riza-io/riza-api-node/issues/137)) ([96a4414](https://github.com/riza-io/riza-api-node/commit/96a441478b1b5ee0b824495bc76a86989e963503))
* **mcp:** remove unused tools.ts ([#141](https://github.com/riza-io/riza-api-node/issues/141)) ([57ee3a8](https://github.com/riza-io/riza-api-node/commit/57ee3a86dbca12ce5b2fcb7d651466d6a18535af))


### Chores

* **ci:** add timeout thresholds for CI jobs ([c3ed378](https://github.com/riza-io/riza-api-node/commit/c3ed378b9e45de48dcae2d297b0c01ad7e4e2fea))
* **ci:** only use depot for staging repos ([adde366](https://github.com/riza-io/riza-api-node/commit/adde3665e7c6c4135b40f5c73dcd7ebfb3b566d9))
* **client:** minor internal fixes ([7f5d830](https://github.com/riza-io/riza-api-node/commit/7f5d8301ef0190f20b2b466e2cecdb9bee2f1ae6))
* **exports:** cleaner resource index imports ([#133](https://github.com/riza-io/riza-api-node/issues/133)) ([1420dbc](https://github.com/riza-io/riza-api-node/commit/1420dbc5ef146282dd8e7f1e66123e1f2004ae5b))
* **exports:** stop using path fallbacks ([#135](https://github.com/riza-io/riza-api-node/issues/135)) ([20c022c](https://github.com/riza-io/riza-api-node/commit/20c022cd1b0eab5bbfec75a47601289737334b0e))
* **internal:** add aliases for Record and Array ([#139](https://github.com/riza-io/riza-api-node/issues/139)) ([d737539](https://github.com/riza-io/riza-api-node/commit/d737539d1203ed7d34ce39d6b563a0b35a6bc52e))
* **internal:** codegen related update ([9e4ea63](https://github.com/riza-io/riza-api-node/commit/9e4ea63869d597c51e4a4dbf6210de75f4ba83af))
* **internal:** reduce CI branch coverage ([8171a8b](https://github.com/riza-io/riza-api-node/commit/8171a8b899aaba7b41447be72040b1d7d4b5e24b))
* **internal:** upload builds and expand CI branch coverage ([0535ce8](https://github.com/riza-io/riza-api-node/commit/0535ce86cd29f6f8186cb0f47e38874d5c8ca4a0))

## 0.10.0 (2025-03-14)

Full Changelog: [v0.9.0...v0.10.0](https://github.com/riza-io/riza-api-node/compare/v0.9.0...v0.10.0)

### Features

* **api:** api update ([#129](https://github.com/riza-io/riza-api-node/issues/129)) ([98638cb](https://github.com/riza-io/riza-api-node/commit/98638cb92f96f9a49cd2fe14cecb680d2a113169))


### Bug Fixes

* **exports:** ensure resource imports don't require /index ([#131](https://github.com/riza-io/riza-api-node/issues/131)) ([3e1a232](https://github.com/riza-io/riza-api-node/commit/3e1a232d3477e0a5dce273a545069f275516f88c))


### Chores

* **internal:** codegen related update ([#127](https://github.com/riza-io/riza-api-node/issues/127)) ([8999cc2](https://github.com/riza-io/riza-api-node/commit/8999cc28285ec37890a584b072cc573ee1a4cd92))
* **internal:** remove extra empty newlines ([#130](https://github.com/riza-io/riza-api-node/issues/130)) ([e243ef3](https://github.com/riza-io/riza-api-node/commit/e243ef303963b5150e3af39cb380fecc4f2e4250))

## 0.9.0 (2025-03-11)

Full Changelog: [v0.8.0...v0.9.0](https://github.com/riza-io/riza-api-node/compare/v0.8.0...v0.9.0)

### Features

* add SKIP_BREW env var to ./scripts/bootstrap ([#125](https://github.com/riza-io/riza-api-node/issues/125)) ([4807027](https://github.com/riza-io/riza-api-node/commit/4807027be28f081b077f93e5ba69fa2fc1fd0f75))
* **api:** api update ([#119](https://github.com/riza-io/riza-api-node/issues/119)) ([a898707](https://github.com/riza-io/riza-api-node/commit/a898707e05f955b943d821a64c2649b4f36db9fd))
* **api:** api update ([#121](https://github.com/riza-io/riza-api-node/issues/121)) ([22856d0](https://github.com/riza-io/riza-api-node/commit/22856d05281c35958e907b1150b4c601316b92df))
* **api:** api update ([#124](https://github.com/riza-io/riza-api-node/issues/124)) ([f32ba1b](https://github.com/riza-io/riza-api-node/commit/f32ba1bb2e38e86bc9c4e6f3ad5e5ee15ccafa3c))


### Bug Fixes

* **client:** fix export map for index exports ([#117](https://github.com/riza-io/riza-api-node/issues/117)) ([44a360a](https://github.com/riza-io/riza-api-node/commit/44a360add88039a9d506530b6bef473c2bc53af9))


### Chores

* **internal:** fix devcontainers setup ([#120](https://github.com/riza-io/riza-api-node/issues/120)) ([2359ecf](https://github.com/riza-io/riza-api-node/commit/2359ecf85bfba07a060e3e6791f47baa202e66c5))


### Documentation

* update URLs from stainlessapi.com to stainless.com ([#122](https://github.com/riza-io/riza-api-node/issues/122)) ([c68b6ff](https://github.com/riza-io/riza-api-node/commit/c68b6ff6254b9cb5dd27866f63dbd726548cfedd))

## 0.8.0 (2025-02-05)

Full Changelog: [v0.7.0...v0.8.0](https://github.com/riza-io/riza-api-node/compare/v0.7.0...v0.8.0)

### Features

* **api:** api update ([#107](https://github.com/riza-io/riza-api-node/issues/107)) ([e6f6cfb](https://github.com/riza-io/riza-api-node/commit/e6f6cfb688a78fb7303b416a7c76960f66681424))
* **api:** api update ([#109](https://github.com/riza-io/riza-api-node/issues/109)) ([0d98ed2](https://github.com/riza-io/riza-api-node/commit/0d98ed21182ea36f59c4fe27c274ee5dda49fb54))
* **api:** api update ([#110](https://github.com/riza-io/riza-api-node/issues/110)) ([44950f3](https://github.com/riza-io/riza-api-node/commit/44950f345b4e386efb93339b45767af4ee12be86))
* **api:** api update ([#111](https://github.com/riza-io/riza-api-node/issues/111)) ([b776ef2](https://github.com/riza-io/riza-api-node/commit/b776ef258d3bfdac865e65754bbbdd0d1c090c47))
* **api:** api update ([#112](https://github.com/riza-io/riza-api-node/issues/112)) ([8e9343b](https://github.com/riza-io/riza-api-node/commit/8e9343bdc600195f1a8878f1e4fcc6470cda038b))
* **api:** api update ([#113](https://github.com/riza-io/riza-api-node/issues/113)) ([475c7a8](https://github.com/riza-io/riza-api-node/commit/475c7a8981542bd0915a604d3774fc3e6517d6e3))
* **api:** api update ([#114](https://github.com/riza-io/riza-api-node/issues/114)) ([10983d6](https://github.com/riza-io/riza-api-node/commit/10983d665710273a4a46058afb75464ea586b14d))
* **client:** send `X-Stainless-Timeout` header ([#115](https://github.com/riza-io/riza-api-node/issues/115)) ([82497db](https://github.com/riza-io/riza-api-node/commit/82497db04479549a02479b291cf3ab927ce3568c))

## 0.7.0 (2025-01-23)

Full Changelog: [v0.6.0...v0.7.0](https://github.com/riza-io/riza-api-node/compare/v0.6.0...v0.7.0)

### Features

* **api:** api update ([#105](https://github.com/riza-io/riza-api-node/issues/105)) ([93d32a2](https://github.com/riza-io/riza-api-node/commit/93d32a2fcab691d4f63f0f852db7017dc40a2de9))


### Chores

* **internal:** add test ([#104](https://github.com/riza-io/riza-api-node/issues/104)) ([c0b757e](https://github.com/riza-io/riza-api-node/commit/c0b757e8ab8bf25044bfa69fdf9eed3426807240))
* **internal:** codegen related update ([#102](https://github.com/riza-io/riza-api-node/issues/102)) ([7153533](https://github.com/riza-io/riza-api-node/commit/71535339885d4349d0b5a312fc50b19da3276e3a))

## 0.6.0 (2025-01-16)

Full Changelog: [v0.5.0...v0.6.0](https://github.com/riza-io/riza-api-node/compare/v0.5.0...v0.6.0)

### Features

* **api:** api update ([#99](https://github.com/riza-io/riza-api-node/issues/99)) ([9990602](https://github.com/riza-io/riza-api-node/commit/99906025c3ee20dcb12d2670268f95ac6d8b8c99))

## 0.5.0 (2025-01-10)

Full Changelog: [v0.4.0...v0.5.0](https://github.com/riza-io/riza-api-node/compare/v0.4.0...v0.5.0)

### Features

* **api:** api update ([#94](https://github.com/riza-io/riza-api-node/issues/94)) ([fc44716](https://github.com/riza-io/riza-api-node/commit/fc44716728b13ad8323e7144304fa127334fadc3))
* **api:** api update ([#95](https://github.com/riza-io/riza-api-node/issues/95)) ([7c798db](https://github.com/riza-io/riza-api-node/commit/7c798dbcaafff33f0cf40113da7f5dd23353934d))
* **api:** api update ([#96](https://github.com/riza-io/riza-api-node/issues/96)) ([8601213](https://github.com/riza-io/riza-api-node/commit/860121365b1f224a2ee033ecb6fc5d476bb9bc97))


### Bug Fixes

* **client:** normalize method ([#88](https://github.com/riza-io/riza-api-node/issues/88)) ([6910793](https://github.com/riza-io/riza-api-node/commit/69107939e6ecb7d62ed404ec200ffa97abf62378))


### Chores

* **internal:** codegen related update ([#85](https://github.com/riza-io/riza-api-node/issues/85)) ([ebe5d87](https://github.com/riza-io/riza-api-node/commit/ebe5d87b1f53c5c6ea3b964c0e19e16f41377800))
* **internal:** codegen related update ([#87](https://github.com/riza-io/riza-api-node/issues/87)) ([7e3bd57](https://github.com/riza-io/riza-api-node/commit/7e3bd571e95cd6b901aa0471a1facbeef0c253c1))
* **internal:** codegen related update ([#89](https://github.com/riza-io/riza-api-node/issues/89)) ([81e6d3b](https://github.com/riza-io/riza-api-node/commit/81e6d3bb1499e54f0ec1ab2cf954f1e2680e49b8))
* **internal:** codegen related update ([#90](https://github.com/riza-io/riza-api-node/issues/90)) ([19cd479](https://github.com/riza-io/riza-api-node/commit/19cd479ca966d3e6fdb17f995bb9a08af3f43a24))
* **internal:** codegen related update ([#92](https://github.com/riza-io/riza-api-node/issues/92)) ([9dffbf7](https://github.com/riza-io/riza-api-node/commit/9dffbf7843010f9e218b5683f8db569f5023b179))
* **internal:** codegen related update ([#93](https://github.com/riza-io/riza-api-node/issues/93)) ([34aa05c](https://github.com/riza-io/riza-api-node/commit/34aa05c8bfbf3b390d66ea03fbcf59db39f2dced))
* **internal:** codegen related update ([#97](https://github.com/riza-io/riza-api-node/issues/97)) ([d8c35e7](https://github.com/riza-io/riza-api-node/commit/d8c35e7b881ae4dd72dcf9150ae257c039ff500c))


### Documentation

* minor formatting changes ([#91](https://github.com/riza-io/riza-api-node/issues/91)) ([4e93be3](https://github.com/riza-io/riza-api-node/commit/4e93be3ff8296c6a0382ddeced3a46e884ee2ef6))

## 0.4.0 (2024-12-18)

Full Changelog: [v0.3.0...v0.4.0](https://github.com/riza-io/riza-api-node/compare/v0.3.0...v0.4.0)

### Features

* **api:** api update ([#81](https://github.com/riza-io/riza-api-node/issues/81)) ([96c7a2a](https://github.com/riza-io/riza-api-node/commit/96c7a2a8cbf6f147266c568174755e4678d5bc1a))
* **api:** api update ([#82](https://github.com/riza-io/riza-api-node/issues/82)) ([64a42e6](https://github.com/riza-io/riza-api-node/commit/64a42e69d0d10ddd2518575ca7550a0cf0d86056))
* **api:** api update ([#83](https://github.com/riza-io/riza-api-node/issues/83)) ([9f8cc58](https://github.com/riza-io/riza-api-node/commit/9f8cc581a8d9ab727fe03ef833afcf274494fd2a))


### Chores

* **internal:** bump cross-spawn to v7.0.6 ([#77](https://github.com/riza-io/riza-api-node/issues/77)) ([a58c5d8](https://github.com/riza-io/riza-api-node/commit/a58c5d8d2fdd6fcc228c0da8f54fc9b7a9d2e07e))
* **internal:** fix some typos ([#80](https://github.com/riza-io/riza-api-node/issues/80)) ([cf17388](https://github.com/riza-io/riza-api-node/commit/cf17388bf00de78756291b466bbd53c47d5a2927))
* **internal:** remove unnecessary getRequestClient function ([#75](https://github.com/riza-io/riza-api-node/issues/75)) ([b1cc992](https://github.com/riza-io/riza-api-node/commit/b1cc9921d6a4435e7d08ceb5ad919bb6f8b194de))
* **internal:** update isAbsoluteURL ([#79](https://github.com/riza-io/riza-api-node/issues/79)) ([1b8d020](https://github.com/riza-io/riza-api-node/commit/1b8d0202d4bb41a6ce3507560d9e7fcd4f475f75))
* **types:** nicer error class types + jsdocs ([#78](https://github.com/riza-io/riza-api-node/issues/78)) ([d307171](https://github.com/riza-io/riza-api-node/commit/d3071717aad73e6989d2f11f446c06c72aed22f4))

## 0.3.0 (2024-12-02)

Full Changelog: [v0.2.0...v0.3.0](https://github.com/riza-io/riza-api-node/compare/v0.2.0...v0.3.0)

### Features

* **api:** api update ([#66](https://github.com/riza-io/riza-api-node/issues/66)) ([8b9ea4e](https://github.com/riza-io/riza-api-node/commit/8b9ea4e5cc6d7c77fdfb0d3a678952679e8d5ecf))
* **api:** api update ([#73](https://github.com/riza-io/riza-api-node/issues/73)) ([0233f64](https://github.com/riza-io/riza-api-node/commit/0233f64ee49306a2db9e1f1d2660e61bb799095a))
* **internal:** make git install file structure match npm ([#72](https://github.com/riza-io/riza-api-node/issues/72)) ([1bc5c1f](https://github.com/riza-io/riza-api-node/commit/1bc5c1f27b83966b79b92f814b5bd545cf31e155))


### Chores

* rebuild project due to codegen change ([#64](https://github.com/riza-io/riza-api-node/issues/64)) ([1ee15f3](https://github.com/riza-io/riza-api-node/commit/1ee15f381194a5efe0a12aa64222e420b022f310))
* rebuild project due to codegen change ([#67](https://github.com/riza-io/riza-api-node/issues/67)) ([53df425](https://github.com/riza-io/riza-api-node/commit/53df425c5080662e12ced02e5cade5f992efeb1e))
* rebuild project due to codegen change ([#68](https://github.com/riza-io/riza-api-node/issues/68)) ([b0df832](https://github.com/riza-io/riza-api-node/commit/b0df8329029174670c8d38294444c25e60d74fbd))
* rebuild project due to codegen change ([#69](https://github.com/riza-io/riza-api-node/issues/69)) ([921638c](https://github.com/riza-io/riza-api-node/commit/921638c9629f5bf7dc7f08fef9debaa8a53793ce))
* remove redundant word in comment ([#71](https://github.com/riza-io/riza-api-node/issues/71)) ([9965611](https://github.com/riza-io/riza-api-node/commit/9965611583ecb1d7df5ac19063f6039c47712a23))


### Documentation

* remove suggestion to use `npm` call out ([#70](https://github.com/riza-io/riza-api-node/issues/70)) ([30808da](https://github.com/riza-io/riza-api-node/commit/30808da56c5ffd2b75319cc2ea21652ad4df6ad5))

## 0.2.0 (2024-11-07)

Full Changelog: [v0.1.1...v0.2.0](https://github.com/riza-io/riza-api-node/compare/v0.1.1...v0.2.0)

### Features

* **api:** api update ([#61](https://github.com/riza-io/riza-api-node/issues/61)) ([40c6ac4](https://github.com/riza-io/riza-api-node/commit/40c6ac4f75f0a35cd4ac1d0e29cb8e1f949ad48b))

## 0.1.1 (2024-11-07)

Full Changelog: [v0.1.0-alpha.11...v0.1.1](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.11...v0.1.1)

### Features

* **api:** api update ([#54](https://github.com/riza-io/riza-api-node/issues/54)) ([fab6e7c](https://github.com/riza-io/riza-api-node/commit/fab6e7c330900e29d7d024187bdb452d69300c68))
* **api:** api update ([#56](https://github.com/riza-io/riza-api-node/issues/56)) ([6bf9814](https://github.com/riza-io/riza-api-node/commit/6bf98145e5f0f66868022b3cf60ee70a01a85e45))
* **api:** api update ([#57](https://github.com/riza-io/riza-api-node/issues/57)) ([a932ca9](https://github.com/riza-io/riza-api-node/commit/a932ca9e6a39a1363d0e163a643d66187b1b17c2))
* **api:** api update ([#58](https://github.com/riza-io/riza-api-node/issues/58)) ([3d9b205](https://github.com/riza-io/riza-api-node/commit/3d9b205defc86a1ae869a579dd4365e9ae96815c))
* **api:** manual updates ([#59](https://github.com/riza-io/riza-api-node/issues/59)) ([aa72b1c](https://github.com/riza-io/riza-api-node/commit/aa72b1c4805691ca8ea6d5cd2b9e2c9a777995bb))


### Chores

* **internal:** version bump ([#51](https://github.com/riza-io/riza-api-node/issues/51)) ([fb63ac1](https://github.com/riza-io/riza-api-node/commit/fb63ac1f61cfadd25df5d0ee4f5704a303fd247c))
* rebuild project due to codegen change ([#53](https://github.com/riza-io/riza-api-node/issues/53)) ([f40beb1](https://github.com/riza-io/riza-api-node/commit/f40beb12deba8ad6a483e5794c9927c40785cbcd))

## 0.1.0-alpha.11 (2024-10-30)

Full Changelog: [v0.1.0-alpha.10...v0.1.0-alpha.11](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.10...v0.1.0-alpha.11)

### Features

* **api:** api update ([#46](https://github.com/riza-io/riza-api-node/issues/46)) ([81ef38c](https://github.com/riza-io/riza-api-node/commit/81ef38ca5867a2877dd061ca3a30a02950910f6a))
* **api:** api update ([#47](https://github.com/riza-io/riza-api-node/issues/47)) ([37ce229](https://github.com/riza-io/riza-api-node/commit/37ce2298345c8647002f57cd2ffbc6f9b7b6bbb7))
* **api:** api update ([#49](https://github.com/riza-io/riza-api-node/issues/49)) ([0cf5937](https://github.com/riza-io/riza-api-node/commit/0cf5937deb060a40ea81f3bbe01e68d8c0975f52))
* **api:** api update ([#50](https://github.com/riza-io/riza-api-node/issues/50)) ([bff1611](https://github.com/riza-io/riza-api-node/commit/bff16110e079a1f2854b75fb51d67f0159f919e3))
* **api:** manual updates ([#48](https://github.com/riza-io/riza-api-node/issues/48)) ([3d97170](https://github.com/riza-io/riza-api-node/commit/3d97170b52452f092142646463b24884f2418be3))


### Chores

* **internal:** pass props through internal parser ([#44](https://github.com/riza-io/riza-api-node/issues/44)) ([b9b1d67](https://github.com/riza-io/riza-api-node/commit/b9b1d674a96c297cf8f40a1f442023b0c97c8739))

## 0.1.0-alpha.10 (2024-10-08)

Full Changelog: [v0.1.0-alpha.9...v0.1.0-alpha.10](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.9...v0.1.0-alpha.10)

### Chores

* **internal:** codegen related update ([#41](https://github.com/riza-io/riza-api-node/issues/41)) ([6044c7d](https://github.com/riza-io/riza-api-node/commit/6044c7d6ef7f8df96f3c6ca2cd5ffe8024c0215f))

## 0.1.0-alpha.9 (2024-09-18)

Full Changelog: [v0.1.0-alpha.8...v0.1.0-alpha.9](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.8...v0.1.0-alpha.9)

### Bug Fixes

* **types:** remove leftover polyfill usage ([#38](https://github.com/riza-io/riza-api-node/issues/38)) ([8bdd29e](https://github.com/riza-io/riza-api-node/commit/8bdd29e65f1fd81e953a34dab14793fd88f45325))


### Chores

* **internal:** add dev dependency ([#36](https://github.com/riza-io/riza-api-node/issues/36)) ([e42cce5](https://github.com/riza-io/riza-api-node/commit/e42cce5e3f44b2469f80aaa01aa56974f4142a34))

## 0.1.0-alpha.8 (2024-09-13)

Full Changelog: [v0.1.0-alpha.7...v0.1.0-alpha.8](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.7...v0.1.0-alpha.8)

### Features

* **api:** OpenAPI spec update via Stainless API ([#29](https://github.com/riza-io/riza-api-node/issues/29)) ([8965ebc](https://github.com/riza-io/riza-api-node/commit/8965ebc89180ecc896d6af95fdbf8f47d3448038))


### Bug Fixes

* **errors:** pass message through to APIConnectionError ([#31](https://github.com/riza-io/riza-api-node/issues/31)) ([9bb9446](https://github.com/riza-io/riza-api-node/commit/9bb944629e7565e59c0abe6e092b6227e266f7a7))
* use relative paths ([#28](https://github.com/riza-io/riza-api-node/issues/28)) ([e15394f](https://github.com/riza-io/riza-api-node/commit/e15394fd31d290bddf0acbdf5b244827f015d6e3))


### Chores

* better object fallback behaviour for casting errors ([#32](https://github.com/riza-io/riza-api-node/issues/32)) ([9317296](https://github.com/riza-io/riza-api-node/commit/93172964ae4bcbe5b48724a08171b407ad675e32))
* **ci:** limit release doctor target branches ([#25](https://github.com/riza-io/riza-api-node/issues/25)) ([888dc11](https://github.com/riza-io/riza-api-node/commit/888dc11895d72501383c92748834e2a167bdc312))
* **docs:** use client instead of package name in Node examples ([#23](https://github.com/riza-io/riza-api-node/issues/23)) ([b2dc48c](https://github.com/riza-io/riza-api-node/commit/b2dc48c1f8b74ce49673d5df80eff5036b7eefb1))
* **internal:** codegen related update ([#30](https://github.com/riza-io/riza-api-node/issues/30)) ([d92b6ce](https://github.com/riza-io/riza-api-node/commit/d92b6ceb3915f229f3b0f8a7ebe9e1fc01d2f735))
* **internal:** codegen related update ([#33](https://github.com/riza-io/riza-api-node/issues/33)) ([bf4398d](https://github.com/riza-io/riza-api-node/commit/bf4398d53d01abfb81e96164c9e045fa6d2b089c))
* **internal:** refactor release doctor script ([#26](https://github.com/riza-io/riza-api-node/issues/26)) ([8a8c57f](https://github.com/riza-io/riza-api-node/commit/8a8c57fbe44a6180b5bbc0f4d10be6b6d2a84c25))
* **tests:** update prism version ([#27](https://github.com/riza-io/riza-api-node/issues/27)) ([fc5d2b1](https://github.com/riza-io/riza-api-node/commit/fc5d2b1324dc078ecc9fca4a824f6fc7b958a236))


### Documentation

* update CONTRIBUTING.md ([#34](https://github.com/riza-io/riza-api-node/issues/34)) ([f2852b0](https://github.com/riza-io/riza-api-node/commit/f2852b04e975560677381345c0c0daae728cabba))

## 0.1.0-alpha.7 (2024-07-23)

Full Changelog: [v0.1.0-alpha.6...v0.1.0-alpha.7](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.6...v0.1.0-alpha.7)

### Features

* **api:** OpenAPI spec update via Stainless API ([#17](https://github.com/riza-io/riza-api-node/issues/17)) ([534a73e](https://github.com/riza-io/riza-api-node/commit/534a73ece4c3730ea3a74ba4e65da30901fc0fdd))


### Chores

* **docs:** mention support of web browser runtimes ([#21](https://github.com/riza-io/riza-api-node/issues/21)) ([d538c1e](https://github.com/riza-io/riza-api-node/commit/d538c1e3fd9391f17c629cbbcff3965b06daa74e))
* **docs:** minor update to formatting of API link in README ([#20](https://github.com/riza-io/riza-api-node/issues/20)) ([29367ce](https://github.com/riza-io/riza-api-node/commit/29367ce68e828a54a64e881d7e53ac20ebbe90a5))
* **internal:** codegen related update ([#18](https://github.com/riza-io/riza-api-node/issues/18)) ([6d55d22](https://github.com/riza-io/riza-api-node/commit/6d55d221fae055bacada3d342728a1c0db46cd48))

## 0.1.0-alpha.6 (2024-05-22)

Full Changelog: [v0.1.0-alpha.5...v0.1.0-alpha.6](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.5...v0.1.0-alpha.6)

### Features

* **api:** OpenAPI spec update via Stainless API ([#13](https://github.com/riza-io/riza-api-node/issues/13)) ([8d98f3f](https://github.com/riza-io/riza-api-node/commit/8d98f3f51cb34ac834f890e5125a3d96f586c477))
* **api:** OpenAPI spec update via Stainless API ([#15](https://github.com/riza-io/riza-api-node/issues/15)) ([d4a0b85](https://github.com/riza-io/riza-api-node/commit/d4a0b85ed5ae047196ba49fcb159abe0fb2eaa38))

## 0.1.0-alpha.5 (2024-04-23)

Full Changelog: [v0.1.0-alpha.4...v0.1.0-alpha.5](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.4...v0.1.0-alpha.5)

### Features

* **api:** update via SDK Studio ([#11](https://github.com/riza-io/riza-api-node/issues/11)) ([4cdf292](https://github.com/riza-io/riza-api-node/commit/4cdf29240caf52db2c1b1c2b9e268973e07782f5))

## 0.1.0-alpha.4 (2024-04-19)

Full Changelog: [v0.1.0-alpha.3...v0.1.0-alpha.4](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.3...v0.1.0-alpha.4)

### Features

* **api:** OpenAPI spec update via Stainless API ([#9](https://github.com/riza-io/riza-api-node/issues/9)) ([f18a12a](https://github.com/riza-io/riza-api-node/commit/f18a12a54d3608192e1330d7a8e8c5d7b1deb408))

## 0.1.0-alpha.3 (2024-04-18)

Full Changelog: [v0.1.0-alpha.2...v0.1.0-alpha.3](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.2...v0.1.0-alpha.3)

### Features

* **api:** update via SDK Studio ([#7](https://github.com/riza-io/riza-api-node/issues/7)) ([5cacdee](https://github.com/riza-io/riza-api-node/commit/5cacdee1a6c46e36815f8446b96aa05959238245))

## 0.1.0-alpha.2 (2024-04-18)

Full Changelog: [v0.1.0-alpha.1...v0.1.0-alpha.2](https://github.com/riza-io/riza-api-node/compare/v0.1.0-alpha.1...v0.1.0-alpha.2)

### Features

* **api:** update via SDK Studio ([#3](https://github.com/riza-io/riza-api-node/issues/3)) ([ace9312](https://github.com/riza-io/riza-api-node/commit/ace93122065204e69ffa22f24ec251c14840973e))
* **api:** update via SDK Studio ([#5](https://github.com/riza-io/riza-api-node/issues/5)) ([10603fb](https://github.com/riza-io/riza-api-node/commit/10603fb7161bab64ee6fe8ec2da5aba122ec45a2))
* **api:** update via SDK Studio ([#6](https://github.com/riza-io/riza-api-node/issues/6)) ([13c7d13](https://github.com/riza-io/riza-api-node/commit/13c7d133b83bcb5dc51e49a91cc801e6ad675aa7))

## 0.1.0-alpha.1 (2024-04-17)

Full Changelog: [v0.0.1-alpha.0...v0.1.0-alpha.1](https://github.com/riza-io/riza-api-node/compare/v0.0.1-alpha.0...v0.1.0-alpha.1)

### Features

* **api:** update via SDK Studio ([9cc5c72](https://github.com/riza-io/riza-api-node/commit/9cc5c72c33e7edb1606a17117d6e6b22d556cca7))


### Chores

* go live ([#1](https://github.com/riza-io/riza-api-node/issues/1)) ([c330674](https://github.com/riza-io/riza-api-node/commit/c330674ecccde7556ac5e1e4052511b1562f6bb1))
