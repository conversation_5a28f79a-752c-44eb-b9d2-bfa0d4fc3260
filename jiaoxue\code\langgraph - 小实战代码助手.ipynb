{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 使用langgraph构建代码助手\n", "******\n", "- 针对特定问题参考材料迭代式的生成代码\n", "- 从用户指定的一组文档开始\n", "- 使用长上下文 LLM 来提取它并执行 RAG 来回答基于它的问题\n", "- 调用一个工具来生成结构化输出\n", "- 将解决方案返回给用户之前，将执行两个单元测试（检查导入和代码执行）\n", "![](langgraph4.png)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: langchain_community in ./.venv/lib/python3.13/site-packages (0.3.20)\n", "Requirement already satisfied: langchain-openai in ./.venv/lib/python3.13/site-packages (0.3.11)\n", "Requirement already satisfied: langchain-deepseek in ./.venv/lib/python3.13/site-packages (0.1.3)\n", "Requirement already satisfied: langchain in ./.venv/lib/python3.13/site-packages (0.3.22)\n", "Requirement already satisfied: langgraph in ./.venv/lib/python3.13/site-packages (0.3.21)\n", "Requirement already satisfied: bs4 in ./.venv/lib/python3.13/site-packages (0.0.2)\n", "Collecting langchain-anthropic\n", "  Downloading langchain_anthropic-0.3.10-py3-none-any.whl.metadata (1.9 kB)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.45 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.3.49)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in ./.venv/lib/python3.13/site-packages (from langchain_community) (2.0.38)\n", "Requirement already satisfied: requests<3,>=2 in ./.venv/lib/python3.13/site-packages (from langchain_community) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in ./.venv/lib/python3.13/site-packages (from langchain_community) (6.0.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in ./.venv/lib/python3.13/site-packages (from langchain_community) (3.10.11)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in ./.venv/lib/python3.13/site-packages (from langchain_community) (9.0.0)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.6.7)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in ./.venv/lib/python3.13/site-packages (from langchain_community) (2.8.1)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.3.13)\n", "Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.4.0)\n", "Requirement already satisfied: numpy<3,>=1.26.2 in ./.venv/lib/python3.13/site-packages (from langchain_community) (1.26.4)\n", "Requirement already satisfied: openai<2.0.0,>=1.68.2 in ./.venv/lib/python3.13/site-packages (from langchain-openai) (1.69.0)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in ./.venv/lib/python3.13/site-packages (from langchain-openai) (0.9.0)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.7 in ./.venv/lib/python3.13/site-packages (from langchain) (0.3.7)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in ./.venv/lib/python3.13/site-packages (from langchain) (2.10.6)\n", "Requirement already satisfied: langgraph-checkpoint<3.0.0,>=2.0.10 in ./.venv/lib/python3.13/site-packages (from langgraph) (2.0.23)\n", "Requirement already satisfied: langgraph-prebuilt<0.2,>=0.1.1 in ./.venv/lib/python3.13/site-packages (from langgraph) (0.1.2)\n", "Requirement already satisfied: langgraph-sdk<0.2.0,>=0.1.42 in ./.venv/lib/python3.13/site-packages (from langgraph) (0.1.55)\n", "Requirement already satisfied: xxhash<4.0.0,>=3.5.0 in ./.venv/lib/python3.13/site-packages (from langgraph) (3.5.0)\n", "Requirement already satisfied: beautifulsoup4 in ./.venv/lib/python3.13/site-packages (from bs4) (4.13.3)\n", "Collecting anthropic<1,>=0.49.0 (from langchain-anthropic)\n", "  Using cached anthropic-0.49.0-py3-none-any.whl.metadata (24 kB)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (2.5.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (25.1.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (6.1.0)\n", "Requirement already satisfied: yarl<2.0,>=1.12.0 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.18.3)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in ./.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic) (4.8.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in ./.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in ./.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic) (0.8.2)\n", "Requirement already satisfied: sniffio in ./.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.10 in ./.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic) (4.12.2)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in ./.venv/lib/python3.13/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in ./.venv/lib/python3.13/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (0.9.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.45->langchain_community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.45->langchain_community) (24.2)\n", "Requirement already satisfied: ormsgpack<2.0.0,>=1.8.0 in ./.venv/lib/python3.13/site-packages (from langgraph-checkpoint<3.0.0,>=2.0.10->langgraph) (1.9.1)\n", "Requirement already satisfied: orjson>=3.10.1 in ./.venv/lib/python3.13/site-packages (from langgraph-sdk<0.2.0,>=0.1.42->langgraph) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (0.23.0)\n", "Requirement already satisfied: tqdm>4 in ./.venv/lib/python3.13/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai) (4.67.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain) (2.27.2)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in ./.venv/lib/python3.13/site-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain_community) (1.0.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (2025.1.31)\n", "Requirement already satisfied: regex>=2022.1.18 in ./.venv/lib/python3.13/site-packages (from tiktoken<1,>=0.7->langchain-openai) (2024.11.6)\n", "Requirement already satisfied: soupsieve>1.2 in ./.venv/lib/python3.13/site-packages (from beautifulsoup4->bs4) (2.6)\n", "Requirement already satisfied: httpcore==1.* in ./.venv/lib/python3.13/site-packages (from httpx<1,>=0.23.0->anthropic<1,>=0.49.0->langchain-anthropic) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in ./.venv/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->anthropic<1,>=0.49.0->langchain-anthropic) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in ./.venv/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.45->langchain_community) (3.0.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in ./.venv/lib/python3.13/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community) (1.0.0)\n", "Requirement already satisfied: propcache>=0.2.0 in ./.venv/lib/python3.13/site-packages (from yarl<2.0,>=1.12.0->aiohttp<4.0.0,>=3.8.3->langchain_community) (0.3.0)\n", "Downloading langchain_anthropic-0.3.10-py3-none-any.whl (25 kB)\n", "Using cached anthropic-0.49.0-py3-none-any.whl (243 kB)\n", "Installing collected packages: anthropic, langchain-anthropic\n", "Successfully installed anthropic-0.49.0 langchain-anthropic-0.3.10\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install -U langchain_community langchain-openai langchain-deepseek langchain langgraph bs4 langchain-anthropic"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 加载LCEL文档\n", "****"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["from bs4 import BeautifulSoup as Soup\n", "from langchain_community.document_loaders.recursive_url_loader import RecursiveUrlLoader\n", "\n", "# LCEL docs\n", "url = \"https://python.langchain.com/docs/concepts/lcel/\"\n", "loader = RecursiveUrlLoader(\n", "    url=url, max_depth=20, extractor=lambda x: Soup(x, \"html.parser\").text\n", ")\n", "docs = loader.load()\n", "\n", "d_sorted = sorted(docs, key=lambda x: x.metadata[\"source\"])\n", "d_reversed = list(reversed(d_sorted))\n", "concatenated_content = \"\\n\\n\\n --- \\n\\n\\n\".join(\n", "    [doc.page_content for doc in d_reversed]\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["设置大模型"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["code(prefix='在LCEL中构建RAG（Retrieval Augmented Generation）链通常涉及以下步骤：1) 使用检索器从向量存储中获取相关文档，2) 将这些文档与用户输入结合生成提示，3) 将提示传递给LLM生成最终答案。以下是一个完整的示例代码，展示了如何构建一个简单的RAG链。', imports='from langchain_core.runnables import RunnablePassthrough\\nfrom langchain_core.prompts import ChatPromptTemplate\\nfrom langchain_core.output_parsers import StrOutputParser\\nfrom langchain_community.vectorstores import FAISS\\nfrom langchain_community.embeddings import OpenAIEmbeddings\\nfrom langchain_community.llms import OpenAI', code='# 假设已经有一个向量存储（FAISS）\\nvectorstore = FAISS.from_texts(\\n    [\"LangChain is a framework for building applications with LLMs.\", \"RAG stands for Retrieval Augmented Generation.\"],\\n    embedding=OpenAIEmbeddings()\\n)\\nretriever = vectorstore.as_retriever()\\n\\n# 定义提示模板\\nprompt = ChatPromptTemplate.from_template(\\n    \"\"\"Answer the question based only on the following context:\\n    {context}\\n\\n    Question: {question}\\n    \"\"\"\\n)\\n\\n# 定义LLM\\nllm = OpenAI()\\n\\n# 构建RAG链\\nrag_chain = (\\n    {\"context\": retriever, \"question\": RunnablePassthrough()}\\n    | prompt\\n    | llm\\n    | StrOutputParser()\\n)\\n\\n# 调用链\\nresult = rag_chain.invoke(\"What is RAG?\")\\nprint(result)')"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_deepseek import ChatDeepSeek\n", "from pydantic import BaseModel, Field\n", "import os\n", "\n", "### OpenAI\n", "\n", "# 评分提示\n", "code_gen_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"你是一位精通LCEL（LangChain表达式语言）的编程助手。\\n \n", "    这里是LCEL文档的完整集合：\\n ------- \\n  {context} \\n ------- \\n 请根据\n", "    上述提供的文档回答用户问题。确保你提供的任何代码都可以执行，\\n \n", "    包含所有必要的导入和已定义的变量。请按照以下结构组织你的回答：首先描述代码解决方案，\\n\n", "    然后列出导入语句，最后给出功能完整的代码块。以下是用户问题：\"\"\",\n", "        ),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "\n", "# 数据模型\n", "class code(BaseModel):\n", "    \"\"\"LCEL问题代码解决方案的模式。\"\"\"\n", "\n", "    prefix: str = Field(description=\"问题和解决方案的描述\")\n", "    imports: str = Field(description=\"代码块导入语句\")\n", "    code: str = Field(description=\"不包括导入语句的代码块\")\n", "\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "code_gen_chain_oai = code_gen_prompt | llm.with_structured_output(code)\n", "question = \"如何在LCEL中构建RAG链？\"\n", "solution = code_gen_chain_oai.invoke(\n", "    {\"context\": concatenated_content, \"messages\": [(\"user\", question)]}\n", ")\n", "solution\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["from langchain_anthropic import ChatAnthropic\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "### Anthropic\n", "\n", "# 强制使用工具的提示\n", "code_gen_prompt_claude = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"<instructions> 你是一位精通LCEL（LangChain表达式语言）的编程助手。\\n \n", "    这里是LCEL文档：\\n ------- \\n  {context} \\n ------- \\n 请根据上述提供的文档回答用户问题。\\n \n", "    确保你提供的任何代码都可以执行，包含所有必要的导入和已定义的变量。\\n\n", "    按以下结构组织你的回答：1) 描述代码解决方案的前言，2) 导入语句，3) 功能完整的代码块。\\n\n", "    调用code工具来正确构建输出。</instructions> \\n 以下是用户问题：\"\"\",\n", "        ),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "\n", "# 大语言模型\n", "# 注意选择使用claude-3-opus-20240229模型,因为其上下文窗口更大\n", "expt_llm = \"claude-3-opus-20240229\"\n", "llm = ChatAnthropic(\n", "    model=expt_llm,\n", "    api_key=os.environ.get(\"ANTHROPIC_API_KEY\"),\n", "    base_url=os.environ.get(\"ANTHROPIC_BASE_URL\"),\n", "    default_headers={\"anthropic-beta\": \"tools-2024-04-04\"},\n", ")\n", "\n", "structured_llm_claude = llm.with_structured_output(code, include_raw=True)\n", "\n", "\n", "# 可选：检查工具使用是否出现错误\n", "def check_claude_output(tool_output):\n", "    \"\"\"检查解析错误或未能调用工具\"\"\"\n", "\n", "    # 解析错误\n", "    if tool_output[\"parsing_error\"]:\n", "        # 报告输出和解析错误\n", "        print(\"解析错误！\")\n", "        raw_output = str(tool_output[\"raw\"].content)\n", "        error = tool_output[\"parsing_error\"]\n", "        raise ValueError(\n", "            f\"解析输出时出错！请确保调用工具。输出：{raw_output}。\\n 解析错误：{error}\"\n", "        )\n", "\n", "    # 未调用工具\n", "    elif not tool_output[\"parsed\"]:\n", "        print(\"未能调用工具！\")\n", "        raise ValueError(\n", "            \"你没有使用提供的工具！请确保调用工具来构建输出。\"\n", "        )\n", "    return tool_output\n", "\n", "\n", "# 带输出检查的链\n", "code_chain_claude_raw = (\n", "    code_gen_prompt_claude | structured_llm_claude | check_claude_output\n", ")\n", "\n", "\n", "def insert_errors(inputs):\n", "    \"\"\"在消息中插入工具解析错误\"\"\"\n", "\n", "    # 获取错误\n", "    error = inputs[\"error\"]\n", "    messages = inputs[\"messages\"]\n", "    messages += [\n", "        (\n", "            \"assistant\",\n", "            f\"重试。你需要修复解析错误：{error} \\n\\n 你必须调用提供的工具。\",\n", "        )\n", "    ]\n", "    return {\n", "        \"messages\": messages,\n", "        \"context\": inputs[\"context\"],\n", "    }\n", "\n", "\n", "# 这将作为后备链运行\n", "fallback_chain = insert_errors | code_chain_claude_raw\n", "N = 3  # 最大重试次数\n", "code_gen_chain_re_try = code_chain_claude_raw.with_fallbacks(\n", "    fallbacks=[fallback_chain] * N, exception_key=\"error\"\n", ")\n", "\n", "\n", "def parse_output(solution):\n", "    \"\"\"当我们在结构化输出中添加'include_raw=True'时，\n", "    它将返回一个包含'raw'、'parsed'、'parsing_error'的字典。\"\"\"\n", "\n", "    return solution[\"parsed\"]\n", "\n", "\n", "# 可选：带重试功能，用于纠正未能调用工具的情况\n", "code_gen_chain = code_gen_chain_re_try | parse_output\n", "\n", "# 不重试\n", "code_gen_chain = code_gen_prompt_claude | structured_llm_claude | parse_output\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["测试"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["code(prefix='要在LCEL中构建一个基本的RAG（Retrieval Augmented Generation）链，可以这样做：\\n\\n1. 使用一个向量存储作为检索器(Retriever)，用于检索与查询最相关的文档。\\n2. 将检索器与一个文档转换器(DocumentTransformer)结合，将检索到的文档格式化为字符串。 \\n3. 使用一个提示模板(PromptTemplate)将查询和格式化后的文档组合成一个提示。\\n4. 将提示作为输入传给一个语言模型(LanguageModel)，生成最终的回答。\\n\\n下面是具体的代码实现：', imports='from langchain_core.runnables import RunnableSequence\\nfrom langchain_core.retrievers import VectorStoreRetriever\\nfrom langchain_core.transformers import TextSplitter, DocumentTransformer  \\nfrom langchain_core.prompts import PromptTemplate\\nfrom langchain.chat_models import ChatOpenAI', code='# 假设已经有一个向量存储vs\\nretriever = VectorStoreRetriever.from_langchain(vs)\\n\\n# 文档转换器，将文档分割并格式化为字符串\\nsplitter = TextSplitter(chunk_size=1000, chunk_overlap=0) \\ndoc_transformer = DocumentTransformer(text_splitter=splitter)\\n\\n# 定义提示模板\\ntemplate = \"\"\"基于以下背景信息回答问题:\\n{context}  \\n\\n问题: {question}\\n\"\"\"\\nprompt = PromptTemplate.from_format(template)\\n\\n# 定义语言模型\\nllm = ChatOpenAI()\\n\\n# 组装RAG链\\nrag_chain = retriever | doc_transformer | prompt | llm\\n\\n# 运行RAG链\\nresult = rag_chain.invoke({\"question\": \"某个查询问题\"})')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# Test\n", "question = \"在LCEL中如何构建一个RAG链?\"\n", "solution = code_gen_chain.invoke(\n", "    {\"context\": concatenated_content, \"messages\": [(\"user\", question)]}\n", ")\n", "solution"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义state"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "from typing_extensions import TypedDict\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    表示我们图的状态。\n", "\n", "    属性：\n", "        error : 用于控制流的二进制标志，表示是否触发了测试错误\n", "        messages : 包含用户问题、错误消息、推理过程\n", "        generation : 代码解决方案\n", "        iterations : 尝试次数\n", "    \"\"\"\n", "\n", "    error: str\n", "    messages: List\n", "    generation: str\n", "    iterations: int\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建节点"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["### 参数\n", "\n", "# 最大尝试次数\n", "max_iterations = 3\n", "# 反思\n", "# flag = 'reflect'\n", "flag = \"do not reflect\"  # 不进行反思\n", "\n", "### 节点\n", "\n", "\n", "def generate(state: GraphState):\n", "    \"\"\"\n", "    生成代码解决方案\n", "\n", "    参数：\n", "        state (dict): 当前图状态\n", "\n", "    返回：\n", "        state (dict): 向状态添加新键，generation\n", "    \"\"\"\n", "\n", "    print(\"---正在生成代码解决方案---\")\n", "\n", "    # 状态\n", "    messages = state[\"messages\"]\n", "    iterations = state[\"iterations\"]\n", "    error = state[\"error\"]\n", "\n", "    # 我们因错误被重新路由到生成\n", "    if error == \"yes\":\n", "        messages += [\n", "            (\n", "                \"user\",\n", "                \"现在，请重试。调用code工具来构建包含前言、导入和代码块的输出：\",\n", "            )\n", "        ]\n", "\n", "    # 解决方案\n", "    code_solution = code_gen_chain.invoke(\n", "        {\"context\": concatenated_content, \"messages\": messages}\n", "    )\n", "    messages += [\n", "        (\n", "            \"assistant\",\n", "            f\"{code_solution.prefix} \\n 导入: {code_solution.imports} \\n 代码: {code_solution.code}\",\n", "        )\n", "    ]\n", "\n", "    # 增加计数\n", "    iterations = iterations + 1\n", "    return {\"generation\": code_solution, \"messages\": messages, \"iterations\": iterations}\n", "\n", "\n", "def code_check(state: GraphState):\n", "    \"\"\"\n", "    检查代码\n", "\n", "    参数：\n", "        state (dict): 当前图状态\n", "\n", "    返回：\n", "        state (dict): 向状态添加新键，error\n", "    \"\"\"\n", "\n", "    print(\"---正在检查代码---\")\n", "\n", "    # 状态\n", "    messages = state[\"messages\"]\n", "    code_solution = state[\"generation\"]\n", "    iterations = state[\"iterations\"]\n", "\n", "    # 获取解决方案组件\n", "    imports = code_solution.imports\n", "    code = code_solution.code\n", "\n", "    # 检查导入\n", "    try:\n", "        exec(imports)\n", "    except Exception as e:\n", "        print(\"---代码导入检查：失败---\")\n", "        error_message = [(\"user\", f\"你的解决方案未通过导入测试：{e}\")]\n", "        messages += error_message\n", "        return {\n", "            \"generation\": code_solution,\n", "            \"messages\": messages,\n", "            \"iterations\": iterations,\n", "            \"error\": \"yes\",\n", "        }\n", "\n", "    # 检查执行\n", "    try:\n", "        exec(imports + \"\\n\" + code)\n", "    except Exception as e:\n", "        print(\"---代码块检查：失败---\")\n", "        error_message = [(\"user\", f\"你的解决方案未通过代码执行测试：{e}\")]\n", "        messages += error_message\n", "        return {\n", "            \"generation\": code_solution,\n", "            \"messages\": messages,\n", "            \"iterations\": iterations,\n", "            \"error\": \"yes\",\n", "        }\n", "\n", "    # 无错误\n", "    print(\"---无代码测试失败---\")\n", "    return {\n", "        \"generation\": code_solution,\n", "        \"messages\": messages,\n", "        \"iterations\": iterations,\n", "        \"error\": \"no\",\n", "    }\n", "\n", "\n", "def reflect(state: GraphState):\n", "    \"\"\"\n", "    对错误进行反思\n", "\n", "    参数：\n", "        state (dict): 当前图状态\n", "\n", "    返回：\n", "        state (dict): 向状态添加新键，generation\n", "    \"\"\"\n", "\n", "    print(\"---正在生成代码解决方案---\")\n", "\n", "    # 状态\n", "    messages = state[\"messages\"]\n", "    iterations = state[\"iterations\"]\n", "    code_solution = state[\"generation\"]\n", "\n", "    # 提示反思\n", "\n", "    # 添加反思\n", "    reflections = code_gen_chain.invoke(\n", "        {\"context\": concatenated_content, \"messages\": messages}\n", "    )\n", "    messages += [(\"assistant\", f\"以下是对错误的反思：{reflections}\")]\n", "    return {\"generation\": code_solution, \"messages\": messages, \"iterations\": iterations}\n", "\n", "\n", "### 边缘\n", "\n", "\n", "def decide_to_finish(state: GraphState):\n", "    \"\"\"\n", "    确定是否结束。\n", "\n", "    参数：\n", "        state (dict): 当前图状态\n", "\n", "    返回：\n", "        str: 要调用的下一个节点\n", "    \"\"\"\n", "    error = state[\"error\"]\n", "    iterations = state[\"iterations\"]\n", "\n", "    if error == \"no\" or iterations == max_iterations:\n", "        print(\"---决定：完成---\")\n", "        return \"end\"\n", "    else:\n", "        print(\"---决定：重新尝试解决方案---\")\n", "        if flag == \"reflect\":\n", "            return \"reflect\"\n", "        else:\n", "            return \"generate\"\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建工作流"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph, START\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "workflow.add_node(\"generate\", generate)  # generation solution\n", "workflow.add_node(\"check_code\", code_check)  # check code\n", "workflow.add_node(\"reflect\", reflect)  # reflect\n", "\n", "# Build graph\n", "workflow.add_edge(START, \"generate\")\n", "workflow.add_edge(\"generate\", \"check_code\")\n", "workflow.add_conditional_edges(\n", "    \"check_code\",\n", "    decide_to_finish,\n", "    {\n", "        \"end\": END,\n", "        \"reflect\": \"reflect\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_edge(\"reflect\", \"generate\")\n", "app = workflow.compile()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["测试"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---正在生成代码解决方案---\n", "---正在检查代码---\n", "---代码导入检查：失败---\n", "---决定：重新尝试解决方案---\n", "---正在生成代码解决方案---\n", "---正在检查代码---\n", "---代码导入检查：失败---\n", "---决定：重新尝试解决方案---\n", "---正在生成代码解决方案---\n", "---正在检查代码---\n", "---代码块检查：失败---\n", "---决定：完成---\n"]}], "source": ["question = \"如何在runnable中传递原始的输入?\"\n", "solution = app.invoke({\"messages\": [(\"user\", question)], \"iterations\": 0, \"error\": \"\"})"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["code(prefix='要在LCEL链中传递原始的输入，可以使用一个lambda函数。lambda函数可以将输入直接传递给后面的Runnable。\\n\\n使用lambda函数和其他Runnable组合，可以在链中传递原始输入。例如:', imports='from langchain_core.runnables import RunnableSequence, RunnableLambda', code='identity_lambda = RunnableLambda(lambda x: x)\\nother_runnable = SomeOtherRunnable()\\n\\n# 使用pipe操作符组合\\nchain = identity_lambda | other_runnable\\n\\n# 或者显式使用RunnableSequence \\n# chain = RunnableSequence([identity_lambda, other_runnable])\\n\\n# 调用链，raw_input会通过identity_lambda原样传递给other_runnable\\noutput = chain.invoke(raw_input)')"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["solution[\"generation\"]"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}