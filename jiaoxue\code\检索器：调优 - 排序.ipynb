{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 检索器：调优 - 排序\n", "***\n", "- lost in the middle"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "import os\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\")+\"/v1\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 什么是lost in the middle\n", "***\n", "![](lost.png)\n", "- 当问题回答语料位于长文中间位置时，需要注意\n", "- 当需要从长文中获取答案时候"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- 这是一篇关于北京故宫历史的文档。\n", "- 故宫是北京最著名的古迹之一。\n", "- 这只是一段随机文本。\n", "- 西湖是杭州著名的旅游景点。\n", "- 我最喜欢的歌曲是《月亮代表我的心》。\n", "- 故宫博物院每年接待游客数百万人次。\n", "- 北京故宫的藏品数量超过一百万件。\n", "- 紫禁城是故宫的别称，位于北京。\n", "- 《三国演义》是中国四大名著之一。\n", "- 我非常喜欢去电影院看电影。\n"]}], "source": ["from langchain_core.vectorstores import InMemoryVectorStore\n", "\n", "texts = [\n", "    \"西湖是杭州著名的旅游景点。\",\n", "    \"我最喜欢的歌曲是《月亮代表我的心》。\",\n", "    \"故宫是北京最著名的古迹之一。\",\n", "    \"这是一篇关于北京故宫历史的文档。\",\n", "    \"我非常喜欢去电影院看电影。\",\n", "    \"北京故宫的藏品数量超过一百万件。\",\n", "    \"这只是一段随机文本。\",\n", "    \"《三国演义》是中国四大名著之一。\",\n", "    \"紫禁城是故宫的别称，位于北京。\",\n", "    \"故宫博物院每年接待游客数百万人次。\",\n", "]\n", "\n", "# 创建检索器\n", "retriever = InMemoryVectorStore.from_texts(texts, embedding=embeddings_model).as_retriever(\n", "    search_kwargs={\"k\": 10}\n", ")\n", "query = \"请告诉我关于故宫的信息？\"\n", "\n", "# 获取按相关性排序的文档\n", "docs = retriever.invoke(query)\n", "for doc in docs:\n", "    print(f\"- {doc.page_content}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["请注意，返回的文档按与查询的相关性降序排列。LongContextReorder文档转换器将实现上述重新排序："]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- 故宫是北京最著名的古迹之一。\n", "- 西湖是杭州著名的旅游景点。\n", "- 故宫博物院每年接待游客数百万人次。\n", "- 紫禁城是故宫的别称，位于北京。\n", "- 我非常喜欢去电影院看电影。\n", "- 《三国演义》是中国四大名著之一。\n", "- 北京故宫的藏品数量超过一百万件。\n", "- 我最喜欢的歌曲是《月亮代表我的心》。\n", "- 这只是一段随机文本。\n", "- 这是一篇关于北京故宫历史的文档。\n"]}], "source": ["from langchain_community.document_transformers import LongContextReorder\n", "\n", "# 重新排序文档：\n", "# 相关性较低的文档将位于列表中间\n", "# 相关性较高的文档将位于开头和结尾\n", "reordering = LongContextReorder()\n", "reordered_docs = reordering.transform_documents(docs)\n", "\n", "# 确认相关性高的文档位于开头和结尾\n", "for doc in reordered_docs:\n", "    print(f\"- {doc.page_content}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["整合到chain里面去"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["故宫是北京最著名的古迹之一，位于北京，且有一个别称叫紫禁城。它是故宫博物院的所在地，藏品数量超过一百万件，每年接待游客数百万人次。故宫以其丰富的历史和文化遗产而闻名，吸引了大量游客前来参观。\n"]}], "source": ["from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_core.prompts import PromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")\n", "\n", "prompt_template = \"\"\"\n", "Given these texts:\n", "-----\n", "{context}\n", "-----\n", "Please answer the following question:\n", "{query}\n", "\"\"\"\n", "\n", "prompt = PromptTemplate(\n", "    template=prompt_template,\n", "    input_variables=[\"context\", \"query\"],\n", ")\n", "\n", "# Create and invoke the chain:\n", "chain = create_stuff_documents_chain(llm, prompt)\n", "response = chain.invoke({\"context\": reordered_docs, \"query\": query})\n", "print(response)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}