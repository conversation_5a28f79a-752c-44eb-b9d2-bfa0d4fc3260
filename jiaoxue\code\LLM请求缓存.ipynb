{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## LLM请求缓存\n", "***\n", "- 短期缓存\n", "- 长期缓存"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 定义模型\n", "***"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "import os\n", "\n", "llm = ChatOpenAI(\n", "    model=\"gpt-4\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 引入依赖包\n", "***"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_core.globals import set_llm_cache"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 短期缓存\n", "***"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 22.9 ms, sys: 4.73 ms, total: 27.6 ms\n", "Wall time: 4.25 s\n"]}, {"data": {"text/plain": ["AIMessage(content='好的，这是一个我最近听到的笑话：\\n\\n为什么电脑永远不会感冒？\\n\\n因为它有Windows（窗户）！', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 45, 'prompt_tokens': 14, 'total_tokens': 59, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-5188934a-762b-485d-a314-0c8ceefaa7fc-0', usage_metadata={'input_tokens': 14, 'output_tokens': 45, 'total_tokens': 59, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "from langchain_core.caches import InMemoryCache\n", "\n", "set_llm_cache(InMemoryCache())\n", "\n", "# 第一次不在缓存层中\n", "llm.invoke(\"给我讲一个笑话\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 560 μs, sys: 111 μs, total: 671 μs\n", "Wall time: 656 μs\n"]}, {"data": {"text/plain": ["AIMessage(content='好的，这是一个我最近听到的笑话：\\n\\n为什么电脑永远不会感冒？\\n\\n因为它有Windows（窗户）！', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 45, 'prompt_tokens': 14, 'total_tokens': 59, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-5188934a-762b-485d-a314-0c8ceefaa7fc-0', usage_metadata={'input_tokens': 14, 'output_tokens': 45, 'total_tokens': 59, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "# 第二次命中缓存，速度会很快\n", "llm.invoke(\"给我讲一个笑话\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### SQLite Cach\n", "***"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting langchain-community\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/7e/31/39c30cab465774835e2c18d3746587e6fd0c9f7265b1c6b1fcd2e1684dd2/langchain_community-0.3.17-py3-none-any.whl (2.5 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.5/2.5 MB\u001b[0m \u001b[31m1.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m00:01\u001b[0m00:01\u001b[0m0m\n", "\u001b[?25hRequirement already satisfied: langchain-core<1.0.0,>=0.3.34 in ./.venv/lib/python3.11/site-packages (from langchain-community) (0.3.34)\n", "Requirement already satisfied: langchain<1.0.0,>=0.3.18 in ./.venv/lib/python3.11/site-packages (from langchain-community) (0.3.18)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in ./.venv/lib/python3.11/site-packages (from langchain-community) (2.0.38)\n", "Requirement already satisfied: requests<3,>=2 in ./.venv/lib/python3.11/site-packages (from langchain-community) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in ./.venv/lib/python3.11/site-packages (from langchain-community) (6.0.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in ./.venv/lib/python3.11/site-packages (from langchain-community) (3.11.12)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in ./.venv/lib/python3.11/site-packages (from langchain-community) (9.0.0)\n", "Collecting dataclasses-json<0.7,>=0.5.7 (from langchain-community)\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/c3/be/d0d44e092656fe7a06b55e6103cbce807cdbdee17884a5367c68c9860853/dataclasses_json-0.6.7-py3-none-any.whl (28 kB)\n", "Collecting pydantic-settings<3.0.0,>=2.4.0 (from langchain-community)\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/b4/46/93416fdae86d40879714f72956ac14df9c7b76f7d41a4d68aa9f71a0028b/pydantic_settings-2.7.1-py3-none-any.whl (29 kB)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in ./.venv/lib/python3.11/site-packages (from langchain-community) (0.3.8)\n", "Collecting httpx-sse<1.0.0,>=0.4.0 (from langchain-community)\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/e1/9b/a181f281f65d776426002f330c31849b86b31fc9d848db62e16f03ff739f/httpx_sse-0.4.0-py3-none-any.whl (7.8 kB)\n", "Requirement already satisfied: numpy<2,>=1.26.4 in ./.venv/lib/python3.11/site-packages (from langchain-community) (1.26.4)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in ./.venv/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (2.4.6)\n", "Requirement already satisfied: aiosignal>=1.1.2 in ./.venv/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in ./.venv/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (25.1.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in ./.venv/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in ./.venv/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (6.1.0)\n", "Requirement already satisfied: propcache>=0.2.0 in ./.venv/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (0.2.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in ./.venv/lib/python3.11/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community) (1.18.3)\n", "Collecting marshmallow<4.0.0,>=3.18.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/34/75/51952c7b2d3873b44a0028b1bd26a25078c18f92f256608e8d1dc61b39fd/marshmallow-3.26.1-py3-none-any.whl (50 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m50.9/50.9 kB\u001b[0m \u001b[31m4.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting typing-inspect<1,>=0.4.0 (from dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/65/f3/107a22063bf27bdccf2024833d3445f4eea42b2e598abfbd46f6a63b6cb0/typing_inspect-0.9.0-py3-none-any.whl (8.8 kB)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.6 in ./.venv/lib/python3.11/site-packages (from langchain<1.0.0,>=0.3.18->langchain-community) (0.3.6)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in ./.venv/lib/python3.11/site-packages (from langchain<1.0.0,>=0.3.18->langchain-community) (2.10.6)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in ./.venv/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain-community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in ./.venv/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain-community) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in ./.venv/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain-community) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in ./.venv/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-community) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in ./.venv/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-community) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in ./.venv/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-community) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in ./.venv/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-community) (0.23.0)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in ./.venv/lib/python3.11/site-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain-community) (1.0.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.11/site-packages (from requests<3,>=2->langchain-community) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in ./.venv/lib/python3.11/site-packages (from requests<3,>=2->langchain-community) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.11/site-packages (from requests<3,>=2->langchain-community) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in ./.venv/lib/python3.11/site-packages (from requests<3,>=2->langchain-community) (2025.1.31)\n", "Requirement already satisfied: anyio in ./.venv/lib/python3.11/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-community) (4.8.0)\n", "Requirement already satisfied: httpcore==1.* in ./.venv/lib/python3.11/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-community) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in ./.venv/lib/python3.11/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-community) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in ./.venv/lib/python3.11/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.34->langchain-community) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.18->langchain-community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.18->langchain-community) (2.27.2)\n", "Collecting mypy-extensions>=0.3.0 (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community)\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/2a/e2/5d3f6ada4297caebe1a2add3b126fe800c96f56dbe5d1988a2cbe0b267aa/mypy_extensions-1.0.0-py3-none-any.whl (4.7 kB)\n", "Requirement already satisfied: sniffio>=1.1 in ./.venv/lib/python3.11/site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-community) (1.3.1)\n", "Installing collected packages: mypy-extensions, marshmallow, httpx-sse, typing-inspect, pydantic-settings, dataclasses-json, langchain-community\n", "Successfully installed dataclasses-json-0.6.7 httpx-sse-0.4.0 langchain-community-0.3.17 marshmallow-3.26.1 mypy-extensions-1.0.0 pydantic-settings-2.7.1 typing-inspect-0.9.0\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m23.2.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install langchain-community"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["!rm .langchain.db"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# We can do the same thing with a SQLite cache\n", "from langchain_community.cache import SQLiteCache\n", "\n", "set_llm_cache(SQLiteCache(database_path=\".langchain.db\"))"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 20.9 ms, sys: 14.1 ms, total: 35 ms\n", "Wall time: 4 s\n"]}, {"data": {"text/plain": ["AIMessage(content='好的，这是一个我最近听到的笑话：\\n\\n为什么电脑永远不会感冒？\\n\\n因为它有Windows（窗户）！', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 45, 'prompt_tokens': 14, 'total_tokens': 59, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-ee976a3a-255e-48ba-9979-594e22fc117a-0', usage_metadata={'input_tokens': 14, 'output_tokens': 45, 'total_tokens': 59, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "# 第一次不在缓存层中\n", "llm.invoke(\"给我讲一个笑话\")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 1.92 ms, sys: 1.06 ms, total: 2.99 ms\n", "Wall time: 2.78 ms\n"]}, {"data": {"text/plain": ["AIMessage(content='好的，这是一个我最近听到的笑话：\\n\\n为什么电脑永远不会感冒？\\n\\n因为它有Windows（窗户）！', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 45, 'prompt_tokens': 14, 'total_tokens': 59, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-ee976a3a-255e-48ba-9979-594e22fc117a-0', usage_metadata={'input_tokens': 14, 'output_tokens': 45, 'total_tokens': 59, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["%%time\n", "# 第二次命中缓存，速度会很快\n", "llm.invoke(\"给我讲一个笑话\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}