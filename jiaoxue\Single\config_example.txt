# Poetry环境配置指南

## 环境变量配置
# 请在项目根目录创建 .env 文件，并配置以下环境变量：

# 钉钉机器人配置（必需）
DINGDING_ID=your_dingtalk_app_id
DINGDING_SECRET=your_dingtalk_app_secret

# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai.com/v1

# DeepSeek API配置（如果使用DeepSeek）
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_API_BASE=https://api.deepseek.com/v1

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Qdrant向量数据库配置
QDRANT_HOST=localhost
QDRANT_PORT=6333

# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000

# 日志配置
LOG_LEVEL=INFO
DEBUG=False

## Poetry使用命令
# 1. 安装依赖: poetry install
# 2. 激活环境: poetry shell
# 3. 运行钉钉机器人: poetry run python run_dingtalk.py
# 4. 运行Web服务器: poetry run python run_server.py
# 5. 添加新依赖: poetry add package_name
# 6. 查看依赖: poetry show
# 7. 更新依赖: poetry update

## 使用方法：
# 1. 复制此文件内容到 .env 文件
# 2. 将 your_* 替换为实际的配置值
# 3. 保存 .env 文件到项目根目录
# 4. 使用 poetry shell 激活环境
# 5. 运行项目 