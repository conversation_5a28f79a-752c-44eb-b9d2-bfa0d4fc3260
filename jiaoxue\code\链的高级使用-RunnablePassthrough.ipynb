{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 使用RunnablePassthrough来传递值\n", "****"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'passed': {'num': 1}, 'modified': 2}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.runnables import RunnableParallel, RunnablePassthrough\n", "\n", "# 创建一个可并行运行的处理流程\n", "runnable = RunnableParallel(\n", "    passed=RunnablePassthrough(),  # 第一个处理器：直接传递输入，不做修改\n", "    modified=lambda x: x[\"num\"] + 1,  # 第二个处理器：取出输入中的\"num\"值并加1\n", ")\n", "\n", "# 执行这个处理流程，输入是一个包含\"num\"字段的字典\n", "runnable.invoke({\"num\": 1})\n", "# 运行结果：{'passed': {'num': 1}, 'modified': 2}\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}