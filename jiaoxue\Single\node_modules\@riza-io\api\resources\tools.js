"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolsToolsPagination = exports.Tools = void 0;
const resource_1 = require("../resource.js");
const core_1 = require("../core.js");
const pagination_1 = require("../pagination.js");
class Tools extends resource_1.APIResource {
    /**
     * Create a tool in your project.
     */
    create(body, options) {
        return this._client.post('/v1/tools', { body, ...options });
    }
    /**
     * Update the source code and input schema of a tool.
     */
    update(id, body, options) {
        return this._client.post(`/v1/tools/${id}`, { body, ...options });
    }
    list(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/tools', ToolsToolsPagination, { query, ...options });
    }
    /**
     * Execute a tool with a given input. The input is validated against the tool's
     * input schema.
     */
    exec(id, body, options) {
        return this._client.post(`/v1/tools/${id}/execute`, { body, ...options });
    }
    /**
     * Retrieves a tool.
     */
    get(id, options) {
        return this._client.get(`/v1/tools/${id}`, options);
    }
}
exports.Tools = Tools;
class ToolsToolsPagination extends pagination_1.ToolsPagination {
}
exports.ToolsToolsPagination = ToolsToolsPagination;
Tools.ToolsToolsPagination = ToolsToolsPagination;
//# sourceMappingURL=tools.js.map