{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 使用LCEL来自定义路由链"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'topic': '<PERSON><PERSON><PERSON><PERSON>', 'question': '我该如何使用langchain?'}\n", "langchain\n"]}, {"data": {"text/plain": ["AIMessage(content='正如Harrison Chase告诉我的，使用LangChain的第一步是理解其核心概念和组件。LangChain是一个用于构建基于语言模型的应用的框架，它允许你将语言模型与其他数据源和工具集成起来。以下是一些基本步骤来开始使用LangChain：\\n\\n1. **安装LangChain**：首先，你需要通过pip安装LangChain库。你可以使用以下命令进行安装：\\n   ```bash\\n   pip install langchain\\n   ```\\n\\n2. **导入必要的模块**：在你的Python脚本中，导入LangChain的核心模块。例如：\\n   ```python\\n   from langchain import LLMChain, PromptTemplate\\n   ```\\n\\n3. **创建提示模板**：提示模板是LangChain中的一个重要概念，它定义了如何将用户输入转换为语言模型的输入。你可以使用`PromptTemplate`类来创建提示模板：\\n   ```python\\n   prompt = PromptTemplate(\\n       input_variables=[\"topic\"],\\n       template=\"请写一篇关于{topic}的文章。\"\\n   )\\n   ```\\n\\n4. **初始化语言模型**：LangChain支持多种语言模型，包括OpenAI的GPT-3、GPT-4等。你需要初始化一个语言模型实例：\\n   ```python\\n   from langchain.llms import OpenAI\\n   llm = OpenAI(api_key=\"your_openai_api_key\")\\n   ```\\n\\n5. **创建链**：链是LangChain中的另一个核心概念，它将提示模板和语言模型连接起来。你可以使用`LLMChain`类来创建链：\\n   ```python\\n   chain = LLMChain(llm=llm, prompt=prompt)\\n   ```\\n\\n6. **运行链**：最后，你可以通过调用链的`run`方法来生成输出：\\n   ```python\\n   output = chain.run(topic=\"人工智能\")\\n   print(output)\\n   ```\\n\\n7. **集成其他工具**：LangChain还允许你将语言模型与其他工具（如搜索引擎、数据库等）集成起来。你可以使用`Tool`类来定义这些工具，并将它们添加到链中。\\n\\n通过这些步骤，你可以开始使用LangChain构建强大的语言模型应用。Harrison Chase强调，LangChain的灵活性和可扩展性使其成为开发复杂应用的理想选择。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 450, 'prompt_tokens': 52, 'total_tokens': 502, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-f08a6958-b894-4173-be8f-121f56ef3066-0', usage_metadata={'input_tokens': 52, 'output_tokens': 450, 'total_tokens': 502, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# 导入必要的库\n", "from langchain_core.output_parsers import StrOutputParser  # 导入字符串输出解析器\n", "from langchain_core.prompts import PromptTemplate  # 导入提示模板\n", "import os  # 导入os库\n", "from langchain_deepseek import ChatDeepSeek\n", "# 导入RunnableLambda用于创建可运行的函数链\n", "from langchain_core.runnables import RunnableLambda\n", "\n", "\n", "claudeLLM = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "# 创建分类链 - 用于确定问题类型\n", "chain = (\n", "    # 创建提示模板，要求模型将问题分类为LangChai<PERSON>、Anthropic或Other\n", "    PromptTemplate.from_template(\n", "        \"\"\"根据下面的用户问题，将其分类为 `<PERSON><PERSON><PERSON><PERSON>`、`Anthropic` 或 `Other`。\n", "\n", "请只回复一个词作为答案。\n", "\n", "<question>\n", "{question}\n", "</question>\n", "\n", "分类结果:\"\"\"\n", "    )\n", "    | claudeLLM  # 将提示发送给Claude模型\n", "    | StrOutputParser()  # 解析模型的输出为纯文本\n", ")\n", "\n", "# 创建LangChain专家链 - 模拟<PERSON>(LangChain创始人)的回答风格\n", "langchain_chain = PromptTemplate.from_template(\n", "    \"\"\"你将扮演一位LangChain专家。请以他的视角回答问题。 \\\n", "你的回答必须以\"正如Harrison Chase告诉我的\"开头，否则你会受到惩罚。 \\\n", "请回答以下问题:\n", "\n", "问题: {question}\n", "回答:\"\"\"\n", ") | claudeLLM  # 将提示发送给\n", "\n", "\n", "\n", "# 创建Anthropic专家链 - 模拟<PERSON><PERSON>(Anthropic创始人)的回答风格\n", "anthropic_chain = PromptTemplate.from_template(\n", "    \"\"\"你将扮演一位一位Anthropic专家。请以他的视角回答问题。 \\\n", "你的回答必须以\"正如Dario Amodei告诉我的\"开头，否则你会受到惩罚。 \\\n", "请回答以下问题:\n", "\n", "问题: {question}\n", "回答:\"\"\"\n", ") | claudeLL<PERSON>  \n", "\n", "# 创建通用回答链 - 用于处理其他类型的问题\n", "general_chain = PromptTemplate.from_template(\n", "    \"\"\"请回答以下问题:\n", "\n", "问题: {question}\n", "回答:\"\"\"\n", ") | claudeLL<PERSON> \n", "\n", "# 自定义路由函数 - 根据问题分类结果选择合适的回答链\n", "def route(info):\n", "    print(info)   # 打印分类结果\n", "    # 根据分类结果选择相应的专家链\n", "    if \"anthropic\" in info[\"topic\"].lower():  # 如果问题与Anthropic相关\n", "        print(\"claude\")\n", "        return anthropic_chain  # 使用Anthropic专家链\n", "    elif \"langchain\" in info[\"topic\"].lower():  # 如果问题与LangChain相关\n", "        print(\"langchain\")\n", "        return langchain_chain  # 使用LangChain专家链\n", "    else:  # 其他类型的问题\n", "        print(\"general\")\n", "        return general_chain # 使用通用回答链\n", "\n", "\n", "\n", "# 创建完整的处理链\n", "# 1. 首先将问题分类并保留原始问题\n", "# 2. 然后根据分类结果路由到相应的专家链处理\n", "full_chain = {\"topic\": chain, \"question\": lambda x: x[\"question\"]} | RunnableLambda(route)\n", "\n", "# 调用完整链处理用户问题\n", "# 这个问题会被分类为Anthropic相关，然后由anthropic_chain处理\n", "full_chain.invoke({\"question\": \"我该如何使用langchain?\"})"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}