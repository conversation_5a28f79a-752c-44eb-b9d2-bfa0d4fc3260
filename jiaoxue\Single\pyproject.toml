[tool.poetry]
name = "dingtalk-langchain"
version = "0.1.0"
description = "小浪助手是一个基于 LangChain 框架开发的智能 Agent 教学案例，旨在展示如何构建一个具有实际应用价值的 AI 助手。在当前 AI 技术快速迭代的背景下，掌握 AI Agents 开发已成为技术从业者的必备技能。本项目通过实战案例，帮助开发者快速入门 AI Agents 开发领域。"
authors = ["<EMAIL>"]
readme = "README.md"
packages = [
    { include = "src" }
]
[tool.poetry.dependencies]
python = ">=3.9,<3.14"
aiofiles = "24.1.0"
aiohttp = "3.10.5"
aiosignal = "1.3.1"
alibabacloud-dingtalk = "2.1.90"
alibabacloud-tea = "0.4.2"
alibabacloud-credentials = "0.3.6"
alibabacloud-endpoint-util = "0.0.3"
alibabacloud-gateway-dingtalk = "1.0.2"
alibabacloud-gateway-spi = "0.0.3"
alibabacloud-openapi-util = "0.2.2"
alibabacloud-tea-util = "0.3.13"
alibabacloud-tea-xml = "0.0.2"
annotated-types = "0.7.0"
anyio = "4.8.0"
attrs = "25.1.0"
beautifulsoup4 = "4.13.3"
build = "1.2.2.post1"
cachecontrol = "0.14.2"
certifi = "2025.1.31"
cffi = "1.17.1"
charset-normalizer = "3.4.1"
cleo = "2.1.0"
click = "8.1.8"
crashtest = "0.4.1"
cryptography = "^43.0.0"
dataclasses-json = "0.6.7"
dingtalk-stream = "0.22.1"
dingtalkchatbot = "1.5.7"
distlib = "0.3.9"
distro = "1.9.0"
dulwich = "0.22.8"
fastapi = "0.115.8"
fastjsonschema = "2.21.1"
filelock = "3.17.0"
findpython = "0.6.2"
frozenlist = "1.5.0"
google-search-results = "2.4.2"
grpcio = "1.70.0"
grpcio-tools = "1.70.0"
h11 = "0.14.0"
h2 = "4.2.0"
hpack = "4.1.0"
httpcore = "1.0.7"
httpx = "0.28.1"
hyperframe = "6.1.0"
idna = "3.10"
installer = "0.7.0"
jaraco-classes = "3.4.0"
jaraco-context = "6.0.1"
jaraco-functools = "4.1.0"
jiter = "0.8.2"
jsonpatch = "1.33"
jsonpointer = "3.0.0"
keyring = "25.6.0"
langchain = "0.3.1"
langchain-community = "0.3.1"
langchain-core = "0.3.40"
langchain-deepseek = "0.1.2"
langchain-openai = "0.3.7"
langchain-qdrant = "0.1.4"
langchain-text-splitters = "0.3.0"
langsmith = "0.1.125"
lxml = "5.3.1"
marshmallow = "3.26.1"
more-itertools = "10.6.0"
msgpack = "1.1.0"
multidict = "6.1.0"
mypy-extensions = "1.0.0"
openai = "1.64.0"
orjson = "3.10.15"
packaging = "24.2"
pbs-installer = "2025.2.12"
pkginfo = "1.12.1.2"
numpy = "1.26.4"
platformdirs = "4.3.6"
poetry = "2.1.1"
poetry-core = "2.1.1"
portalocker = "2.10.1"
propcache = "0.3.0"
protobuf = "5.29.3"
pycparser = "2.22"
pydantic = "2.10.6"
pydantic-settings = "2.8.0"
pydantic-core = "2.27.2"
pyproject-hooks = "1.2.0"
python-dotenv = "1.0.1"
pyyaml = "6.0.2"
qdrant-client = "1.12.1"
rapidfuzz = "3.12.2"
redis = "5.2.1"
regex = "2024.11.6"
requests = "2.32.3"
requests-toolbelt = "1.0.0"
setuptools = "75.8.1"
shellingham = "1.5.4"
sniffio = "1.3.1"
soupsieve = "2.6"
sqlalchemy = "2.0.38"
starlette = "0.45.3"
tenacity = "8.5.0"
tiktoken = "0.9.0"
tomlkit = "0.13.2"
tqdm = "4.67.1"
trove-classifiers = "2025.3.3.18"
typing-inspect = "0.9.0"
typing-extensions = "4.12.2"
urllib3 = "2.3.0"
uvicorn = "0.31.0"
virtualenv = "20.29.3"
websockets = "13.1"
yarl = "1.18.3"
zstandard = "0.23.0"
rizaio = "^0.12.0"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
