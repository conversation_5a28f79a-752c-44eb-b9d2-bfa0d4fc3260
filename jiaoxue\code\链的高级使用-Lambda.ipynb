{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 使用函数"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='The sum of 3 and 9 is calculated as follows:\\n\\n3 + 9 = 12\\n\\n**Answer:** 12', additional_kwargs={'refusal': None, 'reasoning_content': \"Okay, so I need to figure out what 3 plus 9 is. Let me start by recalling how addition works. Addition is combining two numbers to get their total sum. So if I have 3 of something and then add 9 more, how many do I have in total?\\n\\nLet me count on my fingers. Starting with 3, if I add 9 more, I can count up 9 numbers from 3. So 3... then 4, 5, 6, 7, 8, 9, 10, 11, 12. That's 9 numbers added, so 3 + 9 should be 12. Wait, let me check that again to make sure I didn't skip a number. Starting at 3, adding 1 gets me to 4 (that's 1), then 5 (2), 6 (3), 7 (4), 8 (5), 9 (6), 10 (7), 11 (8), 12 (9). Yep, that's 9 steps, so 3 + 9 equals 12.\\n\\nAlternatively, I know that 9 is one less than 10. So maybe breaking it down: 3 + 9 is the same as 3 + (10 - 1). That would be 3 + 10 = 13, then subtract 1 to get 12. That also gives me 12. Hmm, same answer.\\n\\nOr maybe using the commutative property, since addition is commutative. So 3 + 9 is the same as 9 + 3. If I have 9 and add 3, that's easier for some people. Starting at 9, add 1 to get 10, then 2 more to get 12. So 9 + 3 = 12. Yep, still 12.\\n\\nAnother way: using number bonds. If I know that 9 and 1 make 10, then maybe take 1 from the 3 to make 10. So 3 is 2 + 1. Then 9 + 1 = 10, and 10 + 2 = 12. That's another method called making a ten, which is helpful for mental math. So again, 12.\\n\\nWait, maybe I can visualize it with objects. Imagine 3 apples and someone gives me 9 more apples. How many apples do I have? Let's count: 3 apples plus 9 apples. If I line them up, 3 + 9 would be 12 apples in total. That makes sense.\\n\\nI could also use a number line. Starting at 3, move 9 units to the right. Each jump is 1 unit. So from 3, jumping to 4 (1), 5 (2), 6 (3), 7 (4), 8 (5), 9 (6), 10 (7), 11 (8), 12 (9). Land on 12. So that's 3 + 9 = 12.\\n\\nAlternatively, using basic addition facts. If I memorize that 3 + 9 is 12, but since I might not have that memorized, I can check with subtraction. If 12 - 9 = 3, then that confirms that 3 + 9 = 12. Similarly, 12 - 3 = 9, which also checks out.\\n\\nI think all these methods point to the same answer. Let me just make sure I didn't make a mistake in any of the steps. For example, when I broke down 3 into 2 + 1 to add to 9, that's correct because 9 + 1 = 10, and 10 + 2 = 12. No errors there. When I used the number line, each step was counted correctly. Starting at 3 and moving 9 steps lands on 12. The commutative property also holds, so 9 + 3 is indeed the same as 3 + 9. All the different approaches confirm that the sum is 12. I don't see any mistakes in my reasoning, so I'm confident the answer is 12.\"}, response_metadata={'token_usage': {'completion_tokens': 901, 'prompt_tokens': 12, 'total_tokens': 913, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-R1', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-81da5dd1-267d-4aba-8d2a-026d61620ab2-0', usage_metadata={'input_tokens': 12, 'output_tokens': 901, 'total_tokens': 913, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from operator import itemgetter  # 导入itemgetter函数，用于从字典中提取值\n", "\n", "from langchain_core.prompts import ChatPromptTemplate  # 导入聊天提示模板\n", "from langchain_core.runnables import RunnableLambda  # 导入可运行的Lambda函数包装器\n", "from langchain_deepseek import ChatDeepSeek  # 导入DeepSeek聊天模型\n", "import os  # 导入os模块，用于从环境变量获取API密钥\n", "\n", "# 初始化DeepSeek大语言模型\n", "model = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-R1\",  # 使用DeepSeek-R1模型\n", "    temperature=0,  # 设置温度为0，使输出更确定性\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),  # 从环境变量获取API密钥\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),  # 从环境变量获取API基础URL\n", ")\n", "\n", "\n", "def length_function(text):\n", "    return len(text)\n", "\n", "\n", "def _multiple_length_function(text1, text2):\n", "    return len(text1) * len(text2)\n", "\n", "\n", "def multiple_length_function(_dict):\n", "    return _multiple_length_function(_dict[\"text1\"], _dict[\"text2\"])\n", "\n", "\n", "\n", "# 创建一个简单的聊天提示模板，询问a和b的和\n", "prompt = ChatPromptTemplate.from_template(\"what is {a} + {b}\")\n", "\n", "# 构建一个复杂的处理链\n", "chain = (\n", "    {\n", "        # 处理\"a\"参数:\n", "        # 1. 从输入字典中提取\"foo\"键的值\n", "        # 2. 将提取的值传递给length_function函数(假设这个函数计算字符串长度)\n", "        \"a\": itemgetter(\"foo\") | RunnableLambda(length_function),\n", "        \n", "        # 处理\"b\"参数:\n", "        # 1. 创建一个包含两个键值对的字典:\n", "        #    - \"text1\": 从输入字典中提取\"foo\"键的值\n", "        #    - \"text2\": 从输入字典中提取\"bar\"键的值\n", "        # 2. 将这个字典传递给multiple_length_function函数\n", "        #    (假设这个函数计算两个文本的总长度)\n", "        \"b\": {\"text1\": itemgetter(\"foo\"), \"text2\": itemgetter(\"bar\")}\n", "        | RunnableLambda(multiple_length_function),\n", "    }\n", "    | prompt  # 将处理后的\"a\"和\"b\"值填入提示模板\n", "    | model  # 将填充后的提示发送给DeepSeek模型生成回答\n", ")\n", "\n", "# 调用链处理流程，输入一个包含\"foo\"和\"bar\"键的字典\n", "# 整个过程:\n", "# 1. 计算\"bar\"字符串的长度作为a的值\n", "# 2. 计算\"bar\"和\"gah\"字符串的总长度作为b的值\n", "# 3. 将这些值填入提示\"what is {a} + {b}\"\n", "# 4. 让DeepSeek模型回答这个问题\n", "chain.invoke({\"foo\": \"bar\", \"bar\": \"gah\"})"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}