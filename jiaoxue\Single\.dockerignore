# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# 环境和配置
.env
.env.*
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE 和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 项目特定
logs/
*.log
*.sqlite
*.db

# 文档和说明文件（保留 README）
*.md
!README.md

# Docker 相关
Dockerfile*
.dockerignore
docker-compose*.yml

# Git 相关
.git/
.gitignore
.github/

# 测试和开发工具
.pytest_cache/
.coverage
htmlcov/
.mypy_cache/
.ruff_cache/ 