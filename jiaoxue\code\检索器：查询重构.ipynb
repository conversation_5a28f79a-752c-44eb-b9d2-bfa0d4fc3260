{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 检索器：查询重构\n", "****\n", "- 用自然语言来查询SQL"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 查询SQL\n", "***\n", "- 步骤1: 将问题转换为 SQL 查询，模型将用户输入转换为 SQL 查询。\n", "- 步骤2: 执行 SQL 查询，执行查询。\n", "- 步骤3: 回答问题，模型使用查询结果响应用户输入。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["! pip install --upgrade --quiet langchain-community langchainhub langgraph"]}, {"cell_type": "markdown", "metadata": {}, "source": ["查看数据库"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sqlite\n", "['Album', 'Artist', 'Customer', 'Employee', 'Genre', 'Invoice', 'InvoiceLine', 'MediaType', 'Playlist', 'PlaylistTrack', 'Track']\n"]}, {"data": {"text/plain": ["\"[(1, 'AC/DC'), (2, 'Accept'), (3, '<PERSON><PERSON>'), (4, '<PERSON><PERSON>'), (5, '<PERSON> In Chains'), (6, '<PERSON><PERSON><PERSON><PERSON>'), (7, 'Apocalyptica'), (8, 'Audioslave'), (9, 'BackBeat'), (10, '<PERSON>')]\""]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.utilities import SQLDatabase\n", "\n", "db = SQLDatabase.from_uri(\"sqlite:///Chinook.db\")\n", "print(db.dialect)\n", "print(db.get_usable_table_names())\n", "db.run(\"SELECT * FROM Artist LIMIT 10;\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用hub上预制的提示词"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "AssertionError", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAsser<PERSON>Error\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 5\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mlangchain\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m hub\n\u001b[32m      3\u001b[39m query_prompt_template = hub.pull(\u001b[33m\"\u001b[39m\u001b[33mlangchain-ai/sql-query-system-prompt\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m5\u001b[39m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(query_prompt_template.messages) == \u001b[32m1\u001b[39m\n\u001b[32m      6\u001b[39m query_prompt_template.messages[\u001b[32m0\u001b[39m].pretty_print()\n", "\u001b[31mAssertionError\u001b[39m: "]}], "source": ["from langchain import hub\n", "\n", "query_prompt_template = hub.pull(\"langchain-ai/sql-query-system-prompt\")\n", "\n", "assert len(query_prompt_template.messages) == 1\n", "query_prompt_template.messages[0].pretty_print()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from langchain_deepseek import ChatDeepSeek\n", "import os\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用LCEL创建一个最简单的SQL查询"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from typing_extensions import Annotated\n", "from typing_extensions import TypedDict\n", "\n", "# Define the state type\n", "class State(TypedDict):\n", "    question: str\n", "    query: str\n", "    result: str\n", "    answer: str\n", "\n", "# Define the output type\n", "class QueryOutput(TypedDict):\n", "    \"\"\"Generated SQL query.\"\"\"\n", "\n", "    query: Annotated[str, ..., \"Syntactically valid SQL query.\"]\n", "\n", "# Define the write_query function\n", "def write_query(state: State):\n", "    \"\"\"Generate SQL query to fetch information.\"\"\"\n", "    prompt = query_prompt_template.invoke(\n", "        {\n", "            \"dialect\": db.dialect,\n", "            \"top_k\": 10,\n", "            \"table_info\": db.get_table_info(),\n", "            \"input\": state[\"question\"],\n", "        }\n", "    )\n", "    structured_llm = llm.with_structured_output(QueryOutput)\n", "    result = structured_llm.invoke(prompt)\n", "    return {\"query\": result[\"query\"]}"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'query': 'SELECT COUNT(*) AS TotalEmployees FROM Employee;'}\n"]}], "source": ["sqlMessage = write_query({\"question\": \"一共有多少个员工?\"})\n", "print(sqlMessage)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["得到的SQL语句可以接着进行执行 ⚠️ 此操作有风险"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from langchain_community.tools.sql_database.tool import QuerySQLDatabaseTool\n", "\n", "\n", "def execute_query(state: State):\n", "    \"\"\"Execute SQL query.\"\"\"\n", "    execute_query_tool = QuerySQLDatabaseTool(db=db)\n", "    return {\"result\": execute_query_tool.invoke(state[\"query\"])}"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'result': '[(8,)]'}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["execute_query(sqlMessage)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Question: 获取销售额最高的5位员工及其销售总额\n", "\n", "Generated SQL:\n", "{'query': 'SELECT e.EmployeeId, e.<PERSON>Name, e.LastName, SUM(i.Total) AS TotalSales FROM Employee e JOIN Customer c ON e.EmployeeId = c.SupportRepId JOIN Invoice i ON c.CustomerId = i.CustomerId GROUP BY e.EmployeeId ORDER BY TotalSales DESC LIMIT 5'}\n", "\n", "Execution Result:\n", "{'result': \"[(3, '<PERSON>', '<PERSON>', 833.04), (4, '<PERSON>', '<PERSON>', 775.4), (5, '<PERSON>', '<PERSON>', 720.16)]\"}\n", "\n", "Answer:\n", "{'answer': '根据查询结果，销售额最高的5位员工及其销售总额如下：\\n\\n1. **<PERSON>** - 销售总额: 833.04\\n2. **<PERSON>** - 销售总额: 775.40\\n3. **<PERSON>** - 销售总额: 720.16\\n\\n这些员工按照销售总额从高到低排列，仅显示了前三位员工的信息。'}\n"]}], "source": ["from langchain_core.runnables import RunnablePassthrough\n", "\n", "# Define the chain to answer questions from SQL query\n", "def answer_question(state: State):\n", "    \"\"\"Format answer based on the query result.\"\"\"\n", "    prompt = f\"\"\"Based on the SQL query:\n", "{state[\"query\"]}\n", "\n", "And the query result:\n", "{state[\"result\"]}\n", "\n", "Answer the user's question: {state[\"question\"]}\n", "Provide a concise and informative response.\n", "\"\"\"\n", "    return {\"answer\": llm.invoke(prompt).content}\n", "\n", "# Create a full chain from question to answer\n", "sql_chain = (\n", "    RunnablePassthrough.assign(query=write_query)\n", "    .assign(result=execute_query)\n", "    .assign(answer=answer_question)\n", ")\n", "\n", "# Example usage\n", "question = \"获取销售额最高的5位员工及其销售总额\"\n", "response = sql_chain.invoke({\"question\": question})\n", "\n", "print(\"Question:\", question)\n", "print(\"\\nGenerated SQL:\")\n", "print(response[\"query\"])\n", "print(\"\\nExecution Result:\")\n", "print(response[\"result\"])\n", "print(\"\\nAnswer:\")\n", "print(response[\"answer\"])"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}