#!/usr/bin/env python3
"""
简单的 MongoDB 连接测试
"""

try:
    from pymongo import MongoClient
    print("✅ pymongo 导入成功")
    
    # 连接到 MongoDB
    client = MongoClient("mongodb://localhost:27017/", serverSelectionTimeoutMS=5000)
    print("✅ MongoDB 客户端创建成功")
    
    # 测试连接
    client.admin.command('ping')
    print("✅ MongoDB 连接测试成功！")
    
    # 获取数据库列表
    db_names = client.list_database_names()
    print(f"📊 可用数据库: {db_names}")
    
    # 创建测试数据库和集合
    db = client.test_database
    collection = db.test_collection
    
    # 插入测试数据
    test_doc = {"name": "test", "value": 123, "message": "Hello MongoDB!"}
    result = collection.insert_one(test_doc)
    print(f"✅ 数据插入成功，ID: {result.inserted_id}")
    
    # 查询数据
    found_doc = collection.find_one({"name": "test"})
    print(f"✅ 数据查询成功: {found_doc}")
    
    # 清理测试数据
    collection.delete_one({"name": "test"})
    print("✅ 测试数据清理完成")
    
    client.close()
    print("🎉 MongoDB 测试完全成功！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 连接或操作错误: {e}")
