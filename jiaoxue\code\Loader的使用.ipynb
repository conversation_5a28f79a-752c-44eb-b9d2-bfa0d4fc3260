{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Loader加载器\n", "***\n", "- 如何加载PDF\n", "- 使用多模态解析图文PDF\n", "- 如何加载网页\n", "- 如何加载CSV以及Excel\n", "- 如何自定义一个文件加载器"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 如何加载PDF\n", "*****"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: langchain_community in ./.venv/lib/python3.13/site-packages (0.3.19)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.41 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.3.43)\n", "Requirement already satisfied: langchain<1.0.0,>=0.3.20 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.3.20)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in ./.venv/lib/python3.13/site-packages (from langchain_community) (2.0.38)\n", "Requirement already satisfied: requests<3,>=2 in ./.venv/lib/python3.13/site-packages (from langchain_community) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in ./.venv/lib/python3.13/site-packages (from langchain_community) (6.0.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in ./.venv/lib/python3.13/site-packages (from langchain_community) (3.10.11)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in ./.venv/lib/python3.13/site-packages (from langchain_community) (9.0.0)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.6.7)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in ./.venv/lib/python3.13/site-packages (from langchain_community) (2.8.1)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.3.13)\n", "Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.4.0)\n", "Requirement already satisfied: numpy<3,>=1.26.2 in ./.venv/lib/python3.13/site-packages (from langchain_community) (1.26.4)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (2.5.0)\n", "Requirement already satisfied: aiosignal>=1.1.2 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (25.1.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (6.1.0)\n", "Requirement already satisfied: yarl<2.0,>=1.12.0 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.18.3)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in ./.venv/lib/python3.13/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in ./.venv/lib/python3.13/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (0.9.0)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.6 in ./.venv/lib/python3.13/site-packages (from langchain<1.0.0,>=0.3.20->langchain_community) (0.3.6)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in ./.venv/lib/python3.13/site-packages (from langchain<1.0.0,>=0.3.20->langchain_community) (2.10.6)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.41->langchain_community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.41->langchain_community) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.41->langchain_community) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (0.23.0)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in ./.venv/lib/python3.13/site-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain_community) (1.0.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (2025.1.31)\n", "Requirement already satisfied: anyio in ./.venv/lib/python3.13/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (4.8.0)\n", "Requirement already satisfied: httpcore==1.* in ./.venv/lib/python3.13/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in ./.venv/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in ./.venv/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.41->langchain_community) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.20->langchain_community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.20->langchain_community) (2.27.2)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in ./.venv/lib/python3.13/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community) (1.0.0)\n", "Requirement already satisfied: propcache>=0.2.0 in ./.venv/lib/python3.13/site-packages (from yarl<2.0,>=1.12.0->aiohttp<4.0.0,>=3.8.3->langchain_community) (0.3.0)\n", "Requirement already satisfied: sniffio>=1.1 in ./.venv/lib/python3.13/site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (1.3.1)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install langchain_community"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting pypdf\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/73/9f/78d096ef795a813fa0e1cb9b33fa574b205f2b563d9c1e9366c854cf0364/pypdf-5.7.0-py3-none-any.whl (305 kB)\n", "Installing collected packages: pypdf\n", "Successfully installed pypdf-5.7.0\n"]}], "source": ["! pip install pypdf"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["file_path='deepseek.pdf'"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Ignoring wrong pointing object 10 0 (offset 0)\n", "Ignoring wrong pointing object 21 0 (offset 0)\n", "Ignoring wrong pointing object 32 0 (offset 0)\n"]}], "source": ["from langchain_community.document_loaders import PyPDFLoader\n", "\n", "loader = PyPDFLoader(file_path)\n", "pages = []\n", "async for page in loader.alazy_load():\n", "    pages.append(page)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'producer': 'iOS Version 16.7.10 (Build 20H350) Quartz PDFContext', 'creator': 'PyPDF', 'creationdate': \"D:20250208000307Z00'00'\", 'moddate': '2025-02-08T08:06:48+08:00', 'source': 'deepseek.pdf', 'total_pages': 7, 'page': 0, 'page_label': '1'}\n", "\n", "《Deepseek R1 本地部署完全⼿册》\n", "版权归：HomeBrew Ai Club          \n", "作者wechat：samirtan\n", "版本：V2.0          \n", "更新⽇期：2025年2⽉8⽇\n", "⼀、简介\n", "Deepseek R1 是⽀持复杂推理、多模态处理、技术⽂档⽣成的⾼性能通⽤⼤语⾔模型。本⼿册\n", "为技术团队提供完整的本地部署指南，涵盖硬件配置、国产芯⽚适配、量化⽅案、云端替代⽅\n", "案及完整671B MoE模型的Ollama部署⽅法。\n", "核⼼提示：\n", "个⼈⽤户：不建议部署32B及以上模型，硬件成本极⾼且运维复杂。\n", "企业⽤户：需专业团队⽀持，部署前需评估ROI（投资回报率）。\n", "⼆、本地部署核⼼配置要求\n", "1. 模型参数与硬件对应表\n", "模型参\n", "数 Windows 配置要求 Mac 配置要求 适⽤场景\n", "1.5B\n", "- RAM: 4GB\n", "- GPU: 集成显卡/现代CPU\n", "- 存储: 5GB\n", "- 内存: 8GB\n", "（M1/M2/M3）\n", "- 存储: 5GB\n", "简单⽂本⽣成、基础代\n", "码补全\n", "7B\n", "- RAM: 8-10GB\n", "- GPU: GTX 1680（4-bit量\n", "化）\n", "- 存储: 8GB\n", "- 内存: 16GB（M2\n", "Pro/M3）\n", "- 存储: 8GB\n", "中等复杂度问答、代码\n", "调试\n", "14B\n", "- RAM: 24GB\n", "- GPU: RTX 3090（24GB\n", "VRAM）\n", "- 存储: 20GB\n", "- 内存: 32GB（M3\n", "Max）\n", "- 存储: 20GB\n", "复杂推理、技术⽂档⽣\n", "成\n", "32B+ 企业级部署（需多卡并联）暂不⽀持 科研计算、⼤规模数据\n", "处理\n"]}], "source": ["print(f\"{pages[0].metadata}\\n\")\n", "print(pages[0].page_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 多模态模型解析PDF\n", "****\n", "对于包含图片内容的PDF可以使用多模态或unstructured解析"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["file_path='z2021.pdf'"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["! pip install -qU PyMuPDF pillow langchain-openai IPython"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import base64\n", "import io\n", "\n", "import fitz\n", "from PIL import Image\n", "\n", "\n", "def pdf_page_to_base64(pdf_path: str, page_number: int):\n", "    pdf_document = fitz.open(pdf_path)\n", "    page = pdf_document.load_page(page_number - 1)  # input is one-indexed\n", "    pix = page.get_pixmap()\n", "    img = Image.frombytes(\"RGB\", [pix.width, pix.height], pix.samples)\n", "\n", "    buffer = io.BytesIO()\n", "    img.save(buffer, format=\"PNG\")\n", "\n", "    return base64.b64encode(buffer.getvalue()).decode(\"utf-8\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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***************************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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image as IPImage\n", "from IPython.display import display\n", "\n", "base64_image = pdf_page_to_base64(file_path, 11)\n", "display(IPImage(data=base64.b64decode(base64_image)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用多模态"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "import os\n", "OPENAI_API_KEY = \"hk-72n0gg10000506100b90ecb51c341412d508ccd25ed0e3ba\"\n", "OPENAI_API_BASE = \"https://api.openai-hk.com/v1\"\n", "llm = ChatOpenAI(\n", "    model=\"gpt-4o-mini\",\n", "    temperature=0,\n", "    # api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    # base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    api_key=OPENAI_API_KEY,\n", "    base_url=OPENAI_API_BASE,\n", "    )"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["根据您提供的图表，2021年中国Z时代手机消费群体中，一线城市消费者的占比为46.4%。\n"]}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "query = \"一线城市消费者占比有多少？\"\n", "\n", "message = HumanMessage(\n", "    content=[\n", "        {\"type\": \"text\", \"text\": query},\n", "        {\n", "            \"type\": \"image_url\",\n", "            \"image_url\": {\"url\": f\"data:image/jpeg;base64,{base64_image}\"},\n", "        },\n", "    ],\n", ")\n", "response = llm.invoke([message])\n", "print(response.content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 解析网页\n", "***"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["! pip install -qU langchain-community beautifulsoup4 unstructured"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["! pip install -qU langchain-unstructured"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["USER_AGENT environment variable not set, consider setting it to identify your requests.\n", "Fetching pages: 100%|##########| 1/1 [00:00<00:00,  2.68it/s]\n"]}], "source": ["import bs4\n", "from langchain_community.document_loaders import WebBaseLoader\n", "\n", "page_url = \"https://python.langchain.com/docs/how_to/chatbots_memory/\"\n", "\n", "loader = WebBaseLoader(web_paths=[page_url])\n", "docs = []\n", "async for doc in loader.alazy_load():\n", "    docs.append(doc)\n", "\n", "assert len(docs) == 1\n", "doc = docs[0]"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'source': 'https://python.langchain.com/docs/how_to/chatbots_memory/', 'title': 'How to add memory to chatbots | 🦜️🔗 LangChain', 'description': 'A key feature of chatbots is their ability to use the content of previous conversational turns as context. This state management can take several forms, including:', 'language': 'en'}\n", "\n", "How to add memory to chatbots | 🦜️🔗 LangChain\n", "\n", "\n", "\n", "\n", "\n", "\n", "Skip to main contentOur Building Ambient Agents with LangGraph course is now available on LangChain Academy!IntegrationsAPI ReferenceMoreContributingPeopleError referenceLangSmithLangGraphLangChain HubLangChain JS/TSv0.3v0.3v0.2v0.1💬SearchIntroductionTutorialsBuild a Question Answering application over a Graph DatabaseTutorialsBuild a simple LLM application with chat models and prompt templatesBuild a ChatbotBuild a Retrieval Augmented Gen\n"]}], "source": ["print(f\"{doc.metadata}\\n\")\n", "print(doc.page_content[:500].strip())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["制定加载网页某一部分"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Fetching pages: 100%|##########| 1/1 [00:00<00:00,  2.04it/s]\n"]}], "source": ["loader = WebBaseLoader(\n", "    web_paths=[page_url],\n", "    bs_kwargs={\n", "        \"parse_only\": bs4.<PERSON><PERSON>Strainer(class_=\"theme-doc-markdown markdown\"),\n", "    },\n", "    bs_get_text_kwargs={\"separator\": \" | \", \"strip\": True},\n", ")\n", "\n", "docs = []\n", "async for doc in loader.alazy_load():\n", "    docs.append(doc)\n", "\n", "assert len(docs) == 1\n", "doc = docs[0]"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'source': 'https://python.langchain.com/docs/how_to/chatbots_memory/'}\n", "\n", "How to add memory to chatbots | A key feature of chatbots is their ability to use the content of previous conversational turns as context. This state management can take several forms, including: | Simply stuffing previous messages into a chat model prompt. | The above, but trimming old messages to reduce the amount of distracting information the model has to deal with. | More complex modifications like synthesizing summaries for long running conversations. | We'll go into more detail on a few t\n"]}], "source": ["print(f\"{doc.metadata}\\n\")\n", "print(doc.page_content[:500])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["高级：不熟悉网页结构的情况下解析网页"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from langchain_unstructured import UnstructuredLoader\n", "\n", "page_url = \"https://python.langchain.com/docs/how_to/chatbots_memory/\"\n", "loader = UnstructuredLoader(web_url=page_url)\n", "\n", "docs = []\n", "async for doc in loader.alazy_load():\n", "    docs.append(doc)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Open In Colab\n", "Open on GitHub\n", "How to add memory to chatbots\n", "A key feature of chatbots is their ability to use the content of previous conversational turns as context. This state management can take several forms, including:\n", "Simply stuffing previous messages into a chat model prompt.\n"]}], "source": ["for doc in docs[:5]:\n", "    print(doc.page_content)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 加载CVS\n", "***"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["page_content='名称: 专家判断\n", "内容: 是指基于某应用领域、知识领域、学科和行业等的专业知识而做出的，关于当前活动的\n", "合理判断，这些专业知识可来自具有专业学历、知识、技能、经验或培训经历的任何小组或个人。\n", "◆组织战略；\n", "◆ 效益管理；\n", "◆ 关于项目所在的行业以及项目关注的领域的技术知识；\n", "◆ 持续时间和预算的估算；\n", "◆ 风险识别。\n", "章节部位: 4.1.1.1\n", "知识域: 项目整合管理\n", "过程: 制定项目章程' metadata={'source': 'PMBOK6.csv', 'row': 0}\n", "page_content='名称: 头脑风暴\n", "内容: 本技术用于在短时间内获得大量创意，适用于团队环境，需要引导者进行引导。\n", "头脑风暴由两个部分构成：创意产生和创意分析。制定项目章程时可通过头脑风暴向相关方、主题专家和团队成员收集数据、解决方案或创意。\n", "章节部位: 4.1.1.1\n", "知识域: 项目整合管理\n", "过程: 制定项目章程' metadata={'source': 'PMBOK6.csv', 'row': 1}\n"]}], "source": ["from langchain_community.document_loaders.csv_loader import CSVLoader\n", "\n", "file_path = \"PMBOK6.csv\"\n", "\n", "loader = CSVLoader(file_path=file_path)\n", "data = loader.load()\n", "\n", "for record in data[:2]:\n", "    print(record)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["指定一列来标识文档"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["page_content='名称: 专家判断\n", "内容: 是指基于某应用领域、知识领域、学科和行业等的专业知识而做出的，关于当前活动的\n", "合理判断，这些专业知识可来自具有专业学历、知识、技能、经验或培训经历的任何小组或个人。\n", "◆组织战略；\n", "◆ 效益管理；\n", "◆ 关于项目所在的行业以及项目关注的领域的技术知识；\n", "◆ 持续时间和预算的估算；\n", "◆ 风险识别。\n", "章节部位: 4.1.1.1\n", "知识域: 项目整合管理\n", "过程: 制定项目章程' metadata={'source': '专家判断', 'row': 0}\n", "page_content='名称: 头脑风暴\n", "内容: 本技术用于在短时间内获得大量创意，适用于团队环境，需要引导者进行引导。\n", "头脑风暴由两个部分构成：创意产生和创意分析。制定项目章程时可通过头脑风暴向相关方、主题专家和团队成员收集数据、解决方案或创意。\n", "章节部位: 4.1.1.1\n", "知识域: 项目整合管理\n", "过程: 制定项目章程' metadata={'source': '头脑风暴', 'row': 1}\n"]}], "source": ["loader = CSVLoader(file_path=file_path, source_column=\"名称\")\n", "\n", "data = loader.load()\n", "for record in data[:2]:\n", "    print(record)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 解析excel\n", "****\n", "使用微软云的Document intelligence服务来解析，需要申请KEY"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install --upgrade --quiet  langchain langchain-community azure-ai-documentintelligence"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO: Request URL: 'https://jiaoxue.cognitiveservices.azure.com//documentintelligence/documentModels/prebuilt-layout:analyze?api-version=REDACTED&outputContentFormat=REDACTED'\n", "Request method: 'POST'\n", "Request headers:\n", "    'content-type': 'application/octet-stream'\n", "    'Accept': 'application/json'\n", "    'x-ms-client-request-id': 'f9e63582-0007-11f0-b04a-ee76092d5572'\n", "    'x-ms-useragent': 'REDACTED'\n", "    'User-Agent': 'azsdk-python-ai-documentintelligence/1.0.0 Python/3.13.2 (macOS-15.3.1-arm64-arm-64bit-Mach-O)'\n", "    'Ocp-Apim-Subscription-Key': 'REDACTED'\n", "A body is sent with the request\n", "INFO: Response status: 202\n", "Response headers:\n", "    'Content-Length': '0'\n", "    'Operation-Location': 'REDACTED'\n", "    'x-envoy-upstream-service-time': 'REDACTED'\n", "    'apim-request-id': 'REDACTED'\n", "    'Strict-Transport-Security': 'REDACTED'\n", "    'x-content-type-options': 'REDACTED'\n", "    'x-ms-region': 'REDACTED'\n", "    'Date': '<PERSON><PERSON>, 13 Mar 2025 12:37:56 GMT'\n", "INFO: Request URL: 'https://jiaoxue.cognitiveservices.azure.com/documentintelligence/documentModels/prebuilt-layout/analyzeResults/a7b70e92-0dd6-4add-a940-0e6d7daeb938?api-version=REDACTED'\n", "Request method: 'GET'\n", "Request headers:\n", "    'x-ms-client-request-id': 'f9e63582-0007-11f0-b04a-ee76092d5572'\n", "    'x-ms-useragent': 'REDACTED'\n", "    'User-Agent': 'azsdk-python-ai-documentintelligence/1.0.0 Python/3.13.2 (macOS-15.3.1-arm64-arm-64bit-Mach-O)'\n", "    'Ocp-Apim-Subscription-Key': 'REDACTED'\n", "No body was attached to the request\n", "INFO: Response status: 200\n", "Response headers:\n", "    'Content-Length': '1653370'\n", "    'Content-Type': 'application/json; charset=utf-8'\n", "    'x-envoy-upstream-service-time': 'REDACTED'\n", "    'apim-request-id': 'REDACTED'\n", "    'Strict-Transport-Security': 'REDACTED'\n", "    'x-content-type-options': 'REDACTED'\n", "    'x-ms-region': 'REDACTED'\n", "    'Date': '<PERSON><PERSON>, 13 Mar 2025 12:37:57 GMT'\n"]}], "source": ["from langchain_community.document_loaders import AzureAIDocumentIntelligenceLoader\n", "\n", "file_path = \"PMBOK62.xlsx\"\n", "endpoint =\"https://jiaoxue.cognitiveservices.azure.com/\"\n", "key = \"FdsAwGHwsQhEKkEYUN1OaBvR4a5f8GXJqPMPMYTAM70lABs1QgSHJQQJ99BCAC8vTInXJ3w3AAALACOGTQvM\"\n", "loader = AzureAIDocumentIntelligenceLoader(\n", "    api_endpoint=endpoint, api_key=key, file_path=file_path, api_model=\"prebuilt-layout\"\n", ")\n", "\n", "documents = loader.load()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 自定义文档加载器\n", "*****\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["一个文档加载器包含的主要方法"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](loader.png)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: aiofiles in d:\\anaconda3\\envs\\py313\\lib\\site-packages (24.1.0)\n"]}], "source": ["! pip install aiofiles"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["from typing import AsyncIterator, Iterator\n", "\n", "from langchain_core.document_loaders import BaseLoader\n", "from langchain_core.documents import Document\n", "\n", "\n", "class CustomDocumentLoader(BaseLoader):\n", "    \"\"\"逐行读取文件的文档加载器示例\"\"\"\n", "\n", "    def __init__(self, file_path: str) -> None:\n", "        \"\"\"使用文件路径初始化加载器\n", "\n", "        参数:\n", "            file_path: 要加载的文件路径\n", "        \"\"\"\n", "        self.file_path = file_path\n", "\n", "    def lazy_load(self) -> Iterator[Document]:  # <-- 不接受任何参数\n", "        \"\"\"逐行读取文件的惰性加载器\n", "\n", "        当实现惰性加载方法时,你应该使用生成器\n", "        一次生成一个文档\n", "        \"\"\"\n", "        with open(self.file_path, encoding=\"utf-8\") as f:\n", "            line_number = 0\n", "            for line in f:\n", "                yield Document(\n", "                    page_content=line,\n", "                    metadata={\"line_number\": line_number, \"source\": self.file_path},\n", "                )\n", "                line_number += 1\n", "\n", "    # alazy_load 是可选的\n", "    # 如果不实现它,将使用一个默认实现,该实现会委托给 lazy_load!\n", "    async def alazy_load(\n", "        self,\n", "    ) -> AsyncIterator[Document]:  # <-- 不接受任何参数\n", "        \"\"\"逐行读取文件的异步惰性加载器\"\"\"\n", "        # 需要 aiofiles (通过 pip 安装)\n", "        # https://github.com/Tinche/aiofiles\n", "        import aiofiles\n", "\n", "        async with aiofiles.open(self.file_path, encoding=\"utf-8\") as f:\n", "            line_number = 0\n", "            async for line in f:\n", "                yield Document(\n", "                    page_content=line,\n", "                    metadata={\"line_number\": line_number, \"source\": self.file_path},\n", "                )\n", "                line_number += 1\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["测试"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["with open(\"meow.txt\", \"w\", encoding=\"utf-8\") as f:\n", "    quality_content = \"meow meow🐱 \\n meow meow🐱 \\n meow😻😻\"\n", "    f.write(quality_content)\n", "\n", "loader = CustomDocumentLoader(\"meow.txt\")"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "<class 'langchain_core.documents.base.Document'>\n", "page_content='meow meow🐱 \n", "' metadata={'line_number': 0, 'source': 'meow.txt'}\n", "\n", "<class 'langchain_core.documents.base.Document'>\n", "page_content=' meow meow🐱 \n", "' metadata={'line_number': 1, 'source': 'meow.txt'}\n", "\n", "<class 'langchain_core.documents.base.Document'>\n", "page_content=' meow😻😻' metadata={'line_number': 2, 'source': 'meow.txt'}\n"]}], "source": ["## 测试懒加载\n", "for doc in loader.lazy_load():\n", "    print()\n", "    print(type(doc))\n", "    print(doc)"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}