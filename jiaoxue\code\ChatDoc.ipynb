{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## ChatDoc: 又一个文档对话助手\n", "****\n", "- 读取pdf、excel、doc三种常见的文档格式\n", "- 根据文档内容，智能抽取内容并输出相应格式"]}, {"cell_type": "markdown", "metadata": {}, "source": ["********\n", "基本环境:\n", "- mac系统\n", "- python 3.13\n", "- langchain 0.3"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting docx2txt==0.8\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/7d/7d/60ee3f2b16d9bfdfa72e8599470a2c1a5b759cb113c6fe1006be28359327/docx2txt-0.8.tar.gz (2.8 kB)\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Building wheels for collected packages: docx2txt\n", "  Building wheel for docx2txt (pyproject.toml): started\n", "  Building wheel for docx2txt (pyproject.toml): finished with status 'done'\n", "  Created wheel for docx2txt: filename=docx2txt-0.8-py3-none-any.whl size=4037 sha256=661b13f0d8e40a4274d36a2b4eb6855f5f60031bfebfd81d16db33ad5ba6c4be\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\fe\\f3\\db\\4fda94268da8b158beddc8281243960ce34e3ecb264e6f582e\n", "Successfully built docx2txt\n", "Installing collected packages: docx2txt\n", "Successfully installed docx2txt-0.8\n"]}], "source": ["! pip install docx2txt==0.8"]}, {"cell_type": "markdown", "metadata": {}, "source": ["第一步构造loader"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'fake.docx'}, page_content='一、公司基本信息\\n\\n名称：宏图科技发展有限公司\\n\\n注册地址：江苏省南京市雨花台区软件大道101号\\n\\n成立日期：2011年5月16日\\n\\n法定代表人：李强\\n\\n注册资本：人民币5000万元\\n\\n员工人数：约200人\\n\\n联系电话：025-88888888\\n\\n电子邮箱：<EMAIL>\\n\\n\\n\\n二、财务状况概述\\n\\n截至2023年第一季度，宏图科技发展有限公司财务状况堪忧，具体情况如下：\\n\\n1. 资产总额：人民币1.2亿元，较上年同期下降30%。\\n\\n2. 负债总额：人民币1.8亿元，较上年同期上升50%，资不抵债。\\n\\n3. 营业收入：人民币3000万元，较上年同期下降60%。\\n\\n4. 净利润：亏损人民币800万元，去年同期为盈利人民币200万元。\\n\\n5. 现金流量：公司现金流量紧张，现金及现金等价物余额为人民币500万元，难以支撑日常运营。\\n\\n6. 存货：存货积压严重，库存商品价值约为人民币400万元，大部分产品滞销。\\n\\n7. 应收账款：应收账款高达人民币600万元，回收难度大，坏账准备不足。\\n\\n\\n\\n三、主营业务及市场状况\\n\\n宏图科技发展有限公司主要从事计算机软件的研发与销售。近年来，由于市场竞争加剧、技术更新换代速度快和管理层决策失误等原因，公司主营业务收入持续下降。目前，公司面临的主要问题有：\\n\\n1. 产品同质化严重，缺乏核心竞争力。\\n\\n2. 新产品开发进度缓慢，未能及时抓住市场需求变化。\\n\\n3. 市场营销策略不当，导致市场份额大幅缩水。\\n\\n4. 行业内新兴企业崛起迅速，原有客户流失严重。\\n\\n\\n\\n四、债权债务情况\\n\\n宏图科技发展有限公司目前面临的债务问题严峻，具体情况如下：\\n\\n1. 银行贷款：公司向多家银行贷款总额达人民币1亿元，部分贷款已逾期未还。\\n\\n2. 供应商欠款：因现金流紧张，公司拖欠供应商货款达人民币300万元。\\n\\n3. 员工工资及社保：由于资金链断裂，公司拖欠员工工资及社保费用共计人民币200万元。\\n\\n4. 其他应付款项：包括税费、租赁费用等其他应付款项累计约人民币100万元。\\n\\n\\n\\n五、资产清单\\n\\n宏图科技发展有限公司目前拥有的主要资产包括：\\n\\n1. 固定资产：公司办公用房和设备原值合计人民币800万元，累计折旧约400万元。\\n\\n2. 无形资产：包括软件著作权、专利权等无形资产原值合计人民币300万元。\\n\\n3. 存货资产：存货包括已完成软件产品和半成品，价值约为人民币400万元。\\n\\n4. 应收账款：主要包括对外销售软件的应收账款合计人民币600万元。\\n\\n\\n\\n六、潜在风险及预警\\n\\n1. 经营风险：由于连续亏损，公司可能面临破产清算的风险。\\n\\n2. 债务风险：负债累累，若短期内无法筹措足够资金偿还债务，可能面临诉讼或资产被查封的风险。\\n\\n3. 市场风险：行业竞争加剧和市场需求不明朗，可能导致公司未来业绩继续恶化。\\n\\n4. 法律风险：因未能按时支付债务和相关费用，可能面临相关法律诉讼或处罚。\\n\\n\\n\\n七、结论与建议\\n\\n综上所述，宏图科技发展有限公司目前处于财务困境之中，若无外部资金注入或业务转型成功，短期内难以扭转局势。对于不良资产收购方来说，在考虑收购宏图科技的相关资产前，建议进行深入的尽职调查，并制定详细的风险控制和资产处置方案。同时，在估值时应充分考虑到公司所面临的各种潜在风险和清收难度。\\n\\n\\n\\n报告撰写日期：2023年4月20日')]"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_community.document_loaders import Docx2txtLoader\n", "#定义chatdoc\n", "class ChatDoc():\n", "    def getFile():\n", "        #读取文件\n", "        # Load the text from the docx file\n", "        loader = Docx2txtLoader(\"fake.docx\")\n", "        text = loader.load()\n", "        return text\n", "\n", "ChatDoc.getFile()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["加载pdf文档"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:pypdf._reader:Ignoring wrong pointing object 6 0 (offset 0)\n"]}, {"data": {"text/plain": ["[Document(metadata={'producer': 'macOS 版本14.1.1（版号23B81） Quartz PDFContext', 'creator': 'PyPDF', 'creationdate': \"D:20231205083748Z00'00'\", 'moddate': \"D:20231205083748Z00'00'\", 'source': 'fake.pdf', 'total_pages': 3, 'page': 0, 'page_label': '1'}, page_content='一、公司基本信息 名称：宏图科技发展有限公司 注册地址：江苏省南京市雨花台区软件大道101号 成立日期：2011年5月16日 法定代表人：李强 注册资本：人民币5000万元 员工人数：约200人 联系电话：025-88888888 电子邮箱：<EMAIL>  二、财务状况概述 截至2023年第一季度，宏图科技发展有限公司财务状况堪忧，具体情况如下： 1. 资产总额：人民币1.2亿元，较上年同期下降30%。 2. 负债总额：人民币1.8亿元，较上年同期上升50%，资不抵债。 3. 营业收入：人民币3000万元，较上年同期下降60%。 4. 净利润：亏损人民币800万元，去年同期为盈利人民币200万元。 5. 现金流量：公司现金流量紧张，现金及现金等价物余额为人民币500万元，难以支撑日常运营。 6. 存货： 存货积压严重， 库存商品价值约为人民币400万元， 大部分产品滞销。 7. 应收账款：应收账款高达人民币600万元，回收难度大，坏账准备不足。  三、主营业务及市场状况 宏图科技发展有限公司主要从事计算机软件的研发与销售。近年来，由于市场竞争加剧、技术更新换代速度快和管理层决策失误等原因，公司主营业务收入持续下降。目前，公司面临的主要问题有： 1. 产品同质化严重，缺乏核心竞争力。 2. 新产品开发进度缓慢，未能及时抓住市场需求变化。 3. 市场营销策略不当，导致市场份额大幅缩水。'),\n", " Document(metadata={'producer': 'macOS 版本14.1.1（版号23B81） Quartz PDFContext', 'creator': 'PyPDF', 'creationdate': \"D:20231205083748Z00'00'\", 'moddate': \"D:20231205083748Z00'00'\", 'source': 'fake.pdf', 'total_pages': 3, 'page': 1, 'page_label': '2'}, page_content='4. 行业内新兴企业崛起迅速，原有客户流失严重。  四、债权债务情况 宏图科技发展有限公司目前面临的债务问题严峻，具体情况如下： 1. 银行贷款： 公司向多家银行贷款总额达人民币1亿元， 部分贷款已逾期未还。 2. 供应商欠款：因现金流紧张，公司拖欠供应商货款达人民币300万元。 3. 员工工资及社保：由于资金链断裂，公司拖欠员工工资及社保费用共计人民币200万元。 4. 其他应付款项： 包括税费、 租赁费用等其他应付款项累计约人民币100万元。  五、资产清单 宏图科技发展有限公司目前拥有的主要资产包括： 1. 固定资产：公司办公用房和设备原值合计人民币800万元，累计折旧约400万元。 2. 无形资产：包括软件著作权、专利权等无形资产原值合计人民币300万元。 3. 存货资产：存货包括已完成软件产品和半成品，价值约为人民币400万元。 4. 应收账款：主要包括对外销售软件的应收账款合计人民币600万元。  六、潜在风险及预警 1. 经营风险：由于连续亏损，公司可能面临破产清算的风险。 2. 债务风险：负债累累，若短期内无法筹措足够资金偿还债务，可能面临诉讼或资产被查封的风险。 3. 市场风险：行业竞争加剧和市场需求不明朗，可能导致公司未来业绩继续恶化。 4. 法律风险： 因未能按时支付债务和相关费用， 可能面临相关法律诉讼或处罚。  七、结论与建议 综上所述，宏图科技发展有限公司目前处于财务困境之中，若无外部资金注入或业务转型成功，短期内难以扭转局势。对于不良资产收购方来说，在考虑收购宏'),\n", " Document(metadata={'producer': 'macOS 版本14.1.1（版号23B81） Quartz PDFContext', 'creator': 'PyPDF', 'creationdate': \"D:20231205083748Z00'00'\", 'moddate': \"D:20231205083748Z00'00'\", 'source': 'fake.pdf', 'total_pages': 3, 'page': 2, 'page_label': '3'}, page_content='图科技的相关资产前，建议进行深入的尽职调查，并制定详细的风险控制和资产处置方案。 同时， 在估值时应充分考虑到公司所面临的各种潜在风险和清收难度。  报告撰写日期：2023年4月20日')]"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["#导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import PyPDFLoader\n", "#定义chatdoc\n", "class ChatDoc():\n", "    def getFile():\n", "        try:\n", "            #读取文件\n", "            # Load the text from the pdf file\n", "            loader = PyPDFLoader(\"fake.pdf\")\n", "            text = loader.load()\n", "            return text\n", "        except Exception as e:\n", "            print(f\"Error loading files:{e}\")\n", "ChatDoc.getFile()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["加载下excel"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: pandas in ./.venv/lib/python3.13/site-packages (2.2.3)\n", "Requirement already satisfied: openpyxl in ./.venv/lib/python3.13/site-packages (3.1.5)\n", "Requirement already satisfied: numpy>=1.26.0 in ./.venv/lib/python3.13/site-packages (from pandas) (1.26.4)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in ./.venv/lib/python3.13/site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in ./.venv/lib/python3.13/site-packages (from pandas) (2025.1)\n", "Requirement already satisfied: tzdata>=2022.7 in ./.venv/lib/python3.13/site-packages (from pandas) (2025.1)\n", "Requirement already satisfied: et-xmlfile in ./.venv/lib/python3.13/site-packages (from openpyxl) (2.0.0)\n", "Requirement already satisfied: six>=1.5 in ./.venv/lib/python3.13/site-packages (from python-dateutil>=2.8.2->pandas) (1.17.0)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install pandas openpyxl"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'source': 'fake.xlsx', 'filename': 'fake.xlsx', 'last_modified': '2025-03-13T19:47:13', 'page_name': 'Sheet1', 'page_number': 1, 'text_as_html': '<table><tr><td>名称</td><td>宏图科技发展有限公司</td></tr><tr><td>注册地址</td><td>江苏省南京市雨花台区软件大道101号</td></tr><tr><td>成立日期</td><td>40679</td></tr><tr><td>法定代表人</td><td>李强</td></tr><tr><td>注册资本</td><td>人民币5000万元</td></tr><tr><td>员工人数</td><td>约200人</td></tr><tr><td>联系电话</td><td>025-88888888</td></tr><tr><td>电子邮箱</td><td><EMAIL></td></tr><tr><td>资产总额</td><td>人民币1.2亿元，较上年同期下降30%</td></tr><tr><td>负债总额</td><td>人民币1.8亿元，较上年同期上升50%，资不抵债</td></tr><tr><td>营业收入</td><td>人民币3000万元，较上年同期下降60%</td></tr><tr><td>净利润</td><td>亏损人民币800万元，去年同期为盈利人民币200万元</td></tr><tr><td>现金流量</td><td>公司现金流量紧张，现金及现金等价物余额为人民币500万元，难以支撑日常运营</td></tr></table>', 'languages': ['zho', 'kor'], 'filetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'category': 'Table', 'element_id': '7ed30bdc7ad64071dae72ac7a379911a'}, page_content='名称 宏图科技发展有限公司 注册地址 江苏省南京市雨花台区软件大道101号 成立日期 40679 法定代表人 李强 注册资本 人民币5000万元 员工人数 约200人 联系电话 025-88888888 电子邮箱 <EMAIL> 资产总额 人民币1.2亿元，较上年同期下降30% 负债总额 人民币1.8亿元，较上年同期上升50%，资不抵债 营业收入 人民币3000万元，较上年同期下降60% 净利润 亏损人民币800万元，去年同期为盈利人民币200万元 现金流量 公司现金流量紧张，现金及现金等价物余额为人民币500万元，难以支撑日常运营')]"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["#导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader\n", "\n", "#定义chatdoc\n", "#defining ChatDoc\n", "class ChatDoc():\n", "    def getFile():\n", "        try:\n", "            #读取文件\n", "            # Load the text from the excel file\n", "            loader = UnstructuredExcelLoader(\"fake.xlsx\",mode=\"elements\")\n", "            text = loader.load()\n", "            return text\n", "        except Exception as e:\n", "            print(f\"Error loading files:{e}\")\n", "ChatDoc.getFile()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["整合优化，动态加载三种文件格式,增加了文本切割"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Document(metadata={'source': 'fake.xlsx'}, page_content='名称 宏图科技发展有限公司 注册地址 江苏省南京市雨花台区软件大道101号 成立日期 40679 法定代表人 李强 注册资本 人民币5000万元 员工人数 约200人 联系电话 025-88888888 电子邮箱 <EMAIL> 资产总额 人民币1.2亿元，较上年同期下降30% 负债总额 人民币1.8亿元，较上年同期上升50%，资不抵债 营业收入 人民币3000万元，较上年同期下降60% 净利润 亏损人民币800万元，去年同期为盈利人民币200万元 现金流量 公司现金流量紧张，现金及现金等价物余额为人民币500万元，难以支撑日常运营')]\n"]}], "source": ["#导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader,PyPDFLoader\n", "from langchain_text_splitters import  CharacterTextSplitter\n", "\n", "\n", "#定义chatdoc\n", "#defining ChatDoc\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = [] #分割后的文本 splited text\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e: \n", "                print(f\"Error loading {file_extension} files:{e}\") \n", "        else:\n", "             print(f\"Unsupported file extension: {file_extension}\")\n", "             return  None \n", "\n", "    #处理文档的函数\n", "    #function to process the document\n", "    def splitSentences(self):\n", "        full_text = self.getFile() #获取文档内容 get the document content\n", "        if full_text != None:\n", "            #对文档进行分割\n", "            #split the document\n", "            text_split = CharacterTextSplitter(\n", "                chunk_size=150,\n", "                chunk_overlap=20,\n", "            )\n", "            texts = text_split.split_documents(full_text)\n", "            self.splitText = texts\n", "\n", "chat_doc = ChatDoc()\n", "chat_doc.doc = \"fake.xlsx\"\n", "chat_doc.splitSentences()\n", "print(chat_doc.splitText)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["向量化与存储索引"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: langchain_chroma in ./.venv/lib/python3.13/site-packages (0.2.2)\n", "Requirement already satisfied: langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.2,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43 in ./.venv/lib/python3.13/site-packages (from langchain_chroma) (0.3.43)\n", "Requirement already satisfied: numpy<2.0.0,>=1.26.2 in ./.venv/lib/python3.13/site-packages (from langchain_chroma) (1.26.4)\n", "Requirement already satisfied: chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0 in ./.venv/lib/python3.13/site-packages (from langchain_chroma) (0.6.3)\n", "Requirement already satisfied: build>=1.0.3 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.2.2.post1)\n", "Requirement already satisfied: pydantic>=1.9 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2.10.6)\n", "Requirement already satisfied: chroma-hnswlib==0.7.6 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.7.6)\n", "Requirement already satisfied: fastapi>=0.95.2 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.115.11)\n", "Requirement already satisfied: uvicorn>=0.18.3 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.34.0)\n", "Requirement already satisfied: posthog>=2.4.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (3.19.1)\n", "Requirement already satisfied: typing_extensions>=4.5.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (4.12.2)\n", "Requirement already satisfied: onnxruntime>=1.14.1 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.21.0)\n", "Requirement already satisfied: opentelemetry-api>=1.2.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.31.0)\n", "Requirement already satisfied: opentelemetry-exporter-otlp-proto-grpc>=1.2.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.31.0)\n", "Requirement already satisfied: opentelemetry-instrumentation-fastapi>=0.41b0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.52b0)\n", "Requirement already satisfied: opentelemetry-sdk>=1.2.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.31.0)\n", "Requirement already satisfied: tokenizers>=0.13.2 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.21.0)\n", "Requirement already satisfied: pypika>=0.48.9 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.48.9)\n", "Requirement already satisfied: tqdm>=4.65.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (4.67.1)\n", "Requirement already satisfied: overrides>=7.3.1 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (7.7.0)\n", "Requirement already satisfied: importlib-resources in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (6.5.2)\n", "Requirement already satisfied: grpcio>=1.58.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.71.0)\n", "Requirement already satisfied: bcrypt>=4.0.1 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (4.3.0)\n", "Requirement already satisfied: typer>=0.9.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.15.2)\n", "Requirement already satisfied: kubernetes>=28.1.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (32.0.1)\n", "Requirement already satisfied: tenacity>=8.2.3 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (9.0.0)\n", "Requirement already satisfied: PyYAML>=6.0.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (6.0.2)\n", "Requirement already satisfied: mmh3>=4.0.1 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (4.1.0)\n", "Requirement already satisfied: orjson>=3.9.12 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (3.10.15)\n", "Requirement already satisfied: httpx>=0.27.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.28.1)\n", "Requirement already satisfied: rich>=10.11.0 in ./.venv/lib/python3.13/site-packages (from chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (13.9.4)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in ./.venv/lib/python3.13/site-packages (from langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.2,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langchain_chroma) (0.3.13)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in ./.venv/lib/python3.13/site-packages (from langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.2,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langchain_chroma) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in ./.venv/lib/python3.13/site-packages (from langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.2,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langchain_chroma) (24.2)\n", "Requirement already satisfied: pyproject_hooks in ./.venv/lib/python3.13/site-packages (from build>=1.0.3->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.2.0)\n", "Requirement already satisfied: starlette<0.47.0,>=0.40.0 in ./.venv/lib/python3.13/site-packages (from fastapi>=0.95.2->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.46.1)\n", "Requirement already satisfied: anyio in ./.venv/lib/python3.13/site-packages (from httpx>=0.27.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (4.8.0)\n", "Requirement already satisfied: certifi in ./.venv/lib/python3.13/site-packages (from httpx>=0.27.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in ./.venv/lib/python3.13/site-packages (from httpx>=0.27.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.0.7)\n", "Requirement already satisfied: idna in ./.venv/lib/python3.13/site-packages (from httpx>=0.27.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (3.10)\n", "Requirement already satisfied: h11<0.15,>=0.13 in ./.venv/lib/python3.13/site-packages (from httpcore==1.*->httpx>=0.27.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in ./.venv/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.2,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langchain_chroma) (3.0.0)\n", "Requirement already satisfied: six>=1.9.0 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.17.0)\n", "Requirement already satisfied: python-dateutil>=2.5.3 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2.9.0.post0)\n", "Requirement already satisfied: google-auth>=1.0.1 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2.38.0)\n", "Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.8.0)\n", "Requirement already satisfied: requests in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2.32.3)\n", "Requirement already satisfied: requests-oauthlib in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2.0.0)\n", "Requirement already satisfied: oauthlib>=3.2.2 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (3.2.2)\n", "Requirement already satisfied: urllib3>=1.24.2 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2.3.0)\n", "Requirement already satisfied: durationpy>=0.7 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.9)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.2,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langchain_chroma) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core!=0.3.0,!=0.3.1,!=0.3.10,!=0.3.11,!=0.3.12,!=0.3.13,!=0.3.14,!=0.3.2,!=0.3.3,!=0.3.4,!=0.3.5,!=0.3.6,!=0.3.7,!=0.3.8,!=0.3.9,<0.4.0,>=0.2.43->langchain_chroma) (0.23.0)\n", "Requirement already satisfied: coloredlogs in ./.venv/lib/python3.13/site-packages (from onnxruntime>=1.14.1->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (15.0.1)\n", "Requirement already satisfied: flatbuffers in ./.venv/lib/python3.13/site-packages (from onnxruntime>=1.14.1->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (25.2.10)\n", "Requirement already satisfied: protobuf in ./.venv/lib/python3.13/site-packages (from onnxruntime>=1.14.1->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (5.29.3)\n", "Requirement already satisfied: sympy in ./.venv/lib/python3.13/site-packages (from onnxruntime>=1.14.1->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.13.3)\n", "Requirement already satisfied: deprecated>=1.2.6 in ./.venv/lib/python3.13/site-packages (from opentelemetry-api>=1.2.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.2.18)\n", "Requirement already satisfied: importlib-metadata<8.7.0,>=6.0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-api>=1.2.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (8.6.1)\n", "Requirement already satisfied: googleapis-common-protos~=1.52 in ./.venv/lib/python3.13/site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.69.1)\n", "Requirement already satisfied: opentelemetry-exporter-otlp-proto-common==1.31.0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.31.0)\n", "Requirement already satisfied: opentelemetry-proto==1.31.0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.31.0)\n", "Requirement already satisfied: opentelemetry-instrumentation-asgi==0.52b0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.52b0)\n", "Requirement already satisfied: opentelemetry-instrumentation==0.52b0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.52b0)\n", "Requirement already satisfied: opentelemetry-semantic-conventions==0.52b0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.52b0)\n", "Requirement already satisfied: opentelemetry-util-http==0.52b0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-instrumentation-fastapi>=0.41b0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.52b0)\n", "Requirement already satisfied: wrapt<2.0.0,>=1.0.0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-instrumentation==0.52b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.17.2)\n", "Requirement already satisfied: asgiref~=3.0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-instrumentation-asgi==0.52b0->opentelemetry-instrumentation-fastapi>=0.41b0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (3.8.1)\n", "Requirement already satisfied: monotonic>=1.5 in ./.venv/lib/python3.13/site-packages (from posthog>=2.4.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.6)\n", "Requirement already satisfied: backoff>=1.10.0 in ./.venv/lib/python3.13/site-packages (from posthog>=2.4.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2.2.1)\n", "Requirement already satisfied: distro>=1.5.0 in ./.venv/lib/python3.13/site-packages (from posthog>=2.4.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.9.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.13/site-packages (from pydantic>=1.9->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.13/site-packages (from pydantic>=1.9->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2.27.2)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in ./.venv/lib/python3.13/site-packages (from rich>=10.11.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in ./.venv/lib/python3.13/site-packages (from rich>=10.11.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2.19.1)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in ./.venv/lib/python3.13/site-packages (from tokenizers>=0.13.2->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.29.3)\n", "Requirement already satisfied: click>=8.0.0 in ./.venv/lib/python3.13/site-packages (from typer>=0.9.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (8.1.8)\n", "Requirement already satisfied: shellingham>=1.3.0 in ./.venv/lib/python3.13/site-packages (from typer>=0.9.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.5.4)\n", "Requirement already satisfied: httptools>=0.6.3 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.6.4)\n", "Requirement already satisfied: python-dotenv>=0.13 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.0.1)\n", "Requirement already satisfied: uvloop!=0.15.0,!=0.15.1,>=0.14.0 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.21.0)\n", "Requirement already satisfied: watchfiles>=0.13 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.0.4)\n", "Requirement already satisfied: websockets>=10.4 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (15.0.1)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in ./.venv/lib/python3.13/site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (5.5.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in ./.venv/lib/python3.13/site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.4.1)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in ./.venv/lib/python3.13/site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (4.9)\n", "Requirement already satisfied: filelock in ./.venv/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (3.17.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in ./.venv/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (2025.3.0)\n", "Requirement already satisfied: zipp>=3.20 in ./.venv/lib/python3.13/site-packages (from importlib-metadata<8.7.0,>=6.0->opentelemetry-api>=1.2.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (3.21.0)\n", "Requirement already satisfied: mdurl~=0.1 in ./.venv/lib/python3.13/site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.1.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.13/site-packages (from requests->kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (3.4.1)\n", "Requirement already satisfied: sniffio>=1.1 in ./.venv/lib/python3.13/site-packages (from anyio->httpx>=0.27.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.3.1)\n", "Requirement already satisfied: humanfriendly>=9.1 in ./.venv/lib/python3.13/site-packages (from coloredlogs->onnxruntime>=1.14.1->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (10.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in ./.venv/lib/python3.13/site-packages (from sympy->onnxruntime>=1.14.1->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (1.3.0)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in ./.venv/lib/python3.13/site-packages (from pyasn1-modules>=0.2.1->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb!=0.5.10,!=0.5.11,!=0.5.12,!=0.5.4,!=0.5.5,!=0.5.7,!=0.5.9,<0.7.0,>=0.4.0->langchain_chroma) (0.6.1)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install langchain_chroma"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"data": {"text/plain": ["<langchain_chroma.vectorstores.Chroma at 0x308bbedf0>"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["#导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader,PyPDFLoader\n", "from langchain_text_splitters import  CharacterTextSplitter\n", "from langchain_chroma import Chroma\n", "from langchain_openai import OpenAIEmbeddings\n", "import os\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\")+\"/v1\",\n", ")\n", "\n", "#定义chatdoc\n", "#defining ChatDoc\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = [] #分割后的文本 splited text\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e: \n", "                print(f\"Error loading {file_extension} files:{e}\") \n", "        else:\n", "             print(f\"Unsupported file extension: {file_extension}\")\n", "             return  None \n", "\n", "    #处理文档的函数\n", "    #function to process the document\n", "    def splitSentences(self):\n", "        full_text = self.getFile() #获取文档内容 get the document content\n", "        if full_text != None:\n", "            #对文档进行分割\n", "            #split the document\n", "            text_split = CharacterTextSplitter(\n", "                chunk_size=150,\n", "                chunk_overlap=20,\n", "            )\n", "            texts = text_split.split_documents(full_text)\n", "            self.splitText = texts\n", "    \n", "    #向量化与向量存储\n", "    #Embed\n", "    def embeddingAndVectorDB(self):\n", "        db =Chroma.from_documents(\n", "            documents = self.splitText,\n", "            embedding = embeddings_model,\n", "        )\n", "        return db\n", "\n", "chat_doc = ChatDoc()\n", "chat_doc.doc = \"fake.docx\"\n", "chat_doc.splitSentences()\n", "chat_doc.embeddingAndVectorDB()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["索引并使用自然语言找出相关的文本块"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"data": {"text/plain": ["[Document(id='b535dae9-0756-418e-adca-e7062917b0f6', metadata={'source': 'fake.docx'}, page_content='1. 银行贷款：公司向多家银行贷款总额达人民币1亿元，部分贷款已逾期未还。\\n\\n2. 供应商欠款：因现金流紧张，公司拖欠供应商货款达人民币300万元。\\n\\n3. 员工工资及社保：由于资金链断裂，公司拖欠员工工资及社保费用共计人民币200万元。'),\n", " Document(id='d8605ae4-d1f7-422b-92ac-7fdf1d7abdc8', metadata={'source': 'fake.docx'}, page_content='1. 银行贷款：公司向多家银行贷款总额达人民币1亿元，部分贷款已逾期未还。\\n\\n2. 供应商欠款：因现金流紧张，公司拖欠供应商货款达人民币300万元。\\n\\n3. 员工工资及社保：由于资金链断裂，公司拖欠员工工资及社保费用共计人民币200万元。'),\n", " Document(id='4d880797-b48d-4aa4-a10d-a896ee2e4a87', metadata={'source': 'fake.docx'}, page_content='1. 银行贷款：公司向多家银行贷款总额达人民币1亿元，部分贷款已逾期未还。\\n\\n2. 供应商欠款：因现金流紧张，公司拖欠供应商货款达人民币300万元。\\n\\n3. 员工工资及社保：由于资金链断裂，公司拖欠员工工资及社保费用共计人民币200万元。'),\n", " Document(id='7631fd69-d60c-4fde-8065-871b057a1935', metadata={'source': 'fake.docx'}, page_content='1. 银行贷款：公司向多家银行贷款总额达人民币1亿元，部分贷款已逾期未还。\\n\\n2. 供应商欠款：因现金流紧张，公司拖欠供应商货款达人民币300万元。\\n\\n3. 员工工资及社保：由于资金链断裂，公司拖欠员工工资及社保费用共计人民币200万元。')]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["#导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader,PyPDFLoader\n", "from langchain_text_splitters import  CharacterTextSplitter\n", "from langchain_chroma import Chroma\n", "from langchain_openai import OpenAIEmbeddings\n", "import os\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\")+\"/v1\",\n", ")\n", "\n", "#定义chatdoc\n", "#defining ChatDoc\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = [] #分割后的文本 splited text\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e: \n", "                print(f\"Error loading {file_extension} files:{e}\") \n", "        else:\n", "             print(f\"Unsupported file extension: {file_extension}\")\n", "             return  None \n", "\n", "    #处理文档的函数\n", "    #function to process the document\n", "    def splitSentences(self):\n", "        full_text = self.getFile() #获取文档内容 get the document content\n", "        if full_text != None:\n", "            #对文档进行分割\n", "            #split the document\n", "            text_split = CharacterTextSplitter(\n", "                chunk_size=150,\n", "                chunk_overlap=20,\n", "            )\n", "            texts = text_split.split_documents(full_text)\n", "            self.splitText = texts\n", "    \n", "    #向量化与向量存储\n", "    #Embed\n", "    def embeddingAndVectorDB(self):\n", "        db =Chroma.from_documents(\n", "            documents = self.splitText,\n", "            embedding = embeddings_model,\n", "        )\n", "        return db\n", "    \n", "    #提问并找到相关的文本块，使用最简单的检索器\n", "    #Ask and find relevant text blocks\n", "    def askAndFindFiles(self,question):\n", "        db = self.embeddingAndVectorDB()\n", "        retriever = db.as_retriever()\n", "        results = retriever.invoke(question)\n", "        return results\n", "\n", "chat_doc = ChatDoc()\n", "chat_doc.doc = \"fake.docx\"\n", "chat_doc.splitSentences()\n", "chat_doc.askAndFindFiles(\"这家公司叫什么名字?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用查询重写提高文档检索精确度"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:urllib3.connectionpool:Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'RemoteDisconnected('Remote end closed connection without response')': /batch/\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:langchain.retrievers.multi_query:Generated queries: ['1. 这家企业的正式名称是什么？  ', '2. 该公司的注册名称是什么？  ', '3. 请问这家公司的全称是什么？']\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[Document(id='848d3a9c-f36a-4352-bf1f-d35699e7a1a6', metadata={'source': 'fake.docx'}, page_content='报告撰写日期：2023年4月20日'), Document(id='b56dfb1b-72d9-4d68-b861-2911e135e99c', metadata={'source': 'fake.docx'}, page_content='报告撰写日期：2023年4月20日'), Document(id='91a79a4c-b956-4cb4-9857-0d8bc96df8c2', metadata={'source': 'fake.docx'}, page_content='报告撰写日期：2023年4月20日'), Document(id='28fd20d5-14b0-4589-b7f6-25dc2321e40a', metadata={'source': 'fake.docx'}, page_content='报告撰写日期：2023年4月20日'), Document(id='65dfa7bd-5e84-431e-9ba4-d09e7f8eb9cc', metadata={'source': 'fake.docx'}, page_content='一、公司基本信息\\n\\n名称：宏图科技发展有限公司\\n\\n注册地址：江苏省南京市雨花台区软件大道101号\\n\\n成立日期：2011年5月16日\\n\\n法定代表人：李强\\n\\n注册资本：人民币5000万元\\n\\n员工人数：约200人\\n\\n联系电话：025-88888888\\n\\n电子邮箱：<EMAIL>'), Document(id='d97ec21a-f28a-4641-ab0b-3b83fb5de95f', metadata={'source': 'fake.docx'}, page_content='一、公司基本信息\\n\\n名称：宏图科技发展有限公司\\n\\n注册地址：江苏省南京市雨花台区软件大道101号\\n\\n成立日期：2011年5月16日\\n\\n法定代表人：李强\\n\\n注册资本：人民币5000万元\\n\\n员工人数：约200人\\n\\n联系电话：025-88888888\\n\\n电子邮箱：<EMAIL>'), Document(id='21ec7af7-1756-4d2a-bf60-1578d94a4d45', metadata={'source': 'fake.docx'}, page_content='一、公司基本信息\\n\\n名称：宏图科技发展有限公司\\n\\n注册地址：江苏省南京市雨花台区软件大道101号\\n\\n成立日期：2011年5月16日\\n\\n法定代表人：李强\\n\\n注册资本：人民币5000万元\\n\\n员工人数：约200人\\n\\n联系电话：025-88888888\\n\\n电子邮箱：<EMAIL>'), Document(id='8e8e815d-c502-4a98-9a4c-067e2f7814b4', metadata={'source': 'fake.docx'}, page_content='一、公司基本信息\\n\\n名称：宏图科技发展有限公司\\n\\n注册地址：江苏省南京市雨花台区软件大道101号\\n\\n成立日期：2011年5月16日\\n\\n法定代表人：李强\\n\\n注册资本：人民币5000万元\\n\\n员工人数：约200人\\n\\n联系电话：025-88888888\\n\\n电子邮箱：<EMAIL>'), Document(id='1dad0849-3e19-4b6e-83bf-28eb313059cd', metadata={'source': 'fake.docx'}, page_content='报告撰写日期：2023年4月20日')]\n"]}], "source": ["#导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader,PyPDFLoader\n", "from langchain_text_splitters import  CharacterTextSplitter\n", "from langchain_chroma import Chroma\n", "\n", "from langchain.retrievers.multi_query import MultiQueryRetriever\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_deepseek import ChatDeepSeek\n", "import os\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\")+\"/v1\",\n", ")\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "#定义chatdoc\n", "#defining ChatDoc\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = [] #分割后的文本 splited text\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e: \n", "                print(f\"Error loading {file_extension} files:{e}\") \n", "        else:\n", "             print(f\"Unsupported file extension: {file_extension}\")\n", "             return  None \n", "\n", "    #处理文档的函数\n", "    #function to process the document\n", "    def splitSentences(self):\n", "        full_text = self.getFile() #获取文档内容 get the document content\n", "        if full_text != None:\n", "            #对文档进行分割\n", "            #split the document\n", "            text_split = CharacterTextSplitter(\n", "                chunk_size=150,\n", "                chunk_overlap=20,\n", "            )\n", "            texts = text_split.split_documents(full_text)\n", "            self.splitText = texts\n", "    \n", "    #向量化与向量存储\n", "    #Embed\n", "    def embeddingAndVectorDB(self):\n", "        db =Chroma.from_documents(\n", "            documents = self.splitText,\n", "            embedding = embeddings_model,\n", "        )\n", "        return db\n", "    \n", "    #提问并找到相关的文本块\n", "    #Ask and find relevant text blocks\n", "    def askAndFindFiles(self,question):\n", "        db = self.embeddingAndVectorDB()\n", "        #把问题交给LLM进行多角度的扩展\n", "        retriever_from_llm = MultiQueryRetriever.from_llm(\n", "            retriever = db.as_retriever(),\n", "            llm = llm,\n", "        )\n", "        return retriever_from_llm.invoke(question)\n", "\n", "chat_doc = ChatDoc()\n", "chat_doc.doc = \"fake.docx\"\n", "chat_doc.splitSentences()\n", "#设置下logging查看生成查询\n", "#Set the logging to see the generated queries\n", "import logging\n", "logging.basicConfig(level=logging.INFO)\n", "logging.getLogger(\"langchain.retrievers.multi_query\").setLevel(logging.DEBUG)\n", "unique_doc = chat_doc.askAndFindFiles(\"公司名称是什么?\")\n", "print(unique_doc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用上下文压缩检索降低冗余信息"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai-proxy.org/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai-proxy.org/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/chat/completions \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[Document(metadata={'source': 'fake.docx'}, page_content='名称：宏图科技发展有限公司'), Document(metadata={'source': 'fake.docx'}, page_content='名称：宏图科技发展有限公司'), Document(metadata={'source': 'fake.docx'}, page_content='名称：宏图科技发展有限公司'), Document(metadata={'source': 'fake.docx'}, page_content='名称：宏图科技发展有限公司')]\n"]}], "source": ["#导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader,PyPDFLoader\n", "from langchain_text_splitters import  CharacterTextSplitter\n", "from langchain_chroma import Chroma\n", "#引入openai和多重向量检索\n", "#Import openai and multi query retriever\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain_openai import  ChatOpenAI\n", "from langchain.retrievers import ContextualCompressionRetriever\n", "from langchain.retrievers.document_compressors import  LLMChainExtractor\n", "\n", "import os\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\")+\"/v1\",\n", ")\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "# 对比使用openai 还是有差距的\n", "embeddings_openai = OpenAIEmbeddings(\n", "    dimensions=1024,\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    model=\"text-embedding-3-small\"\n", "    )\n", "llm_openai = ChatOpenAI(\n", "    model=\"gpt-4o\",\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    )\n", "\n", "#定义chatdoc\n", "#defining ChatDoc\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = [] #分割后的文本 splited text\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e: \n", "                print(f\"Error loading {file_extension} files:{e}\") \n", "        else:\n", "             print(f\"Unsupported file extension: {file_extension}\")\n", "             return  None \n", "\n", "    #处理文档的函数\n", "    #function to process the document\n", "    def splitSentences(self):\n", "        full_text = self.getFile() #获取文档内容 get the document content\n", "        if full_text != None:\n", "            #对文档进行分割\n", "            #split the document\n", "            text_split = CharacterTextSplitter(\n", "                chunk_size=150,\n", "                chunk_overlap=20,\n", "            )\n", "            texts = text_split.split_documents(full_text)\n", "            self.splitText = texts\n", "    \n", "    #向量化与向量存储\n", "    #Embed\n", "    def embeddingAndVectorDB(self):\n", "        db =Chroma.from_documents(\n", "            documents = self.splitText,\n", "            embedding = embeddings_openai,\n", "        )\n", "        return db\n", "    \n", "    #提问并找到相关的文本块\n", "    #Ask and find relevant text blocks\n", "    def askAndFindFiles(self,question):\n", "        db = self.embeddingAndVectorDB()\n", "        retriever = db.as_retriever()\n", "        #从LLM中提取压缩器\n", "        #Extract compressor from LLM\n", "        compressor = LLMChainExtractor.from_llm(\n", "            llm = llm,\n", "        )\n", "        #创建上下文压缩检索器\n", "        #Create a contextual compression retriever\n", "        compressor_retriever = ContextualCompressionRetriever(\n", "            base_retriever = retriever,\n", "            base_compressor = compressor,\n", "        )\n", "        return compressor_retriever.invoke(input=question)\n", "\n", "chat_doc = ChatDoc()\n", "chat_doc.doc = \"fake.docx\"\n", "chat_doc.splitSentences()\n", "#设置下logging查看生成查询\n", "#Set the logging to see the generated queries\n", "import logging\n", "logging.basicConfig(level=logging.INFO)\n", "logging.getLogger(\"langchain.retrievers.multi_query\").setLevel(logging.DEBUG)\n", "unique_doc = chat_doc.askAndFindFiles(\"公司名称是什么?\")\n", "print(unique_doc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在向量存储里使用最大边际相似性（MMR）和相似性打分"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.openai-proxy.org/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.openai-proxy.org/v1/embeddings \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[Document(id='34b572a3-b09c-4fe9-a1d8-3edb4b450ef3', metadata={'source': 'fake.docx'}, page_content='一、公司基本信息\\n\\n名称：宏图科技发展有限公司\\n\\n注册地址：江苏省南京市雨花台区软件大道101号\\n\\n成立日期：2011年5月16日\\n\\n法定代表人：李强\\n\\n注册资本：人民币5000万元\\n\\n员工人数：约200人\\n\\n联系电话：025-88888888\\n\\n电子邮箱：<EMAIL>')]\n"]}], "source": ["#导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader,PyPDFLoader\n", "from langchain_text_splitters import  CharacterTextSplitter\n", "from langchain_chroma import Chroma\n", "#引入openai和多重向量检索\n", "#Import openai and multi query retriever\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain_openai import  ChatOpenAI\n", "\n", "\n", "import os\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\")+\"/v1\",\n", ")\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "# 对比使用openai 还是有差距的\n", "embeddings_openai = OpenAIEmbeddings(\n", "    dimensions=1024,\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    model=\"text-embedding-3-small\"\n", "    )\n", "llm_openai = ChatOpenAI(\n", "    model=\"gpt-4o\",\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    )\n", "\n", "#定义chatdoc\n", "#defining ChatDoc\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = [] #分割后的文本 splited text\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e: \n", "                print(f\"Error loading {file_extension} files:{e}\") \n", "        else:\n", "             print(f\"Unsupported file extension: {file_extension}\")\n", "             return  None \n", "\n", "    #处理文档的函数\n", "    #function to process the document\n", "    def splitSentences(self):\n", "        full_text = self.getFile() #获取文档内容 get the document content\n", "        if full_text != None:\n", "            #对文档进行分割\n", "            #split the document\n", "            text_split = CharacterTextSplitter(\n", "                chunk_size=150,\n", "                chunk_overlap=20,\n", "            )\n", "            texts = text_split.split_documents(full_text)\n", "            self.splitText = texts\n", "    \n", "    #向量化与向量存储\n", "    #Embed\n", "    def embeddingAndVectorDB(self):\n", "        db =Chroma.from_documents(\n", "            documents = self.splitText,\n", "            embedding = embeddings_openai,\n", "        )\n", "        return db\n", "    \n", "    #提问并找到相关的文本块\n", "    #Ask and find relevant text blocks\n", "    def askAndFindFiles(self,question):\n", "        db = self.embeddingAndVectorDB()\n", "        #retriever = db.as_retriever()\n", "        #retriever = db.as_retriever(search_type=\"mmr\")\n", "        retriever = db.as_retriever(search_type=\"similarity_score_threshold\",search_kwargs={\"score_threshold\":.2,\"k\":1})\n", "        return retriever.invoke(input=question)\n", "\n", "chat_doc = ChatDoc()\n", "chat_doc.doc = \"fake.docx\"\n", "chat_doc.splitSentences()\n", "#设置下logging查看生成查询\n", "#Set the logging to see the generated queries\n", "import logging\n", "logging.basicConfig(level=logging.INFO)\n", "logging.getLogger(\"langchain.retrievers.multi_query\").setLevel(logging.DEBUG)\n", "unique_doc = chat_doc.askAndFindFiles(\"公司名称是什么?\")\n", "print(unique_doc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["ChatDOc:和文件聊天"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/v1/embeddings \"HTTP/1.1 200 OK\"\n", "INFO:httpx:HTTP Request: POST https://api.siliconflow.cn/chat/completions \"HTTP/1.1 200 OK\"\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "公司\n", "公司注册地址\n", "公司注册地址是江苏省\n", "公司注册地址是江苏省南京市雨\n", "公司注册地址是江苏省南京市雨花台\n", "公司注册地址是江苏省南京市雨花台区软件\n", "公司注册地址是江苏省南京市雨花台区软件大道101\n", "公司注册地址是江苏省南京市雨花台区软件大道101号。\n", "公司注册地址是江苏省南京市雨花台区软件大道101号。\n"]}], "source": ["#导入必须的包\n", "# Import the required packages\n", "from langchain_community.document_loaders import UnstructuredExcelLoader,Docx2txtLoader,PyPDFLoader\n", "from langchain_text_splitters import  CharacterTextSplitter\n", "from langchain_chroma import Chroma\n", "#导入聊天所需的模块\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain_openai import  ChatOpenAI\n", "\n", "\n", "import os\n", "# 使用国产模型\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\")+\"/v1\",\n", ")\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "# 对比使用openai 还是有差距的\n", "embeddings_openai = OpenAIEmbeddings(\n", "    dimensions=1024,\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    model=\"text-embedding-3-small\"\n", "    )\n", "llm_openai = ChatOpenAI(\n", "    model=\"gpt-4o\",\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    )\n", "\n", "#定义chatdoc\n", "# Define the ChatDoc class\n", "class ChatDoc():\n", "    def __init__(self):\n", "        self.doc = None\n", "        self.splitText = [] #分割后的文本 split text\n", "        self.template = [\n", "            (\"system\",\"你是一个处理文档的秘书,你从不说自己是一个大模型或者AI助手,你会根据下面提供的上下文内容来继续回答问题.\\n 上下文内容\\n {context} \\n\"),\n", "            (\"human\",\"你好！\"),\n", "            (\"ai\",\"你好\"),\n", "            (\"human\",\"{question}\"),\n", "        ]\n", "        self.prompt = ChatPromptTemplate.from_messages(self.template)\n", "\n", "    def getFile(self):\n", "        doc = self.doc\n", "        loaders = {\n", "            \"docx\":Docx2txtLoader,\n", "            \"pdf\":<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", "            \"xlsx\":UnstructuredExcelLoader,\n", "        }\n", "        file_extension = doc.split(\".\")[-1]\n", "        loader_class = loaders.get(file_extension)\n", "        if loader_class:\n", "            try:\n", "                loader = loader_class(doc)\n", "                text = loader.load()\n", "                return text\n", "            except Exception as e: \n", "                print(f\"Error loading {file_extension} files:{e}\") \n", "        else:\n", "             print(f\"Unsupported file extension: {file_extension}\")\n", "             return  None \n", "\n", "    #处理文档的函数\n", "    # Function to process the document\n", "    def splitSentences(self):\n", "        full_text = self.getFile() #获取文档内容 get the content of the document\n", "        if full_text != None:\n", "            #对文档进行分割\n", "            # Split the document\n", "            text_split = CharacterTextSplitter(\n", "                chunk_size=150,\n", "                chunk_overlap=20,\n", "            )\n", "            texts = text_split.split_documents(full_text)\n", "            self.splitText = texts\n", "    \n", "    #向量化与向量存储\n", "    #Embedding and Vector DB\n", "    def embeddingAndVectorDB(self):\n", "        db =Chroma.from_documents(\n", "            documents = self.splitText,\n", "            embedding = embeddings_model,\n", "        )\n", "        return db\n", "    \n", "    #提问并找到相关的文本块\n", "    #Ask and find relevant text blocks\n", "    def askAndFindFiles(self,question):\n", "        db = self.embeddingAndVectorDB()\n", "        retriever = db.as_retriever(search_type=\"mmr\")\n", "        return retriever.invoke(input=question)\n", "    \n", "    #用自然语言和文档聊天\n", "    #Chat with the document using natural language\n", "    def chatWithDoc(self,question):\n", "        _content = \"\"\n", "        context = self.askAndFindFiles(question)\n", "        for i in context:\n", "            _content += i.page_content\n", "        \n", "        messages = self.prompt.format_messages(context=_content,question=question)\n", "        strOut = \"\"\n", "        for chunk in llm.stream(messages):\n", "            strOut += chunk.content\n", "            print(strOut)\n", "\n", "chat_doc = ChatDoc()\n", "chat_doc.doc = \"fake.docx\"\n", "chat_doc.splitSentences()\n", "chat_doc.chatWithDoc(\"公司注册地址是哪里？\")"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}