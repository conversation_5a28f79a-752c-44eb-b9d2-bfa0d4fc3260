{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 检索器\n", "***\n", "- 基本检索器设置\n", "- 词法搜索检索器"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 基本检索器设置\n", "***"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n"]}], "source": ["from langchain_community.document_loaders import TextLoader\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_openai import OpenAIEmbeddings\n", "from langchain_text_splitters import CharacterTextSplitter\n", "\n", "loader = TextLoader(\"test.txt\")\n", "\n", "documents = loader.load()\n", "text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=0)\n", "texts = text_splitter.split_documents(documents)\n", "print(type(texts))\n", "embeddings = OpenAIEmbeddings(\n", "        model=\"BAAI/bge-m3\",\n", "    api_key = \"sk-khjeakixzbxqaraibcdenuhfrlostygebnqkwvyrtbrxhwmu\",\n", "    base_url = \"https://api.siliconflow.cn/v1\",\n", ")\n", "vectorstore = FAISS.from_documents(texts, embeddings)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["实例化检索器"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["retriever = vectorstore.as_retriever()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["docs = retriever.invoke(\"deepseek是什么？\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(id='1e9a7e83-0fc1-4531-b465-37f659faeee6', metadata={'source': 'test.txt'}, page_content='Deepseek R1 是⽀持复杂推理、多模态处理、技术⽂档⽣成的⾼性能通⽤⼤语⾔模型。本⼿册\\n为技术团队提供完整的本地部署指南，涵盖硬件配置、国产芯⽚适配、量化⽅案、云端替代⽅\\n案及完整671B MoE模型的Ollama部署⽅法。模型 参数规\\n模\\n计算精\\n度\\n最低显存需\\n求 最低算⼒需求\\nDeepSeek-R1 (671B) 671B FP8 ≥890GB\\n2*XE9680（16*H20\\nGPU）\\nDeepSeek-R1-Distill-\\n70B\\n70B BF16 ≥180GB 4*L20 或 2*H20 GPU\\n三、国产芯⽚与硬件适配⽅案\\n1. 国内⽣态合作伙伴动态\\n企业 适配内容 性能对标（vs\\nNVIDIA）\\n华为昇\\n腾\\n昇腾910B原⽣⽀持R1全系列，提供端到端推理优化\\n⽅案 等效A100（FP16）\\n沐曦\\nGPU\\nMXN系列⽀持70B模型BF16推理，显存利⽤率提升\\n30% 等效RTX 3090\\n海光\\nDCU 适配V3/R1模型，性能对标NVIDIA A100 等效A100（BF16）\\n2. 国产硬件推荐配置\\n模型参数 推荐⽅案 适⽤场景\\n1.5B 太初T100加速卡 个⼈开发者原型验证\\n14B 昆仑芯K200集群 企业级复杂任务推理\\n32B 壁彻算⼒平台+昇腾910B集群 科研计算与多模态处理\\n12月26日，Deepseek发布了全新系列模型DeepSeek-v3，一夜之间霸榜开源模型，并在性能上和世界顶尖的闭源模型GPT-4o以及 Claude-3.5-Sonnet相提并论。\\n\\n该模型为MOE架构，大大降低了训练成本，据说训练成本仅600万美元，成本降低10倍，资源运用效率极高。有AI投资机构负责人直言，DeepSeek发布的53页的技术论文是黄金。\\n\\n那就先让我们看看论文是怎么说的吧，老规矩，先上资源地址：\\n\\nGithub: GitHub - deepseek-ai/DeepSeek-V3\\n\\n模型地址：https://huggingface.co/deepseek-ai\\n\\n论文地址：https://github.com/deepseek-ai/DeepSeek-V3/blob/main/DeepSeek_V3.pdf\\n\\n以下为技术解读：'),\n", " Document(id='b56f30dd-29c4-4e72-840f-f65264800733', metadata={'source': 'test.txt'}, page_content='DeepSeek-V3 还引入了一种辅助损失免费负载平衡策略，通过引入偏置项 b_i 并将其添加到相应的亲和度分数 s_i,t 中，来确定 top-K 路由。通过动态调整偏置项，DeepSeek-V3 能够在整个训练过程中保持平衡的专家负载，并取得比纯粹使用辅助损失的模型更好的性能。\\n\\n多 token 预测\\nDeepSeek-V3 采用了一种名为多 token 预测 (MTP) 的训练目标，该目标扩展了预测范围，以便在每个位置预测多个未来的 token。MTP 目标可以提高数据效率和模型的预测能力，并通过预先规划未来的 token 的表示来提升性能。\\n\\nMTP 实现了 D 个连续的模块来预测 D 个额外的 token，每个模块都包含一个共享嵌入层、一个共享输出头、一个 Transformer 模块和一个投影矩阵。每个 MTP 模块都使用线性投影将 token 的表示和嵌入相连接，然后通过 Transformer 模块生成输出表示，并计算额外的预测 token 的概率分布。\\n\\n基础设施：高效训练的基石\\nDeepSeek-V3 的训练过程依赖于高效的计算集群和训练框架。\\n\\n\\n计算集群\\nDeepSeek-V3 在一个配备 2048 个 NVIDIA H800 GPU 的集群上进行训练。每个节点包含 8 个 GPU，通过 NVLink 和 NVSwitch 相互连接。跨节点之间使用 InfiniBand (IB) 进行通信。\\n\\n\\n训练框架\\nDeepSeek-V3 的训练框架基于 HAI-LLM 框架，该框架为高效训练提供了强大的支持。DeepSeek-V3 应用了 16 路 Pipeline Parallelism (PP)、64 路 Expert Parallelism (EP) 和 ZeRO-1 Data Parallelism (DP)。\\n\\n双向管道并行 (DualPipe)\\n\\n为了解决跨节点专家并行导致的通信开销问题，DeepSeek-V3 设计了一种名为 DualPipe 的新型管道并行算法。DualPipe 通过重叠正向和反向计算通信阶段，不仅提高了模型训练速度，还减少了管道气泡的数量。\\n\\n跨节点全连接通信'),\n", " Document(id='2e1dc571-fb5f-43c1-bb2d-625400778162', metadata={'source': 'test.txt'}, page_content='DeepSeek-V3 开发了高效的跨节点全连接通信内核，以充分利用 IB 和 NVLink 的带宽，并节省专门用于通信的 Streaming Multiprocessors (SMs)。\\n\\n极低的内存占用\\n\\nDeepSeek-V3 通过以下技术来降低训练过程中的内存占用：\\n\\nRMSNorm 和 MLA 上投影的重新计算: 在反向传播过程中重新计算所有 RMSNorm 操作和 MLA 上投影，从而消除了永久存储其输出激活的需求。\\n\\nCPU 上的指数移动平均: 在训练过程中保存模型参数的指数移动平均 (EMA)，用于早期估计模型性能，并异步更新 EMA 参数，从而避免额外的内存和时间开销。\\n\\n多 token 预测中的共享嵌入和输出头: 利用 DualPipe 策略，将模型的最浅层和最深层部署在同一个 PP 路径上，从而实现共享嵌入和输出头的参数和梯度，进一步提高内存效率。\\n\\nFP8 训练\\nDeepSeek-V3 支持使用 FP8 数据格式进行混合精度训练，以实现加速训练和降低 GPU 内存使用。\\n\\n\\n混合精度框架\\n\\n混合精度框架使用 FP8 格式进行大多数计算密集型操作，而一些关键操作则保留其原始数据格式，以平衡训练效率和数值稳定性。\\n\\n\\n量化精度提升\\n\\n为了提高低精度训练的精度，DeepSeek-V3 引入了几种策略：\\n\\n细粒度量化: 将激活和权重分组并分别进行缩放，以更好地适应异常值。\\n\\n增加累积精度: 将部分结果复制到 FP32 寄存器中进行全精度累积，以提高精度。\\n\\n尾数超过指数: 采用 E4M3 格式，即 4 位指数和 3 位尾数，以提高精度。\\n\\n低精度存储和通信\\n\\nDeepSeek-V3 通过以下方式进一步降低内存和通信开销：\\n\\n低精度优化器状态: 使用 BF16 格式跟踪 AdamW 优化器的第一和第二矩。\\n低精度激活: 使用 FP8 格式缓存 Linear 操作的激活，并对一些关键激活使用 E5M6 格式，或重新计算其输出。\\n低精度通信: 将激活在 MoE 上投影之前量化为 FP8，并使用调度组件，与 MoE 上投影中的 FP8 Fprop 兼容。\\n预训练：迈向终极训练效率\\nDeepSeek-V3 在一个包含 14.8 万亿高质量和多样化 token 的语料库上进行预训练。预训练过程非常稳定，没有遇到不可恢复的损失峰值或需要回滚的情况。'),\n", " Document(id='04792bb0-2c44-4eeb-9949-fad24c4557ef', metadata={'source': 'test.txt'}, page_content='3.1 创建知识库\\n将需要 AI 分析处理的文档上传至知识库中。为确保 DeepSeek 模型能够准确理解文档内容，建议使用\"父子分段\"模式进行文本处理 - 这种模式能够更好地保留文档的层级结构和上下文关系。如需了解详细的配置步骤，请参考：创建知识库。\\n\\n\\n3.2 将知识库集成至 AI 应用\\n在 AI 应用的\"上下文\"内添加知识库，在对话框内输入相关问题。LLM 将首先从知识库内获取与问题相关上下文，在此基础上进行总结并给出更高质量的回答。\\n\\n\\n4. 分享 AI 应用\\n构建完成后，你可以将该 AI 应用分享给他人使用或集成至其它网站内。\\n\\n\\n阅读更多\\n除了构建简单的 AI 应用外，你还可以创建 Chatflow / Workflow 搭建更多复杂功能的应用（例如具备文件识别、图像识别、语音识别等能力）。详细说明请参考以下文档：')]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["docs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### BM25也称为Okapi BM25，是信息检索系统中用来估计文档与给定搜索查询的相关性的排名函数。\n", "****\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["! pip install --upgrade --quiet  rank_bm25"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from langchain_community.retrievers import BM25Retriever\n", "vretriever = BM25Retriever.from_texts([\"foo\", \"bar\", \"world\", \"hello\", \"foo bar\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用所创建的文档来创建一个新的检索器"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from langchain_core.documents import Document\n", "\n", "retriever = BM25Retriever.from_documents(\n", "    [\n", "        Document(page_content=\"foo\"),\n", "        Document(page_content=\"bar\"),\n", "        Document(page_content=\"world\"),\n", "        Document(page_content=\"hello\"),\n", "        Document(page_content=\"foo bar\"),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["result = retriever.invoke(\"foo\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={}, page_content='foo'),\n", " Document(metadata={}, page_content='foo bar'),\n", " Document(metadata={}, page_content='hello'),\n", " Document(metadata={}, page_content='world')]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用预处理器进行增强，在词语级别检索效果比较显著"]}, {"cell_type": "markdown", "metadata": {}, "source": ["punkt 是一个句子分割器模型"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[nltk_data] Downloading package punkt_tab to\n", "[nltk_data]     /Users/<USER>/nltk_data...\n", "[nltk_data]   Package punkt_tab is already up-to-date!\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["import nltk\n", "\n", "nltk.download(\"punkt_tab\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={}, page_content='bar'),\n", " Document(metadata={}, page_content='foo bar')]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["from nltk.tokenize import word_tokenize\n", "\n", "retriever = BM25Retriever.from_documents(\n", "    [\n", "        Document(page_content=\"foo\"),\n", "        Document(page_content=\"bar\"),\n", "        Document(page_content=\"world\"),\n", "        Document(page_content=\"hello\"),\n", "        Document(page_content=\"foo bar\"),\n", "    ],\n", "    k=2,\n", "    preprocess_func=word_tokenize,\n", ")\n", "\n", "result = retriever.invoke(\"bar\")\n", "result"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}