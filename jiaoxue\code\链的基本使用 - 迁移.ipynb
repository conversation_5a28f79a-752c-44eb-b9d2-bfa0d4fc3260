{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 从老版本的chain迁移到LCEL\n", "***\n", "- 示例1:从<PERSON><PERSON><PERSON>n迁移\n", "- 示例2:从StuffDocumentsChain迁移"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 从LLMChain迁移到LCEL"]}, {"cell_type": "markdown", "metadata": {}, "source": ["废弃的用法"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/gp/1nc10nrd3kjfxp5kkm9h93l80000gn/T/ipykernel_2820/1435414171.py:9: LangChainDeprecationWarning: The class `LLMChain` was deprecated in LangChain 0.1.17 and will be removed in 1.0. Use :meth:`~RunnableSequence, e.g., `prompt | llm`` instead.\n", "  legacy_chain = LLMChain(llm=ChatOpenAI(), prompt=prompt)\n", "/var/folders/gp/1nc10nrd3kjfxp5kkm9h93l80000gn/T/ipykernel_2820/1435414171.py:11: LangChainDeprecationWarning: The method `Chain.__call__` was deprecated in langchain 0.1.0 and will be removed in 1.0. Use :meth:`~invoke` instead.\n", "  legacy_result = legacy_chain({\"adjective\": \"funny\"})\n"]}, {"data": {"text/plain": ["{'adjective': 'funny',\n", " 'text': \"Why don't scientists trust atoms?\\n\\nBecause they make up everything!\"}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain.chains import LLMChain\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [(\"user\", \"Tell me a {adjective} joke\")],\n", ")\n", "\n", "legacy_chain = LLMChain(llm=ChatOpenAI(), prompt=prompt)\n", "\n", "legacy_result = legacy_chain({\"adjective\": \"funny\"})\n", "legacy_result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["LCEL的用法"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"Why couldn't the leopard play hide and seek?\\n\\nBecause he was always spotted!\""]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [(\"user\", \"Tell me a {adjective} joke\")],\n", ")\n", "\n", "chain = prompt | ChatOpenAI() | StrOutputParser()\n", "\n", "chain.invoke({\"adjective\": \"funny\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 从StuffDocumentsChain迁移"]}, {"cell_type": "markdown", "metadata": {}, "source": ["废弃的做法"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/gp/1nc10nrd3kjfxp5kkm9h93l80000gn/T/ipykernel_2820/3687773536.py:24: LangChainDeprecationWarning: This class is deprecated. Use the `create_stuff_documents_chain` constructor instead. See migration guide here: https://python.langchain.com/docs/versions/migrating_chains/stuff_docs_chain/\n", "  chain = StuffDocumentsChain(\n"]}], "source": ["from langchain_core.documents import Document\n", "\n", "documents = [\n", "    Document(page_content=\"Apples are red\", metadata={\"title\": \"apple_book\"}),\n", "    Document(page_content=\"Blueberries are blue\", metadata={\"title\": \"blueberry_book\"}),\n", "    Document(page_content=\"Bananas are yelow\", metadata={\"title\": \"banana_book\"}),\n", "]\n", "\n", "from langchain.chains import LLMChain, StuffDocumentsChain\n", "from langchain_core.prompts import ChatPromptTemplate, PromptTemplate\n", "\n", "# This controls how each document will be formatted. Specifically,\n", "# it will be passed to `format_document` - see that function for more\n", "# details.\n", "document_prompt = PromptTemplate(\n", "    input_variables=[\"page_content\"], template=\"{page_content}\"\n", ")\n", "document_variable_name = \"context\"\n", "# The prompt here should take as an input variable the\n", "# `document_variable_name`\n", "prompt = ChatPromptTemplate.from_template(\"Summarize this content: {context}\")\n", "llm=ChatOpenAI()\n", "llm_chain = LLMChain(llm=llm, prompt=prompt)\n", "chain = StuffDocumentsChain(\n", "    llm_chain=llm_chain,\n", "    document_prompt=document_prompt,\n", "    document_variable_name=document_variable_name,\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["'The content describes the colors of different fruits: apples are red, blueberries are blue, and bananas are yellow.'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result = chain.invoke(documents)\n", "result[\"output_text\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["LCEL的做法"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["'This content describes the colors of different fruits: apples are red, blueberries are blue, and bananas are yellow.'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.documents import Document\n", "\n", "documents = [\n", "    Document(page_content=\"Apples are red\", metadata={\"title\": \"apple_book\"}),\n", "    Document(page_content=\"Blueberries are blue\", metadata={\"title\": \"blueberry_book\"}),\n", "    Document(page_content=\"Bananas are yelow\", metadata={\"title\": \"banana_book\"}),\n", "]\n", "\n", "from langchain.chains.combine_documents import create_stuff_documents_chain\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "prompt = ChatPromptTemplate.from_template(\"Summarize this content: {context}\")\n", "chain = create_stuff_documents_chain(llm, prompt)\n", "\n", "result = chain.invoke({\"context\": documents})\n", "result"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}