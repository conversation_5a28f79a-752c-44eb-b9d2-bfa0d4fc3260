{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Langgraph - 节点与可控制性\n", "*****\n", "- 第一个Langgraph\n", "- 基本控制：串行控制\n", "- 基本控制：分支控制\n", "- 基本控制：条件分支与循环\n", "- 基本控制：图的可视化\n", "- 精细控制：图的运行时配置\n", "- 精细控制：map-reduce"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 第一个Langgraph\n", "*****"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Defaulting to user installation because normal site-packages is not writeable\n", "Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting langgraph\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/6e/90/e37e2bc19bee81f859bfd61526d977f54ec5d8e036fa091f2cfe4f19560b/langgraph-0.6.1-py3-none-any.whl (151 kB)\n", "Requirement already satisfied: langchain-core>=0.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langgraph) (0.3.70)\n", "Requirement already satisfied: langgraph-checkpoint<3.0.0,>=2.1.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langgraph) (2.1.1)\n", "Collecting langgraph-prebuilt<0.7.0,>=0.6.0 (from langgraph)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a6/df/cb4d73e99719b7ca0d42503d39dec49c67225779c6a1e689954a5604dbe6/langgraph_prebuilt-0.6.1-py3-none-any.whl (28 kB)\n", "Collecting langgraph-sdk<0.3.0,>=0.2.0 (from langgraph)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a5/03/a8ab0e8ea74be6058cb48bb1d85485b5c65d6ea183e3ee1aa8ca1ac73b3e/langgraph_sdk-0.2.0-py3-none-any.whl (50 kB)\n", "Requirement already satisfied: pydantic>=2.7.4 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langgraph) (2.11.7)\n", "Requirement already satisfied: xxhash>=3.5.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langgraph) (3.5.0)\n", "Requirement already satisfied: ormsgpack>=1.10.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langgraph-checkpoint<3.0.0,>=2.1.0->langgraph) (1.10.0)\n", "Requirement already satisfied: httpx>=0.25.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langgraph-sdk<0.3.0,>=0.2.0->langgraph) (0.28.1)\n", "Requirement already satisfied: orjson>=3.10.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langgraph-sdk<0.3.0,>=0.2.0->langgraph) (3.10.18)\n", "Requirement already satisfied: anyio in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from httpx>=0.25.2->langgraph-sdk<0.3.0,>=0.2.0->langgraph) (4.9.0)\n", "Requirement already satisfied: certifi in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from httpx>=0.25.2->langgraph-sdk<0.3.0,>=0.2.0->langgraph) (2025.7.14)\n", "Requirement already satisfied: httpcore==1.* in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from httpx>=0.25.2->langgraph-sdk<0.3.0,>=0.2.0->langgraph) (1.0.9)\n", "Requirement already satisfied: idna in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from httpx>=0.25.2->langgraph-sdk<0.3.0,>=0.2.0->langgraph) (3.10)\n", "Requirement already satisfied: h11>=0.16 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from httpcore==1.*->httpx>=0.25.2->langgraph-sdk<0.3.0,>=0.2.0->langgraph) (0.16.0)\n", "Requirement already satisfied: langsmith>=0.3.45 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langchain-core>=0.1->langgraph) (0.4.8)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langchain-core>=0.1->langgraph) (8.5.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langchain-core>=0.1->langgraph) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langchain-core>=0.1->langgraph) (6.0.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langchain-core>=0.1->langgraph) (4.14.1)\n", "Requirement already satisfied: packaging>=23.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langchain-core>=0.1->langgraph) (25.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from jsonpatch<2.0,>=1.33->langchain-core>=0.1->langgraph) (3.0.0)\n", "Requirement already satisfied: requests<3,>=2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langsmith>=0.3.45->langchain-core>=0.1->langgraph) (2.32.4)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langsmith>=0.3.45->langchain-core>=0.1->langgraph) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from langsmith>=0.3.45->langchain-core>=0.1->langgraph) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic>=2.7.4->langgraph) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic>=2.7.4->langgraph) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from pydantic>=2.7.4->langgraph) (0.4.1)\n", "Requirement already satisfied: charset_normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from requests<3,>=2->langsmith>=0.3.45->langchain-core>=0.1->langgraph) (3.4.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from requests<3,>=2->langsmith>=0.3.45->langchain-core>=0.1->langgraph) (2.5.0)\n", "Requirement already satisfied: sniffio>=1.1 in c:\\users\\<USER>\\appdata\\roaming\\python\\python313\\site-packages (from anyio->httpx>=0.25.2->langgraph-sdk<0.3.0,>=0.2.0->langgraph) (1.3.1)\n", "Installing collected packages: langgraph-sdk, langgraph-prebuilt, langgraph\n", "\n", "   -------------------------- ------------- 2/3 [langgraph]\n", "   -------------------------- ------------- 2/3 [langgraph]\n", "   ---------------------------------------- 3/3 [langgraph]\n", "\n", "Successfully installed langgraph-0.6.1 langgraph-prebuilt-0.6.1 langgraph-sdk-0.2.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["ERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "open-deep-research 0.0.16 requires langchain-deepseek>=0.1.2, which is not installed.\n"]}], "source": ["! pip install -U langgraph"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义state\n", "****\n", "- TypedDict:属于 Python 标准库 typing 模块的一部分,仅提供静态类型检查，运行时不执行验证\n", "- Pydantic:第三方库，需要单独安装,提供运行时数据验证和序列化功能"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import AnyMessage\n", "from typing_extensions import TypedDict\n", "\n", "# 节点间通讯的消息类型\n", "class State(TypedDict):\n", "    messages: list[AnyMessage]\n", "    extra_field: int"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义节点\n", "***\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import AIMessage\n", "\n", "\n", "def node(state: State):\n", "    messages = state[\"messages\"]\n", "    new_message = AIMessage(\"你好!\")\n", "\n", "    return {\"messages\": messages + [new_message], \"extra_field\": 10}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建图\n", "****\n", "- 包含一个节点\n", "- 使用state通信"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph\n", "\n", "graph_builder = StateGraph(State)\n", "graph_builder.add_node(node)\n", "graph_builder.set_entry_point(\"node\")\n", "graph = graph_builder.compile()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["查看节点与图结构（内置）\n", "****\n", "Mermaid 是一种基于文本的图表和可视化工具，它允许用户通过简单的文本语法来创建复杂的图表和流程图。它特别适合开发者、文档编写者和技术人员在文档、代码库或网页中嵌入可视化内容。"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用langgraph图结构\n", "***"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='Hi', additional_kwargs={}, response_metadata={}),\n", "  AIMessage(content='你好!', additional_kwargs={}, response_metadata={})],\n", " 'extra_field': 10}"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "result = graph.invoke({\"messages\": [HumanMessage(\"Hi\")]})\n", "result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用 pretty_print 来格式化显示\n", "***"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Hi\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "你好!\n"]}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "result = graph.invoke({\"messages\": [HumanMessage(\"Hi\")]})\n", "for message in result[\"messages\"]:\n", "    message.pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 串行控制\n", "****"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAGoAAAGwCAIAAADOkWc9AAAAAXNSR0IArs4c6QAAHe9JREFUeJztnXtcE1e+wE8yeYdAEgivEB4BBQWfoFStD9Suuj7BYsXW126rtd32drt2t/e2Xq1d71q9rfatdVt1fdaqtYrV6lqrqCBSQYuiAoII4RXI+zlJ5v4RL6U0CRMOQ0J6vp/+gTNnkt98e2bm5Mw550cjCAIgegrd1wH0b5A+KJA+KJA+KJ<PERSON>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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from typing_extensions import TypedDict\n", "from IPython.display import Image, display\n", "from langgraph.graph import START, StateGraph\n", "\n", "class State(TypedDict):\n", "    value_1: str\n", "    value_2: int\n", "\n", "def step_1(state: State):\n", "    return {\"value_1\": \"a\"}\n", "\n", "def step_2(state: State):\n", "    current_value_1 = state[\"value_1\"]\n", "    return {\"value_1\": f\"{current_value_1} b\"}\n", "\n", "def step_3(state: State):\n", "    return {\"value_2\": 10}\n", "\n", "\n", "graph_builder = StateGraph(State)\n", "\n", "# Add nodes\n", "graph_builder.add_node(step_1)\n", "graph_builder.add_node(step_2)\n", "graph_builder.add_node(step_3)\n", "\n", "# Add edges\n", "graph_builder.add_edge(START, \"step_1\")\n", "graph_builder.add_edge(\"step_1\", \"step_2\")\n", "graph_builder.add_edge(\"step_2\", \"step_3\")\n", "\n", "graph = graph_builder.compile()\n", "\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'value_1': 'a b', 'value_2': 10}"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"value_1\": \"c\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 分支控制\n", "****\n", "![](langgraph1.png)\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import operator\n", "from typing import Annotated, Any\n", "\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.graph import StateGraph, START, END\n", "from IPython.display import Image, display\n", "\n", "# Annotated允许为类型提示添加额外的元数据，而不影响类型检查器对类型本身的理解\n", "class State(TypedDict):\n", "    aggregate: Annotated[list, operator.add]\n", "\n", "\n", "def a(state: State):\n", "    print(f'Adding \"A\" to {state[\"aggregate\"]}')\n", "    return {\"aggregate\": [\"A\"]}\n", "\n", "\n", "def b(state: State):\n", "    print(f'Adding \"B\" to {state[\"aggregate\"]}')\n", "    return {\"aggregate\": [\"B\"]}\n", "\n", "\n", "def c(state: State):\n", "    print(f'Adding \"C\" to {state[\"aggregate\"]}')\n", "    return {\"aggregate\": [\"C\"]}\n", "\n", "\n", "def d(state: State):\n", "    print(f'Adding \"D\" to {state[\"aggregate\"]}')\n", "    return {\"aggregate\": [\"D\"]}\n", "\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(a)\n", "builder.add_node(b)\n", "builder.add_node(c)\n", "builder.add_node(d)\n", "builder.add_edge(START, \"a\")\n", "builder.add_edge(\"a\", \"b\")\n", "builder.add_edge(\"a\", \"c\")\n", "builder.add_edge(\"b\", \"d\")\n", "builder.add_edge(\"c\", \"d\")\n", "builder.add_edge(\"d\", END)\n", "graph = builder.compile()\n", "\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Adding \"A\" to []\n", "Adding \"B\" to ['A']\n", "Adding \"C\" to ['A']\n", "Adding \"D\" to ['A', 'B', 'C']\n"]}, {"data": {"text/plain": ["{'aggregate': ['A', 'B', 'C', 'D']}"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"aggregate\": []}, {\"configurable\": {\"thread_id\": \"foo\"}})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 条件分支与循环\n", "***\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["import operator\n", "from typing import Annotated, Literal\n", "\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.graph import StateGraph, START, END\n", "\n", "\n", "class State(TypedDict):\n", "    aggregate: Annotated[list, operator.add]\n", "\n", "\n", "def a(state: State):\n", "    print(f'Node A sees {state[\"aggregate\"]}')\n", "    return {\"aggregate\": [\"A\"]}\n", "\n", "\n", "def b(state: State):\n", "    print(f'Node B sees {state[\"aggregate\"]}')\n", "    return {\"aggregate\": [\"B\"]}\n", "\n", "\n", "# Define nodes\n", "builder = StateGraph(State)\n", "builder.add_node(a)\n", "builder.add_node(b)\n", "\n", "\n", "# Define edges\n", "def route(state: State) -> Literal[\"b\", END]:\n", "    if len(state[\"aggregate\"]) < 7:\n", "        return \"b\"\n", "    else:\n", "        return END\n", "\n", "\n", "builder.add_edge(START, \"a\")\n", "builder.add_conditional_edges(\"a\", route)\n", "builder.add_edge(\"b\", \"a\")\n", "graph = builder.compile()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Node A sees []\n", "Node B sees ['A']\n", "Node A sees ['A', 'B']\n", "Node B sees ['A', 'B', 'A']\n", "Node A sees ['A', 'B', 'A', 'B']\n", "Node B sees ['A', 'B', 'A', 'B', 'A']\n", "Node A sees ['A', 'B', 'A', 'B', 'A', 'B']\n"]}, {"data": {"text/plain": ["{'aggregate': ['A', 'B', 'A', 'B', 'A', 'B', 'A']}"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"aggregate\": []})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用递归限制recursion_limit，防止异常情况下的大量无用调用"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Node A sees []\n", "Node B sees ['A']\n", "Node A sees ['A', 'B']\n", "Node B sees ['A', 'B', 'A']\n", "Recursion Error\n"]}], "source": ["from langgraph.errors import GraphRecursionError\n", "\n", "try:\n", "    graph.invoke({\"aggregate\": []}, {\"recursion_limit\": 4})\n", "except GraphRecursionError:\n", "    print(\"Recursion Error\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["循环"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import operator\n", "from typing import Annotated, Literal\n", "\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.graph import StateGraph, START, END\n", "from IPython.display import Image, display\n", "\n", "class State(TypedDict):\n", "    aggregate: Annotated[list, operator.add]\n", "\n", "\n", "def a(state: State):\n", "    print(f'Node A sees {state[\"aggregate\"]}')\n", "    return {\"aggregate\": [\"A\"]}\n", "\n", "\n", "def b(state: State):\n", "    print(f'Node B sees {state[\"aggregate\"]}')\n", "    return {\"aggregate\": [\"B\"]}\n", "\n", "\n", "def c(state: State):\n", "    print(f'Node C sees {state[\"aggregate\"]}')\n", "    return {\"aggregate\": [\"C\"]}\n", "\n", "\n", "def d(state: State):\n", "    print(f'<PERSON><PERSON> <PERSON> sees {state[\"aggregate\"]}')\n", "    return {\"aggregate\": [\"D\"]}\n", "\n", "\n", "# 节点\n", "builder = StateGraph(State)\n", "builder.add_node(a)\n", "builder.add_node(b)\n", "builder.add_node(c)\n", "builder.add_node(d)\n", "\n", "\n", "# 边\n", "def route(state: State) -> Literal[\"b\", END]:\n", "    if len(state[\"aggregate\"]) < 7:\n", "        return \"b\"\n", "    else:\n", "        return END\n", "\n", "\n", "builder.add_edge(START, \"a\")\n", "builder.add_conditional_edges(\"a\", route)\n", "builder.add_edge(\"b\", \"c\")\n", "builder.add_edge(\"b\", \"d\")\n", "builder.add_edge([\"c\", \"d\"], \"a\")\n", "graph = builder.compile()\n", "\n", "\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1. 节点A\n", "2. 节点B\n", "3. 节点C或节点D\n", "4. 节点A"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Node A sees []\n", "Node B sees ['A']\n", "Node C sees ['A', 'B']\n", "<PERSON><PERSON> D sees ['A', 'B']\n", "Node A sees ['A', 'B', 'C', 'D']\n", "Node B sees ['A', 'B', 'C', 'D', 'A']\n", "Node C sees ['A', 'B', 'C', 'D', 'A', 'B']\n", "<PERSON><PERSON> D sees ['A', 'B', 'C', 'D', 'A', 'B']\n", "Node A sees ['A', 'B', 'C', 'D', 'A', 'B', 'C', 'D']\n"]}], "source": ["result = graph.invoke({\"aggregate\": []})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 图的运行时配置\n", "****"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import operator\n", "from typing import Annotated, Sequence\n", "from typing_extensions import TypedDict\n", "\n", "from langchain_deepseek import ChatDeepSeek\n", "from langchain_openai import ChatOpenAI\n", "import os\n", "from langchain_core.messages import BaseMessage, HumanMessage\n", "from langchain_core.runnables.config import RunnableConfig\n", "from langgraph.graph import END, StateGraph, START\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "\n", "\n", "\n", "# model = ChatDeepSeek(\n", "#     model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "#     temperature=0,\n", "#     api_key=api_key,\n", "#     base_url=base_url,\n", "# )\n", "\n", "model = ChatDeepSeek(\n", "    model=\"deepseek-chat\",\n", "    temperature=0,\n", "    max_tokens=None,\n", "    timeout=None,\n", "    max_retries=2,\n", "    api_key=os.getenv(\"DEEPSEEK_API_KEY\"),\n", "    # other params...\n", ")\n", "\n", "\n", "model1 = ChatOpenAI(\n", "    model=\"gpt-3.5-turbo\",\n", "    temperature=0,\n", "    api_key=os.getenv(\"OPENAI_API_KEY\"),\n", "    base_url=os.getenv(\"OPENAI_API_BASE\"),)\n", "# 定义要切换的模型\n", "models = {\n", "    \"deepseek\": model,\n", "    \"openai\": model1,\n", "}\n", "\n", "\n", "class AgentState(TypedDict):\n", "    messages: Annotated[Sequence[BaseMessage], operator.add]\n", "\n", "\n", "def _call_model(state: AgentState, config: RunnableConfig):\n", "    # 使用LCEL的配置\n", "    model_name = config[\"configurable\"].get(\"model\", \"deepseek\")\n", "    model = models[model_name]\n", "    response = model.invoke(state[\"messages\"])\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# Define a new graph\n", "builder = StateGraph(AgentState)\n", "builder.add_node(\"model\", _call_model)\n", "builder.add_edge(START, \"model\")\n", "builder.add_edge(\"model\", END)\n", "\n", "graph = builder.compile()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["没有增加运行时配置的情况下，它会默认调用deepseek"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='hi 你是谁？', additional_kwargs={}, response_metadata={}),\n", "  AIMessage(content='你好呀！😊我是DeepSeek Chat，由深度求索公司（DeepSeek）创造的智能AI助手。我可以帮你解答问题、提供建议、陪你聊天，还能处理各种文本、文档相关任务！有什么我可以帮你的吗？✨', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 53, 'prompt_tokens': 7, 'total_tokens': 60, 'completion_tokens_details': None, 'prompt_tokens_details': {'audio_tokens': None, 'cached_tokens': 0}, 'prompt_cache_hit_tokens': 0, 'prompt_cache_miss_tokens': 7}, 'model_name': 'deepseek-chat', 'system_fingerprint': 'fp_8802369eaa_prod0623_fp8_kvcache', 'id': '7c1e79ae-41dd-4f2b-89a9-02f0e284e40f', 'service_tier': None, 'finish_reason': 'stop', 'logprobs': None}, id='run--46a5c630-25dc-4169-b466-339b55238304-0', usage_metadata={'input_tokens': 7, 'output_tokens': 53, 'total_tokens': 60, 'input_token_details': {'cache_read': 0}, 'output_token_details': {}})]}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["graph.invoke({\"messages\": [HumanMessage(content=\"hi 你是谁？\")]})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["增加运行时配置，动态切换模型"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='hi 你是谁？', additional_kwargs={}, response_metadata={}),\n", "  AIMessage(content='你好，我是一个人工智能助手。我可以回答你的问题和提供帮助。有什么可以帮到你的吗？', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 44, 'prompt_tokens': 14, 'total_tokens': 58, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-3.5-turbo-0125', 'system_fingerprint': 'fp_0165350fbb', 'finish_reason': 'stop', 'logprobs': None}, id='run-7cfaec6c-cd0d-45d3-b565-bc433f4c5765-0', usage_metadata={'input_tokens': 14, 'output_tokens': 44, 'total_tokens': 58, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["config = {\"configurable\": {\"model\": \"openai\"}}\n", "graph.invoke({\"messages\": [HumanMessage(content=\"hi 你是谁？\")]}, config=config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### MapReduce并行执行\n", "****\n", "给定一个来自用户的一般主题，生成相关主题列表，为每个主题生成一个笑话，并从结果列表中选择最佳笑话。\n", "![](langgraph2.png)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import operator\n", "from typing import Annotated\n", "from typing_extensions import TypedDict\n", "\n", "from langchain_deepseek import ChatDeepSeek\n", "\n", "from langgraph.types import Send\n", "from langgraph.graph import END, StateGraph, START\n", "\n", "from pydantic import BaseModel, Field\n", "\n", "# 模型和提示词\n", "# 定义我们将使用的模型和提示词\n", "subjects_prompt = \"\"\"生成一个逗号分隔的列表，包含2到5个与以下主题相关的例子：{topic}。\"\"\"\n", "joke_prompt = \"\"\"生成一个关于{subject}的笑话\"\"\"\n", "best_joke_prompt = \"\"\"以下是一些关于{topic}的笑话。选出最好的一个！返回最佳笑话的ID。\n", "\n", "{jokes}\"\"\"\n", "\n", "\n", "class Subjects(BaseModel):\n", "    subjects: list[str]\n", "\n", "\n", "class Joke(BaseModel):\n", "    joke: str\n", "\n", "\n", "class BestJoke(BaseModel):\n", "    id: int = Field(description=\"最佳笑话的索引，从0开始\", ge=0)\n", "\n", "\n", "# model = ChatDeepSeek(\n", "#     model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "#     temperature=0,\n", "#     api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "#     base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", "# )\n", "\n", "model = ChatDeepSeek(\n", "    model=\"deepseek-chat\",\n", "    temperature=0,\n", "    max_tokens=None,\n", "    timeout=None,\n", "    max_retries=2,\n", "    api_key=os.getenv(\"DEEPSEEK_API_KEY\"),\n", "    # other params...\n", ")\n", "\n", "# 图组件：定义构成图的组件\n", "\n", "\n", "# 这将是主图的整体状态。\n", "# 它将包含一个主题（我们期望用户提供）\n", "# 然后将生成一个主题列表，并为每个主题生成一个笑话\n", "class OverallState(TypedDict):\n", "    topic: str\n", "    subjects: list\n", "    # 注意这里我们使用operator.add\n", "    # 这是因为我们想把从各个节点生成的所有笑话\n", "    # 合并回一个列表 - 这本质上是\"归约\"部分\n", "    jokes: Annotated[list, operator.add]\n", "    best_selected_joke: str\n", "\n", "\n", "# 这将是我们将\"映射\"所有主题的节点的状态\n", "# 用于生成笑话\n", "class JokeState(TypedDict):\n", "    subject: str\n", "\n", "\n", "# 这是我们用来生成笑话主题的函数\n", "def generate_topics(state: OverallState):\n", "    prompt = subjects_prompt.format(topic=state[\"topic\"])\n", "    response = model.with_structured_output(Subjects).invoke(prompt)\n", "    return {\"subjects\": response.subjects}\n", "\n", "\n", "# 这里我们根据给定的主题生成笑话\n", "def generate_joke(state: JokeState):\n", "    prompt = joke_prompt.format(subject=state[\"subject\"])\n", "    response = model.with_structured_output(Joke).invoke(prompt)\n", "    return {\"jokes\": [response.joke]}\n", "\n", "\n", "# 这里我们定义映射到生成的主题上的逻辑\n", "# 我们将在图中使用这个作为边缘\n", "def continue_to_jokes(state: OverallState):\n", "    # 我们将返回一个`Send`对象列表\n", "    # 每个`Send`对象包含图中节点的名称\n", "    # 以及要发送到该节点的状态\n", "    return [Send(\"generate_joke\", {\"subject\": s}) for s in state[\"subjects\"]]\n", "\n", "\n", "# 这里我们将评判最佳笑话\n", "def best_joke(state: OverallState):\n", "    jokes = \"\\n\\n\".join(state[\"jokes\"])\n", "    prompt = best_joke_prompt.format(topic=state[\"topic\"], jokes=jokes)\n", "    response = model.with_structured_output(BestJoke).invoke(prompt)\n", "    return {\"best_selected_joke\": state[\"jokes\"][response.id]}\n", "\n", "\n", "# 构建图：这里我们将所有内容组合在一起构建我们的图\n", "graph = StateGraph(OverallState)\n", "graph.add_node(\"generate_topics\", generate_topics)\n", "graph.add_node(\"generate_joke\", generate_joke)\n", "graph.add_node(\"best_joke\", best_joke)\n", "graph.add_edge(START, \"generate_topics\")\n", "graph.add_conditional_edges(\"generate_topics\", continue_to_jokes, [\"generate_joke\"])\n", "graph.add_edge(\"generate_joke\", \"best_joke\")\n", "graph.add_edge(\"best_joke\", END)\n", "app = graph.compile()\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["from IPython.display import Image\n", "\n", "Image(app.get_graph().draw_mermaid_png())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'generate_topics': {'subjects': ['动物']}}\n", "{'generate_joke': {'jokes': ['动物']}}\n", "{'best_joke': {'best_selected_joke': '动物'}}\n"]}], "source": ["for s in app.stream({\"topic\": \"动物\"}):\n", "    print(s)"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}