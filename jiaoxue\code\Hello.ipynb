{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 本地ipynb代码演示环境\n", "************\n", "- 需要python 3.12.1\n", "- 需要隔离虚拟环境venv\n", "- 需要安装jupyter\n", "- 需要本地配置.env"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 安装依赖包\n", "**********"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: python-dotenv in ./.venv/lib/python3.13/site-packages (1.0.1)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install python-dotenv"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting langchain\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/18/7d/0f4cc3317634195381f87c5d90268f29b9a31fda62aa7a7f36a1c27b06f3/langchain-0.3.19-py3-none-any.whl (1.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m15.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting langchain-core<1.0.0,>=0.3.35 (from langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/6a/f5/9ce2a94bc49b64c0bf53b17524d5fc5c926070e911b11d489979d47d5491/langchain_core-0.3.37-py3-none-any.whl (413 kB)\n", "Collecting langchain-text-splitters<1.0.0,>=0.3.6 (from langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/4c/f8/6b82af988e65af9697f6a2f25373fb173fd32d48b62772a8773c5184c870/langchain_text_splitters-0.3.6-py3-none-any.whl (31 kB)\n", "Collecting langsmith<0.4,>=0.1.17 (from langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/27/2c/8e48e5c264022b03555fa3c5367c58f424d50f6a562a7efb20a178e1d9e9/langsmith-0.3.10-py3-none-any.whl (333 kB)\n", "Collecting pydantic<3.0.0,>=2.7.4 (from langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/f4/3c/8cc1cc84deffa6e25d2d0c688ebb80635dfdbf1dbea3e30c541c8cf4d860/pydantic-2.10.6-py3-none-any.whl (431 kB)\n", "Collecting SQLAlchemy<3,>=1.4 (from langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/f4/ec/94bb036ec78bf9a20f8010c807105da9152dd84f72e8c51681ad2f30b3fd/SQLAlchemy-2.0.38-cp313-cp313-macosx_11_0_arm64.whl (2.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.1/2.1 MB\u001b[0m \u001b[31m13.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting requests<3,>=2 (from langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/f9/9b/335f9764261e915ed497fcdeb11df5dfd6f7bf257d4a6a2a686d80da4d54/requests-2.32.3-py3-none-any.whl (64 kB)\n", "Collecting PyYAML>=5.3 (from langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/45/9f/3b1c20a0b7a3200524eb0076cc027a970d320bd3a6592873c85c92a08731/PyYAML-6.0.2-cp313-cp313-macosx_11_0_arm64.whl (171 kB)\n", "Collecting aiohttp<4.0.0,>=3.8.3 (from langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/8e/f9/de568f8a8ca6b061d157c50272620c53168d6e3eeddae78dbb0f7db981eb/aiohttp-3.11.12-cp313-cp313-macosx_11_0_arm64.whl (453 kB)\n", "Collecting tenacity!=8.4.0,<10,>=8.1.0 (from langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/b6/cb/b86984bed139586d01532a587464b5805f12e397594f19f931c4c2fbfa61/tenacity-9.0.0-py3-none-any.whl (28 kB)\n", "Collecting numpy<3,>=1.26.2 (from langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/20/60/70af0acc86495b25b672d403e12cb25448d79a2b9658f4fc45e845c397a8/numpy-2.2.3-cp313-cp313-macosx_14_0_arm64.whl (5.1 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m5.1/5.1 MB\u001b[0m \u001b[31m13.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hCollecting aiohappyeyeballs>=2.3.0 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/44/4c/03fb05f56551828ec67ceb3665e5dc51638042d204983a03b0a1541475b6/aiohappyeyeballs-2.4.6-py3-none-any.whl (14 kB)\n", "Collecting aiosignal>=1.1.2 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/ec/6a/bc7e17a3e87a2985d3e8f4da4cd0f481060eb78fb08596c42be62c90a4d9/aiosignal-1.3.2-py2.py3-none-any.whl (7.6 kB)\n", "Collecting attrs>=17.3.0 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/fc/30/d4986a882011f9df997a55e6becd864812ccfcd821d64aac8570ee39f719/attrs-25.1.0-py3-none-any.whl (63 kB)\n", "Collecting frozenlist>=1.1.1 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/3a/c8/76f23bf9ab15d5f760eb48701909645f686f9c64fbb8982674c241fbef14/frozenlist-1.5.0-cp313-cp313-macosx_11_0_arm64.whl (50 kB)\n", "Collecting multidict<7.0,>=4.5 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/67/5e/04575fd837e0958e324ca035b339cea174554f6f641d3fb2b4f2e7ff44a2/multidict-6.1.0-cp313-cp313-macosx_11_0_arm64.whl (29 kB)\n", "Collecting propcache>=0.2.0 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/01/9b/fd5ddbee66cf7686e73c516227c2fd9bf471dbfed0f48329d095ea1228d3/propcache-0.3.0-cp313-cp313-macosx_11_0_arm64.whl (44 kB)\n", "Collecting yarl<2.0,>=1.17.0 (from aiohttp<4.0.0,>=3.8.3->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/c6/fc/d68d8f83714b221a85ce7866832cba36d7c04a68fa6a960b908c2c84f325/yarl-1.18.3-cp313-cp313-macosx_11_0_arm64.whl (91 kB)\n", "Collecting jsonpatch<2.0,>=1.33 (from langchain-core<1.0.0,>=0.3.35->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/73/07/02e16ed01e04a374e644b575638ec7987ae846d25ad97bcc9945a3ee4b0e/jsonpatch-1.33-py2.py3-none-any.whl (12 kB)\n", "Requirement already satisfied: packaging<25,>=23.2 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain) (24.2)\n", "Collecting typing-extensions>=4.7 (from langchain-core<1.0.0,>=0.3.35->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/26/9f/ad63fc0248c5379346306f8668cda6e2e2e9c95e01216d2b8ffd9ff037d0/typing_extensions-4.12.2-py3-none-any.whl (37 kB)\n", "Collecting httpx<1,>=0.23.0 (from langsmith<0.4,>=0.1.17->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl (73 kB)\n", "Collecting <PERSON><PERSON><PERSON><4.0.0,>=3.9.14 (from langsmith<0.4,>=0.1.17->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/06/10/fe7d60b8da538e8d3d3721f08c1b7bff0491e8fa4dd3bf11a17e34f4730e/orjson-3.10.15-cp313-cp313-macosx_10_15_x86_64.macosx_11_0_arm64.macosx_10_15_universal2.whl (249 kB)\n", "Collecting requests-toolbelt<2.0.0,>=1.0.0 (from langsmith<0.4,>=0.1.17->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/3f/51/d4db610ef29373b879047326cbf6fa98b6c1969d6f6dc423279de2b1be2c/requests_toolbelt-1.0.0-py2.py3-none-any.whl (54 kB)\n", "Collecting zstandard<0.24.0,>=0.23.0 (from langsmith<0.4,>=0.1.17->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/16/e8/cbf01077550b3e5dc86089035ff8f6fbbb312bc0983757c2d1117ebba242/zstandard-0.23.0-cp313-cp313-macosx_11_0_arm64.whl (633 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m633.4/633.4 kB\u001b[0m \u001b[31m15.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting annotated-types>=0.6.0 (from pydantic<3.0.0,>=2.7.4->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl (13 kB)\n", "Collecting pydantic-core==2.27.2 (from pydantic<3.0.0,>=2.7.4->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/10/6c/e62b8657b834f3eb2961b49ec8e301eb99946245e70bf42c8817350cbefc/pydantic_core-2.27.2-cp313-cp313-macosx_11_0_arm64.whl (1.8 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.8/1.8 MB\u001b[0m \u001b[31m17.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hCollecting charset-normalizer<4,>=2 (from requests<3,>=2->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/38/94/ce8e6f63d18049672c76d07d119304e1e2d7c6098f0841b51c666e9f44a0/charset_normalizer-3.4.1-cp313-cp313-macosx_10_13_universal2.whl (195 kB)\n", "Collecting idna<4,>=2.5 (from requests<3,>=2->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl (70 kB)\n", "Collecting urllib3<3,>=1.21.1 (from requests<3,>=2->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/c8/19/4ec628951a74043532ca2cf5d97b7b14863931476d117c471e8e2b1eb39f/urllib3-2.3.0-py3-none-any.whl (128 kB)\n", "Collecting certifi>=2017.4.17 (from requests<3,>=2->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/38/fc/bce832fd4fd99766c04d1ee0eead6b0ec6486fb100ae5e74c1d91292b982/certifi-2025.1.31-py3-none-any.whl (166 kB)\n", "Collecting anyio (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/46/eb/e7f063ad1fec6b3178a3cd82d1a3c4de82cccf283fc42746168188e1cdd5/anyio-4.8.0-py3-none-any.whl (96 kB)\n", "Collecting httpcore==1.* (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/87/f5/72347bc88306acb359581ac4d52f23c0ef445b57157adedb9aee0cd689d2/httpcore-1.0.7-py3-none-any.whl (78 kB)\n", "Collecting h11<0.15,>=0.13 (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/95/04/ff642e65ad6b90db43e668d70ffb6736436c7ce41fcc549f4e9472234127/h11-0.14.0-py3-none-any.whl (58 kB)\n", "Collecting jsonpointer>=1.9 (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.35->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/71/92/5e77f98553e9e75130c78900d000368476aed74276eb8ae8796f65f00918/jsonpointer-3.0.0-py2.py3-none-any.whl (7.6 kB)\n", "Collecting sniffio>=1.1 (from anyio->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.17->langchain)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl (10 kB)\n", "Installing collected packages: zstandard, urllib3, typing-extensions, tenacity, sniffio, PyYAML, propcache, orjson, numpy, multidict, jsonpointer, idna, h11, frozenlist, charset-normalizer, certifi, attrs, annotated-types, aiohappyeyeballs, yarl, SQLAlchemy, requests, pydantic-core, jsonpatch, httpcore, anyio, aiosignal, requests-toolbelt, pydantic, httpx, aiohttp, langsmith, langchain-core, langchain-text-splitters, langchain\n", "Successfully installed PyYAML-6.0.2 SQLAlchemy-2.0.38 aiohappyeyeballs-2.4.6 aiohttp-3.11.12 aiosignal-1.3.2 annotated-types-0.7.0 anyio-4.8.0 attrs-25.1.0 certifi-2025.1.31 charset-normalizer-3.4.1 frozenlist-1.5.0 h11-0.14.0 httpcore-1.0.7 httpx-0.28.1 idna-3.10 jsonpatch-1.33 jsonpointer-3.0.0 langchain-0.3.19 langchain-core-0.3.37 langchain-text-splitters-0.3.6 langsmith-0.3.10 multidict-6.1.0 numpy-2.2.3 orjson-3.10.15 propcache-0.3.0 pydantic-2.10.6 pydantic-core-2.27.2 requests-2.32.3 requests-toolbelt-1.0.0 sniffio-1.3.1 tenacity-9.0.0 typing-extensions-4.12.2 urllib3-2.3.0 yarl-1.18.3 zstandard-0.23.0\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install langchain -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting langchain-openai\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/b5/49/302754c09f955e4a240efe83e48f4e79149d50ca52b3f4731365f1be94b1/langchain_openai-0.3.6-py3-none-any.whl (54 kB)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.35 in ./.venv/lib/python3.13/site-packages (from langchain-openai) (0.3.37)\n", "Collecting openai<2.0.0,>=1.58.1 (from langchain-openai)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/9a/1a/e62718f311daa26d208800976d7944e5ee6d503e1ea474522b2a15a904bb/openai-1.64.0-py3-none-any.whl (472 kB)\n", "Collecting tiktoken<1,>=0.7 (from langchain-openai)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/80/0e/f38ba35713edb8d4197ae602e80837d574244ced7fb1b6070b31c29816e0/tiktoken-0.9.0-cp313-cp313-macosx_11_0_arm64.whl (1.0 MB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m9.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hRequirement already satisfied: langsmith<0.4,>=0.1.125 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain-openai) (0.3.10)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain-openai) (9.0.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain-openai) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain-openai) (6.0.2)\n", "Requirement already satisfied: packaging<25,>=23.2 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain-openai) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain-openai) (4.12.2)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.35->langchain-openai) (2.10.6)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in ./.venv/lib/python3.13/site-packages (from openai<2.0.0,>=1.58.1->langchain-openai) (4.8.0)\n", "Collecting distro<2,>=1.7.0 (from openai<2.0.0,>=1.58.1->langchain-openai)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl (20 kB)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from openai<2.0.0,>=1.58.1->langchain-openai) (0.28.1)\n", "Collecting jiter<1,>=0.4.0 (from openai<2.0.0,>=1.58.1->langchain-openai)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a4/8f/396ddb4e292b5ea57e45ade5dc48229556b9044bad29a3b4b2dddeaedd52/jiter-0.8.2-cp313-cp313-macosx_11_0_arm64.whl (309 kB)\n", "Requirement already satisfied: sniffio in ./.venv/lib/python3.13/site-packages (from openai<2.0.0,>=1.58.1->langchain-openai) (1.3.1)\n", "Collecting tqdm>4 (from openai<2.0.0,>=1.58.1->langchain-openai)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl (78 kB)\n", "Collecting regex>=2022.1.18 (from tiktoken<1,>=0.7->langchain-openai)\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/09/c9/4e68181a4a652fb3ef5099e077faf4fd2a694ea6e0f806a7737aff9e758a/regex-2024.11.6-cp313-cp313-macosx_11_0_arm64.whl (284 kB)\n", "Requirement already satisfied: requests>=2.26.0 in ./.venv/lib/python3.13/site-packages (from tiktoken<1,>=0.7->langchain-openai) (2.32.3)\n", "Requirement already satisfied: idna>=2.8 in ./.venv/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.58.1->langchain-openai) (3.10)\n", "Requirement already satisfied: certifi in ./.venv/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.58.1->langchain-openai) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in ./.venv/lib/python3.13/site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.58.1->langchain-openai) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in ./.venv/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.58.1->langchain-openai) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in ./.venv/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.35->langchain-openai) (3.0.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.35->langchain-openai) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.35->langchain-openai) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.35->langchain-openai) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain-core<1.0.0,>=0.3.35->langchain-openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain-core<1.0.0,>=0.3.35->langchain-openai) (2.27.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.13/site-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain-openai) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.13/site-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain-openai) (2.3.0)\n", "Installing collected packages: tqdm, regex, jiter, distro, tiktoken, openai, langchain-openai\n", "Successfully installed distro-1.9.0 jiter-0.8.2 langchain-openai-0.3.6 openai-1.64.0 regex-2024.11.6 tiktoken-0.9.0 tqdm-4.67.1\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install langchain-openai -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: langchain-deepseek in ./.venv/lib/python3.13/site-packages (0.1.2)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.34 in ./.venv/lib/python3.13/site-packages (from langchain-deepseek) (0.3.37)\n", "Requirement already satisfied: langchain-openai<1.0.0,>=0.3.5 in ./.venv/lib/python3.13/site-packages (from langchain-deepseek) (0.3.6)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (0.3.10)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (9.0.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (6.0.2)\n", "Requirement already satisfied: packaging<25,>=23.2 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (4.12.2)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (2.10.6)\n", "Requirement already satisfied: openai<2.0.0,>=1.58.1 in ./.venv/lib/python3.13/site-packages (from langchain-openai<1.0.0,>=0.3.5->langchain-deepseek) (1.64.0)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in ./.venv/lib/python3.13/site-packages (from langchain-openai<1.0.0,>=0.3.5->langchain-deepseek) (0.9.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in ./.venv/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (3.0.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (3.10.15)\n", "Requirement already satisfied: requests<3,>=2 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (2.32.3)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (0.23.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in ./.venv/lib/python3.13/site-packages (from openai<2.0.0,>=1.58.1->langchain-openai<1.0.0,>=0.3.5->langchain-deepseek) (4.8.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in ./.venv/lib/python3.13/site-packages (from openai<2.0.0,>=1.58.1->langchain-openai<1.0.0,>=0.3.5->langchain-deepseek) (1.9.0)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in ./.venv/lib/python3.13/site-packages (from openai<2.0.0,>=1.58.1->langchain-openai<1.0.0,>=0.3.5->langchain-deepseek) (0.8.2)\n", "Requirement already satisfied: sniffio in ./.venv/lib/python3.13/site-packages (from openai<2.0.0,>=1.58.1->langchain-openai<1.0.0,>=0.3.5->langchain-deepseek) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in ./.venv/lib/python3.13/site-packages (from openai<2.0.0,>=1.58.1->langchain-openai<1.0.0,>=0.3.5->langchain-deepseek) (4.67.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (2.27.2)\n", "Requirement already satisfied: regex>=2022.1.18 in ./.venv/lib/python3.13/site-packages (from tiktoken<1,>=0.7->langchain-openai<1.0.0,>=0.3.5->langchain-deepseek) (2024.11.6)\n", "Requirement already satisfied: idna>=2.8 in ./.venv/lib/python3.13/site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.58.1->langchain-openai<1.0.0,>=0.3.5->langchain-deepseek) (3.10)\n", "Requirement already satisfied: certifi in ./.venv/lib/python3.13/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in ./.venv/lib/python3.13/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in ./.venv/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (0.14.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain-deepseek) (2.3.0)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install  langchain-deepseek -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Hello langchain\n", "********"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='作为一个人工智能，我是由OpenAI创建的。我被设计用来理解和生成人类语言，帮助人们获取信息，完成任务，学习新的主题，等等。我可以在各种语境中进行交流，包括但不限于教育，工作，娱乐等。我不断学习和进步，但我并不完美，我可能会犯错误或理解不准确。我尊重用户的隐私，不会存储或分享个人信息。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 144, 'prompt_tokens': 15, 'total_tokens': 159, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-d9ee2cda-762a-411e-abee-89b55690a7c7-0', usage_metadata={'input_tokens': 15, 'output_tokens': 144, 'total_tokens': 159, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_openai import ChatOpenAI\n", "from dotenv import load_dotenv\n", "import os\n", "load_dotenv()\n", "\n", "llm = ChatOpenAI(\n", "    model=\"gpt-4\",\n", "    temperature=0,\n", "    api_key=os.getenv('OPENAI_API_KEY'),\n", "    base_url=os.getenv('OPENAI_API_BASE'),\n", "    )\n", "llm.invoke(\"介绍下你自己\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='您好！我是由中国的深度求索公司开发的智能助手DeepSeek-R1。如您有任何任何问题，我会尽我所能为您提供帮助。', additional_kwargs={'refusal': None, 'reasoning_content': '您好！我是由中国的深度求求公司开发的智能助手DeepSeek-R1。如您有任何任何问题，我会尽我所能为您提供帮助。'}, response_metadata={'token_usage': {'completion_tokens': 66, 'prompt_tokens': 8, 'total_tokens': 74, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-R1', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-3dc9b17d-ea5b-4c9f-a16e-bb4810eb5793-0', usage_metadata={'input_tokens': 8, 'output_tokens': 66, 'total_tokens': 74, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_deepseek import ChatDeepSeek\n", "from dotenv import load_dotenv\n", "import os\n", "load_dotenv()\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-R1\",\n", "    temperature=0,\n", "    api_key=os.getenv(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.getenv(\"DEEPSEEK_API_BASE\"),\n", "    )\n", "llm.invoke(\"介绍下你自己\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}