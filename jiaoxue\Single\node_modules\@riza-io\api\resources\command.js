"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Command = void 0;
const resource_1 = require("../resource.js");
class Command extends resource_1.APIResource {
    /**
     * Run a script in a secure, isolated environment. Scripts can read from `stdin`
     * and write to `stdout` or `stderr`. They can access input files, environment
     * variables and command line arguments.
     *
     * @example
     * ```ts
     * const response = await client.command.exec({
     *   code: 'print("Hello world!")',
     *   language: 'PYTHON',
     * });
     * ```
     */
    exec(body, options) {
        return this._client.post('/v1/execute', { body, ...options });
    }
    /**
     * Run a function in a secure, isolated environment. Define a function named
     * `execute`. The function will be passed `input` as an object.
     *
     * @example
     * ```ts
     * const response = await client.command.execFunc({
     *   code: 'def execute(input): return { "name": input["name"], "executed": True }',
     *   language: 'python',
     *   input: { name: '<PERSON>' },
     * });
     * ```
     */
    execFunc(body, options) {
        return this._client.post('/v1/execute-function', { body, ...options });
    }
}
exports.Command = Command;
//# sourceMappingURL=command.js.map