{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 相似性分数\n", "****\n", "- 根据相似性打分过滤\n", "- 为文档添加分数"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 相似性分数搜索\n", "***\n", "以Chrom为例"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m25.0\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install -qU \"langchain-chroma>=0.1.2\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "import os\n", "embeddings_model = OpenAIEmbeddings(\n", "    model=\"BAAI/bge-m3\",\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\")+\"/v1\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["初始化chroma客户端"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from langchain_chroma import Chroma\n", "\n", "vector_store = Chroma(\n", "    collection_name=\"example_collection\",\n", "    embedding_function=embeddings_model,\n", "    persist_directory=\"chroma_langchain_db\",  # 可选参数，指定持久化目录\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Add of existing embedding ID: 1\n", "Add of existing embedding ID: 2\n", "Add of existing embedding ID: 3\n", "Insert of existing embedding ID: 1\n", "Insert of existing embedding ID: 2\n", "Insert of existing embedding ID: 3\n"]}], "source": ["import chromadb\n", "\n", "persistent_client = chromadb.PersistentClient()\n", "collection = persistent_client.get_or_create_collection(\"collection_name\")\n", "collection.add(ids=[\"1\", \"2\", \"3\"], documents=[\"a\", \"b\", \"c\"])\n", "\n", "vector_store_from_client = Chroma(\n", "    client=persistent_client,\n", "    collection_name=\"collection_name\",\n", "    embedding_function=embeddings_model,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["添加一组文档"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["['19cc65c7-bdad-44fd-8012-35f920e693fe',\n", " 'c6a547e1-833f-4b2d-ad16-69079886464b',\n", " 'd2db9277-98f6-4317-ae8f-a24afa62976c',\n", " 'b0410821-0e3a-47e0-8d4a-59c5d6025440',\n", " 'a59cd9c6-48a6-40ca-a190-2efabbe41c79',\n", " 'da0268aa-431d-4e64-a500-11cea860c6e0',\n", " '6384556c-1d8f-400e-af4f-932457205c6e',\n", " 'd95a31ac-d5fc-4bbb-ab25-59b9b414a948',\n", " '9922fb95-702c-4172-9300-f41db3270677',\n", " '99895a8e-57f3-439f-8261-6f560e37b2bc']"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from uuid import uuid4\n", "\n", "from langchain_core.documents import Document\n", "\n", "document_1 = Document(\n", "    page_content=\"I had chocolate chip pancakes and scrambled eggs for breakfast this morning.\",\n", "    metadata={\"source\": \"tweet\"},\n", "    id=1,\n", ")\n", "\n", "document_2 = Document(\n", "    page_content=\"The weather forecast for tomorrow is cloudy and overcast, with a high of 62 degrees.\",\n", "    metadata={\"source\": \"news\"},\n", "    id=2,\n", ")\n", "\n", "document_3 = Document(\n", "    page_content=\"Building an exciting new project with <PERSON><PERSON><PERSON><PERSON> - come check it out!\",\n", "    metadata={\"source\": \"tweet\"},\n", "    id=3,\n", ")\n", "\n", "document_4 = Document(\n", "    page_content=\"Robbers broke into the city bank and stole $1 million in cash.\",\n", "    metadata={\"source\": \"news\"},\n", "    id=4,\n", ")\n", "\n", "document_5 = Document(\n", "    page_content=\"Wow! That was an amazing movie. I can't wait to see it again.\",\n", "    metadata={\"source\": \"tweet\"},\n", "    id=5,\n", ")\n", "\n", "document_6 = Document(\n", "    page_content=\"Is the new iPhone worth the price? Read this review to find out.\",\n", "    metadata={\"source\": \"website\"},\n", "    id=6,\n", ")\n", "\n", "document_7 = Document(\n", "    page_content=\"The top 10 soccer players in the world right now.\",\n", "    metadata={\"source\": \"website\"},\n", "    id=7,\n", ")\n", "\n", "document_8 = Document(\n", "    page_content=\"LangGraph is the best framework for building stateful, agentic applications!\",\n", "    metadata={\"source\": \"tweet\"},\n", "    id=8,\n", ")\n", "\n", "document_9 = Document(\n", "    page_content=\"The stock market is down 500 points today due to fears of a recession.\",\n", "    metadata={\"source\": \"news\"},\n", "    id=9,\n", ")\n", "\n", "document_10 = Document(\n", "    page_content=\"I have a bad feeling I am going to get deleted :(\",\n", "    metadata={\"source\": \"tweet\"},\n", "    id=10,\n", ")\n", "\n", "documents = [\n", "    document_1,\n", "    document_2,\n", "    document_3,\n", "    document_4,\n", "    document_5,\n", "    document_6,\n", "    document_7,\n", "    document_8,\n", "    document_9,\n", "    document_10,\n", "]\n", "uuids = [str(uuid4()) for _ in range(len(documents))]\n", "\n", "vector_store.add_documents(documents=documents, ids=uuids)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用相似性分数检索"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* [SIM=0.837352] Robbers broke into the city bank and stole $1 million in cash. [{'source': 'news'}]\n"]}], "source": ["results = vector_store.similarity_search_with_score(\n", "    \"Will it be hot tomorrow?\", k=1, filter={\"source\": \"news\"}\n", ")\n", "for res, score in results:\n", "    print(f\"* [SIM={score:3f}] {res.page_content} [{res.metadata}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 为文档添加分数\n", "****\n", "通过一个自定义链，可以为原始文档增加相关性评分"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from typing import List\n", "\n", "from langchain_core.documents import Document\n", "from langchain_core.runnables import chain\n", "\n", "\n", "@chain\n", "def retriever(query: str) -> List[Document]:\n", "    docs, scores = zip(*vector_store.similarity_search_with_score(query))\n", "    for doc, score in zip(docs, scores):\n", "        doc.metadata[\"score\"] = score\n", "\n", "    return docs"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["(Document(id='19cc65c7-bdad-44fd-8012-35f920e693fe', metadata={'source': 'tweet', 'score': 0.7794285171572265}, page_content='I had chocolate chip pancakes and scrambled eggs for breakfast this morning.'),\n", " Document(id='c92c7a2d-2c76-4064-a5df-bfcb340990aa', metadata={'source': 'tweet', 'score': 0.779443091410297}, page_content='I had chocolate chip pancakes and scrambled eggs for breakfast this morning.'),\n", " Document(id='99895a8e-57f3-439f-8261-6f560e37b2bc', metadata={'source': 'tweet', 'score': 0.8598275718992796}, page_content='I have a bad feeling I am going to get deleted :('),\n", " Document(id='a9734ba5-6331-43d0-886f-1c381cbfa123', metadata={'source': 'tweet', 'score': 0.8601369578337088}, page_content='I have a bad feeling I am going to get deleted :('))"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["result = retriever.invoke(\"Rob<PERSON>\")\n", "result"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}