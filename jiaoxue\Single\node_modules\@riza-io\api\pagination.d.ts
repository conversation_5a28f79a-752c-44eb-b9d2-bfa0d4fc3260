import { AbstractPage, Response, APIClient, FinalRequestOptions, PageInfo } from "./core.js";
export interface DefaultPaginationResponse<Item> {
    data: Array<Item>;
}
export interface DefaultPaginationParams {
    starting_after?: string;
    limit?: number;
}
export declare class DefaultPagination<Item extends {
    id: string;
}> extends AbstractPage<Item> implements DefaultPaginationResponse<Item> {
    data: Array<Item>;
    constructor(client: APIClient, response: Response, body: DefaultPaginationResponse<Item>, options: FinalRequestOptions);
    getPaginatedItems(): Item[];
    nextPageParams(): Partial<DefaultPaginationParams> | null;
    nextPageInfo(): PageInfo | null;
}
export interface RuntimesPaginationResponse<Item> {
    runtimes: Array<Item>;
}
export interface RuntimesPaginationParams {
    starting_after?: string;
    limit?: number;
}
export declare class RuntimesPagination<Item extends {
    id: string;
}> extends AbstractPage<Item> implements RuntimesPaginationResponse<Item> {
    runtimes: Array<Item>;
    constructor(client: APIClient, response: Response, body: RuntimesPaginationResponse<Item>, options: FinalRequestOptions);
    getPaginatedItems(): Item[];
    nextPageParams(): Partial<RuntimesPaginationParams> | null;
    nextPageInfo(): PageInfo | null;
}
export interface ToolsPaginationResponse<Item> {
    tools: Array<Item>;
}
export interface ToolsPaginationParams {
    starting_after?: string;
    limit?: number;
}
export declare class ToolsPagination<Item extends {
    id: string;
}> extends AbstractPage<Item> implements ToolsPaginationResponse<Item> {
    tools: Array<Item>;
    constructor(client: APIClient, response: Response, body: ToolsPaginationResponse<Item>, options: FinalRequestOptions);
    getPaginatedItems(): Item[];
    nextPageParams(): Partial<ToolsPaginationParams> | null;
    nextPageInfo(): PageInfo | null;
}
export interface SecretsPaginationResponse<Item> {
    secrets: Array<Item>;
}
export interface SecretsPaginationParams {
    starting_after?: string;
    limit?: number;
}
export declare class SecretsPagination<Item extends {
    id: string;
}> extends AbstractPage<Item> implements SecretsPaginationResponse<Item> {
    secrets: Array<Item>;
    constructor(client: APIClient, response: Response, body: SecretsPaginationResponse<Item>, options: FinalRequestOptions);
    getPaginatedItems(): Item[];
    nextPageParams(): Partial<SecretsPaginationParams> | null;
    nextPageInfo(): PageInfo | null;
}
//# sourceMappingURL=pagination.d.ts.map