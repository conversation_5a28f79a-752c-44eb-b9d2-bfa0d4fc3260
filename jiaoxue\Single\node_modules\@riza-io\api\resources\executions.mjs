// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
import { APIResource } from "../resource.mjs";
import { isRequestOptions } from "../core.mjs";
import { DefaultPagination } from "../pagination.mjs";
export class Executions extends APIResource {
    list(query = {}, options) {
        if (isRequestOptions(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/executions', ExecutionsDefaultPagination, { query, ...options });
    }
    /**
     * Retrieves an execution.
     */
    get(id, options) {
        return this._client.get(`/v1/executions/${id}`, options);
    }
}
export class ExecutionsDefaultPagination extends DefaultPagination {
}
Executions.ExecutionsDefaultPagination = ExecutionsDefaultPagination;
//# sourceMappingURL=executions.mjs.map