{"t":{"$date":"2025-07-31T15:19:58.179Z"},"s":"E","c":"MONGOSH","id":1000000006,"ctx":"telemetry","msg":"Error: Failed to resolve machine ID","attr":{"stack":"Error: Failed to resolve machine ID\n    at i (eval at module.exports (node:lib-boxednode/mongosh:103:20), <anonymous>:341:199750)\n    at async LoggingAndTelemetry.setupTelemetry (eval at module.exports (node:lib-boxednode/mongosh:103:20), <anonymous>:341:307813)","name":"Error","message":"Failed to resolve machine ID","code":null}}
{"t":{"$date":"2025-07-31T15:19:58.199Z"},"s":"I","c":"MONGOSH","id":1000000005,"ctx":"config","msg":"User updated"}
{"t":{"$date":"2025-07-31T15:19:58.201Z"},"s":"I","c":"MONGOS<PERSON>","id":1000000048,"ctx":"config","msg":"Loading global configuration file","attr":{"filename":"/etc/mongosh.conf","found":false}}
{"t":{"$date":"2025-07-31T15:19:58.202Z"},"s":"I","c":"MONGOSH","id":1000000000,"ctx":"log","msg":"Starting log","attr":{"execPath":"/usr/bin/mongosh","envInfo":{"EDITOR":null,"NODE_OPTIONS":null,"TERM":"xterm"},"version":"2.5.6","distributionKind":"compiled","buildArch":"x64","buildPlatform":"linux","buildTarget":"linux-x64","buildTime":"2025-07-17T14:56:25.712Z","gitVersion":"d31796eee7e35790748e6114858cd55565e8152d","nodeVersion":"v20.19.4","opensslVersion":"3.0.15+quic","sharedOpenssl":false,"runtimeArch":"x64","runtimePlatform":"linux","runtimeGlibcVersion":"2.39","deps":{"nodeDriverVersion":"6.16.0","libmongocryptVersion":"1.13.0","libmongocryptNodeBindingsVersion":"6.3.0","kerberosVersion":"2.1.0"}}}
{"t":{"$date":"2025-07-31T15:19:58.275Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000049,"ctx":"mongosh-connect","msg":"Loaded system CA list","attr":{"caCount":296,"asyncFallbackError":null,"systemCertsError":null,"messages":[]}}
{"t":{"$date":"2025-07-31T15:19:58.289Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000042,"ctx":"mongosh-connect","msg":"Initiating connection attempt","attr":{"uri":"mongodb://127.0.0.1:27017/?directConnection=true&serverSelectionTimeoutMS=2000&appName=mongosh+2.5.6","driver":{"name":"nodejs|mongosh","version":"6.16.0|2.5.6"},"devtoolsConnectVersion":"3.4.1","host":"127.0.0.1:27017"}}
{"t":{"$date":"2025-07-31T15:19:58.312Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000035,"ctx":"mongosh-connect","msg":"Server heartbeat succeeded","attr":{"connectionId":"127.0.0.1:27017"}}
{"t":{"$date":"2025-07-31T15:19:58.357Z"},"s":"I","c":"DEVTOOLS-CONNECT","id":1000000037,"ctx":"mongosh-connect","msg":"Connection attempt finished"}
{"t":{"$date":"2025-07-31T15:19:58.358Z"},"s":"I","c":"MONGOSH","id":1000000010,"ctx":"shell-api","msg":"Initialized context","attr":{"method":"setCtx","arguments":{}}}
{"t":{"$date":"2025-07-31T15:19:58.360Z"},"s":"I","c":"MONGOSH-SNIPPETS","id":1000000019,"ctx":"snippets","msg":"Loaded snippets","attr":{"installdir":"/data/db/.mongodb/mongosh/snippets"}}
{"t":{"$date":"2025-07-31T15:19:58.363Z"},"s":"I","c":"MONGOSH","id":1000000003,"ctx":"repl","msg":"Start loading CLI scripts"}
{"t":{"$date":"2025-07-31T15:19:58.364Z"},"s":"I","c":"MONGOSH","id":1000000013,"ctx":"repl","msg":"Evaluating script passed on the command line"}
{"t":{"$date":"2025-07-31T15:19:58.365Z"},"s":"I","c":"MONGOSH","id":1000000007,"ctx":"repl","msg":"Evaluating input","attr":{"input":"db.runCommand({ping: 1})"}}
{"t":{"$date":"2025-07-31T15:19:58.366Z"},"s":"I","c":"MONGOSH","id":1000000011,"ctx":"shell-api","msg":"Performed API call","attr":{"method":"runCommand","class":"Database","db":"test","arguments":{"cmd":{"ping":1}}}}
{"t":{"$date":"2025-07-31T15:19:58.369Z"},"s":"I","c":"MONGOSH","id":1000000045,"ctx":"analytics","msg":"Flushed outstanding data","attr":{"flushError":"Trying to persist throttle state before userId is set","flushDuration":0}}
