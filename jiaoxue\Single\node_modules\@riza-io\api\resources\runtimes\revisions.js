"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Revisions = void 0;
const resource_1 = require("../../resource.js");
class Revisions extends resource_1.APIResource {
    /**
     * Creates a new runtime revision.
     */
    create(id, body, options) {
        return this._client.post(`/v1/runtimes/${id}/revisions`, { body, ...options });
    }
    /**
     * Lists all revisions for a runtime.
     */
    list(id, options) {
        return this._client.get(`/v1/runtimes/${id}/revisions`, options);
    }
    /**
     * Retrieves a runtime revision.
     */
    get(runtimeId, revisionId, options) {
        return this._client.get(`/v1/runtimes/${runtimeId}/revisions/${revisionId}`, options);
    }
}
exports.Revisions = Revisions;
//# sourceMappingURL=revisions.js.map