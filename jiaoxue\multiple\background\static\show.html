<!DOCTYPE html>
<html>
<head>
    <title>小浪助手 - <PERSON><PERSON><PERSON><PERSON>智能客服</title>
    <!-- 修改：使用浏览器完整版本的 highlight.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/styles/atom-one-dark.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .header {
            background-color: #9b4cc7;
            color: white;
            width: 100%;
            padding: 15px 0;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 28px;
        }
        
        .header p {
            margin: 5px 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .main-container {
            width: 90%;
            max-width: 800px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .avatar-container {
            position: relative;
            /* 修改：增大数字人头像容器的尺寸 */
            width: 350px;
            height: 350px;
            margin-bottom: 20px;
            border-radius: 50%;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(155, 76, 199, 0.3);
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #222;
        }
        
        video {
            position: absolute;
            height: 100%;
            transform: scale(1.2);
            object-fit: cover;
        }
        
        .status-indicator {
            display: none;
        }
        
        .status-dot {
            height: 10px;
            width: 10px;
            border-radius: 50%;
            background-color: #4CAF50;
            margin-right: 5px;
            display: inline-block;
        }
        
        .disconnected {
            background-color: #F44336;
        }
        
        #chatHistory {
            width: 100%;
            /* 修改：增高聊天历史展示区域 */
            height: 350px;
            padding: 15px;
            border: 2px solid #9b4cc7;
            overflow-y: auto;
            background-color: white;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            box-sizing: border-box;
        }
        
        .user-message {
            color: #333;
            margin-bottom: 10px;
            padding: 8px 12px;
            background-color: #f0e6f5;
            border-left: 4px solid #9b4cc7;
        }
        
        .ai-message {
            color: #333;
            margin-bottom: 15px;
            padding: 8px 12px;
            background-color: white;
            border-left: 4px solid #6b3287;
        }
        
        .typing-indicator {
            color: #555;
            font-style: italic;
            margin-bottom: 5px;
            padding: 8px 12px;
            background-color: white;
            border-left: 4px solid #6b3287;
            animation: pulse 1.5s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }
        
        .input-container {
            width: 100%;
            margin: 15px 0;
            display: flex;
            gap: 10px;
        }
        
        #chatInput {
            flex-grow: 1;
            height: 46px;
            padding: 0 15px;
            border: 2px solid #9b4cc7;
            background-color: white;
            font-size: 16px;
            outline: none;
        }
        
        #chatInput:focus {
            border-color: #6b3287;
        }
        
        button, select {
            height: 50px;
            background-color: #9b4cc7;
            border: none;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
            padding: 0 15px;
        }
        
        button:hover, select:hover {
            background-color: #7b3c9c;
        }
        
        select {
            padding-right: 25px;
            appearance: none;
            background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23FFFFFF%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
            background-repeat: no-repeat;
            background-position: right 10px top 50%;
            background-size: 12px auto;
        }
        
        .helper-text {
            width: 100%;
            color: #666;
            font-size: 13px;
            margin-top: 5px;
            text-align: left;
        }
        
        .footer {
            margin-top: 20px;
            color: #666;
            font-size: 12px;
            text-align: center;
            width: 100%;
            padding: 10px 0;
        }
        
        /* 添加代码高亮样式 */
        pre {
            margin: 0;
            padding: 0;
        }
        
        code {
            font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
            font-size: 14px;
            display: block;
            padding: 10px;
            overflow-x: auto;
            border-radius: 5px;
            background-color: #282c34;
        }
        
        /* 添加加载动画 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 5;
            color: white;
            border-radius: 50%;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 5px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #9b4cc7;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 15px;
        }
        
        .loading-text {
            font-size: 14px;
            text-align: center;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* 添加全局状态指示器样式 */
        .global-status {
            position: fixed;
            top: 10px;
            right: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }
        
        .status-item {
            display: flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 20px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            font-size: 14px;
        }
        
        .status-icon {
            height: 12px;
            width: 12px;
            border-radius: 50%;
            margin-right: 8px;
            display: inline-block;
        }
        
        .status-connected {
            background-color: #4CAF50;
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.8);
        }
        
        .status-disconnected {
            background-color: #F44336;
            box-shadow: 0 0 8px rgba(244, 67, 54, 0.8);
        }
        
        .status-connecting {
            background-color: #FFC107;
            box-shadow: 0 0 8px rgba(255, 193, 7, 0.8);
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/microsoft-cognitiveservices-speech-sdk@latest/distrib/browser/microsoft.cognitiveservices.speech.sdk.bundle-min.js">
    </script>
    <!-- 修改：使用适合浏览器的完整版 highlight.js -->
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/highlight.min.js"></script>
    <!-- 按需加载语言包 -->
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/languages/python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/languages/javascript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/languages/json.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/languages/bash.min.js"></script>
</head>
<body>
    <!-- 添加全局状态指示器 -->
    <div class="global-status">
        <div class="status-item">
            <span class="status-icon status-connecting" id="webrtcStatus"></span>
            <span id="webrtcStatusText">WebRTC: 正在连接...</span>
        </div>
        <div class="status-item">
            <span class="status-icon status-connecting" id="websocketStatus"></span>
            <span id="websocketStatusText">WebSocket: 正在连接...</span>
        </div>
    </div>

    <div class="header">
        <h1>小浪助手</h1>
        <p>又一个AI智能体客服</p>
    </div>

    <div class="main-container">
        <div class="avatar-container">
            <!-- 加载动画 -->
            <div class="loading-overlay" id="loadingOverlay">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载数字人...</div>
            </div>
            <!-- 视频将由JavaScript动态添加到这里 -->
            <div class="status-indicator">
                <span class="status-dot" id="connectionStatus"></span>
                <span id="statusText">正在连接...</span>
            </div>
        </div>
        
        <div id="chatHistory"></div>
        
        <div class="helper-text">
            提示：你可以询问小浪助手关于LangChain的学习问题，例如："LangGraph是什么？"
        </div>
        
        <div class="input-container">
            <input type="text" id="chatInput" placeholder="请输入您想问的问题...">
            <select id="yinse">
                <option value="zh-HK-HiuMaanNeural">中文粤语</option>
                <option value="zh-TW-HsiaoChenNeural">中文台湾</option>
                <option value="zh-CN-shaanxi-XiaoniNeural">中文陕西话</option>
                <option value="zh-CN-liaoning-XiaobeiNeural">中文东北话</option>
                <option selected value="zh-CN-XiaomoNeural">中文普通话</option>
                <option value="wuu-CN-XiaotongNeural">中文吴语</option>
            </select>
            <button id="PlayerButton">发送</button>
            <button id="clearButton">清空对话</button>
        </div>
    </div>

    <div class="footer">
        © 2025 小浪助手 - 1goto.ai
    </div>
    
    <script>
         var SpeechSDK;
         var peerConnection;
         var websocket = null;
         var currentAvatarSynthesizer = null;
         
         // 初始化 highlight.js
         document.addEventListener('DOMContentLoaded', (event) => {
             hljs.configure({
                 languages: ['javascript', 'python', 'json', 'bash', 'plaintext']
             });
         });
         
         // 更新WebRTC连接状态
         function updateWebRTCStatus(status, message) {
             const statusIcon = document.getElementById("webrtcStatus");
             const statusText = document.getElementById("webrtcStatusText");
             
             // 移除所有可能的类
             statusIcon.classList.remove("status-connected", "status-disconnected", "status-connecting");
             
             if (status === "connected") {
                 statusIcon.classList.add("status-connected");
                 statusText.textContent = "WebRTC: 已连接";
             } else if (status === "connecting") {
                 statusIcon.classList.add("status-connecting");
                 statusText.textContent = "WebRTC: " + (message || "正在连接...");
             } else {
                 statusIcon.classList.add("status-disconnected");
                 statusText.textContent = "WebRTC: " + (message || "未连接");
             }
             
             // 同时更新旧的状态指示器，保持兼容性
             updateConnectionStatus(status === "connected", message);
         }
         
         // 更新WebSocket连接状态
         function updateWebSocketStatus(status, message) {
             const statusIcon = document.getElementById("websocketStatus");
             const statusText = document.getElementById("websocketStatusText");
             
             // 移除所有可能的类
             statusIcon.classList.remove("status-connected", "status-disconnected", "status-connecting");
             
             if (status === "connected") {
                 statusIcon.classList.add("status-connected");
                 statusText.textContent = "WebSocket: 已连接";
             } else if (status === "connecting") {
                 statusIcon.classList.add("status-connecting");
                 statusText.textContent = "WebSocket: " + (message || "正在连接...");
             } else {
                 statusIcon.classList.add("status-disconnected");
                 statusText.textContent = "WebSocket: " + (message || "未连接");
             }
         }
         
         // 初始化WebSocket连接
         function initWebSocket() {
             if (websocket && websocket.readyState === WebSocket.OPEN) {
                 console.log("WebSocket已连接");
                 updateWebSocketStatus("connected");
                 return;
             }
             
             updateWebSocketStatus("connecting", "正在连接...");
             
             websocket = new WebSocket("ws://localhost:8000/ws/chat");
             
             websocket.onopen = function(e) {
                 console.log("WebSocket连接已建立");
                 updateWebSocketStatus("connected");
                 // 发送心跳包
                 setInterval(function() {
                     if (websocket && websocket.readyState === WebSocket.OPEN) {
                         websocket.send(JSON.stringify({type: "ping"}));
                     }
                 }, 25000);
             };
             
             websocket.onmessage = function(e) {
                 const data = JSON.parse(e.data);
                 console.log("收到WebSocket消息:", data);
                 
                 if (data.type === "pong") {
                     console.log("收到心跳响应");
                 } else if (data.type === "stream") {
                     // 处理流式消息，更新AI回复
                     handleStreamMessage(data.content);
                 } else if (data.type === "complete") {
                     // 消息完成，处理最终回复
                     handleCompleteMessage(data.content, data.qingxu || "",data.action);
                 }
             };
             
             websocket.onclose = function(e) {
                 console.log("WebSocket连接已关闭:", e);
                 websocket = null;
                 updateWebSocketStatus("disconnected", "连接已断开");
                 // 可选：尝试重连
                 setTimeout(initWebSocket, 3000);
             };
             
             websocket.onerror = function(e) {
                 console.error("WebSocket错误:", e);
                 updateWebSocketStatus("disconnected", "连接错误");
             };
         }
         
         // 更新原有的连接状态显示（保留向后兼容）
         function updateConnectionStatus(isConnected, message) {
             const statusDot = document.getElementById("connectionStatus");
             const statusText = document.getElementById("statusText");
             
             if (isConnected) {
                 statusDot.classList.remove("disconnected");
                 statusText.textContent = "已连接";
             } else {
                 statusDot.classList.add("disconnected");
                 statusText.textContent = message || "未连接";
             }
         }
         
         // 改进的代码块格式化函数，更好地处理文本与代码混合的情况
         function formatCodeBlocks(text) {
             // 使用正则表达式查找所有代码块
             const codeBlockRegex = /```(\w*)([\s\S]*?)```/g;
             let lastIndex = 0;
             let result = '';
             let match;

             // 逐个处理找到的代码块
             while ((match = codeBlockRegex.exec(text)) !== null) {
                 // 添加代码块前的普通文本
                 result += text.substring(lastIndex, match.index);
                 
                 // 提取语言和代码内容
                 const language = match[1].trim() || 'plaintext';
                 const code = match[2].trim();
                 
                 // 构建带有语法高亮的HTML
                 result += `<pre><code class="language-${language}">${escapeHtml(code)}</code></pre>`;
                 
                 // 更新处理位置
                 lastIndex = match.index + match[0].length;
             }
             
             // 添加最后一个代码块后的普通文本
             result += text.substring(lastIndex);
             
             return result;
         }
         
         // HTML转义函数，防止代码中的HTML标签被错误解析
         function escapeHtml(str) {
             return str
                 .replace(/&/g, '&amp;')
                 .replace(/</g, '&lt;')
                 .replace(/>/g, '&gt;')
                 .replace(/"/g, '&quot;')
                 .replace(/'/g, '&#039;');
         }
         
         // 处理流式消息
         function handleStreamMessage(content) {
             const chatHistory = document.getElementById("chatHistory");
             const typingIndicator = document.querySelector(".typing-indicator");
             
             if (typingIndicator) {
                 // 更新现有的正在输入提示
                 const aiMessageElement = typingIndicator;
                 const textSpan = aiMessageElement.querySelector("span");
                 if (textSpan) {
                     const currentText = textSpan.getAttribute("data-text") || "";
                     const newText = currentText + content;
                     textSpan.setAttribute("data-text", newText);
                     textSpan.textContent = newText;
                 }
             } else {
                 // 创建新的AI消息元素
                 const aiMessageElement = document.createElement("div");
                 aiMessageElement.className = "ai-message typing-indicator";
                 aiMessageElement.innerHTML = "<strong>小浪助手:</strong> <span data-text='" + content + "'>" + content + "</span>";
                 chatHistory.appendChild(aiMessageElement);
             }
             
             // 自动滚动到底部
             chatHistory.scrollTop = chatHistory.scrollHeight;
         }
         
         // 处理完整消息
         function handleCompleteMessage(content, qingxu,action) {
             // 移除所有正在输入的提示，添加完整的AI消息
             const typingIndicator = document.querySelector(".typing-indicator");
             if (typingIndicator) {
                 typingIndicator.remove();
             }
             
             // 添加完整的AI消息到聊天历史
             addMessageToChat(content, false);
             
             // 语音合成
             if (currentAvatarSynthesizer) {
                 speakerHander(currentAvatarSynthesizer, content, qingxu,action);
             }
         }
         
         // 发送消息到WebSocket
         function sendMessageToWebSocket(message) {
             if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                 console.log("WebSocket未连接，尝试连接...");
                 initWebSocket();
                 setTimeout(() => sendMessageToWebSocket(message), 1000);
                 return;
             }
             
             const data = {
                 message: message,
                 voice: document.getElementById("yinse").value,
                 user_id: "user-" + Date.now()
             };
             
             websocket.send(JSON.stringify(data));
         }
         
         document.addEventListener("DOMContentLoaded", 
        () => {
            const cogSvcRegion = "westus2";
            const subscriptionKey = "你的微软云订阅ID";
            const speechConfig = SpeechSDK.SpeechConfig.fromSubscription(subscriptionKey, cogSvcRegion);
            // Set either the `SpeechSynthesisVoiceName` or `SpeechSynthesisLanguage`.
            speechConfig.speechSynthesisLanguage = "zh-HK";
            speechConfig.speechSynthesisVoiceName = "zh-HK-HiuGaaiNeural";
            var videoFormat = new SpeechSDK.AvatarVideoFormat()
            const avatarConfig = new SpeechSDK.AvatarConfig(
                "lisa", // Set avatar character here.
                "casual-sitting", // Set avatar style here.
                videoFormat, // Set video format here.
            );
            avatarConfig.backgroundColor = "#ffffff";
            // 初始化状态为"连接中"
            updateWebRTCStatus("connecting", "正在初始化...");
            
            const xhr = new XMLHttpRequest()
            xhr.open("GET", `https://${cogSvcRegion}.tts.speech.microsoft.com/cognitiveservices/avatar/relay/token/v1`)
            xhr.setRequestHeader("Ocp-Apim-Subscription-Key", subscriptionKey)
            xhr.addEventListener("readystatechange", function() {
            if (this.readyState === 4) {
                    const responseData = JSON.parse(this.responseText)
                    const iceServerUrl = responseData.Urls[0]
                    const iceServerUsername = responseData.Username
                    const iceServerCredential = responseData.Password
                    //setupWebRTC(iceServerUrl, iceServerUsername, iceServerCredential)
                    // Create WebRTC peer connection
                    peerConnection = new RTCPeerConnection({
                        iceServers: [{
                            urls: [ iceServerUrl ],
                            username: iceServerUsername,
                            credential: iceServerCredential
                        }]
                    });
                    // Fetch WebRTC video/audio streams and mount them to HTML video/audio player elements
                    peerConnection.ontrack = function (event) {
                        if (event.track.kind === 'video') {
                            console.log("video track received."+event)
                            const videoElement = document.createElement(event.track.kind)
                            videoElement.id = 'videoPlayer'
                            videoElement.srcObject = event.streams[0]
                            videoElement.autoplay = true
                            videoElement.muted = true
                            videoElement.playsInline = true
                            
                            // 将视频添加到avatar-container
                            const avatarContainer = document.querySelector('.avatar-container');
                            avatarContainer.insertBefore(videoElement, avatarContainer.firstChild);
                        }

                        if (event.track.kind === 'audio') {
                            const audioElement = document.createElement(event.track.kind)
                            audioElement.id = 'audioPlayer'
                            audioElement.srcObject = event.streams[0]
                            audioElement.muted = true
                            audioElement.autoplay = true
                            document.body.appendChild(audioElement) 
                        }
                    }

                    peerConnection.oniceconnectionstatechange = e => {
                        console.log("WebRTC status: " + peerConnection.iceConnectionState)

                        if (peerConnection.iceConnectionState === 'connected') {
                            console.log("WebRTC status:connected ")
                            updateWebRTCStatus("connected");
                            
                            // 隐藏加载动画
                            document.getElementById('loadingOverlay').style.display = 'none';
                        }

                        if (peerConnection.iceConnectionState === 'disconnected' || peerConnection.iceConnectionState === 'failed') {
                            console.log("WebRTC status:disconnected ")
                            updateWebRTCStatus("disconnected", "视频连接已断开");
                            
                            // 显示加载动画并更新文字
                            const loadingOverlay = document.getElementById('loadingOverlay');
                            loadingOverlay.style.display = 'flex';
                            loadingOverlay.querySelector('.loading-text').textContent = '连接已断开，正在重新连接...';
                            // 在WebRTC连接失败后的处理代码
                            // 尝试清理当前连接并重新初始化
                            setTimeout(() => {
                                console.log("尝试重新连接WebRTC...");
                                
                                // 清理现有的视频/音频元素
                                const existingVideo = document.getElementById('videoPlayer');
                                const existingAudio = document.getElementById('audioPlayer');
                                
                                if (existingVideo) {
                                    existingVideo.srcObject = null;
                                    existingVideo.remove();
                                }
                                
                                if (existingAudio) {
                                    existingAudio.srcObject = null;
                                    existingAudio.remove();
                                }
                                
                                // 关闭当前的连接
                                if (peerConnection) {
                                    peerConnection.close();
                                }
                                
                                // 刷新页面重新初始化所有连接
                                window.location.reload();
                            }, 3000);
                        }
                    }

                    // Offer to receive one video track, and one audio track
                    peerConnection.addTransceiver('video', { direction: 'sendrecv' })
                    peerConnection.addTransceiver('audio', { direction: 'sendrecv' })
                    // Create avatar synthesizer
                    var avatarSynthesizer = new SpeechSDK.AvatarSynthesizer(speechConfig, avatarConfig)
                    
                    // 保存全局引用
                    currentAvatarSynthesizer = avatarSynthesizer;

                    // Start avatar and establish WebRTC connection
                    avatarSynthesizer.startAvatarAsync(peerConnection).then(
                        (r) => { 
                            console.log("[" + (new Date()).toISOString() + "] Avatar started. Result ID: " + r.resultId)
                            
                            // 初始化WebSocket连接
                            initWebSocket();
                            
                            // 初始化对话
                            addMessageToChat("你好！我是小浪助手，一个专门帮助你学习LangChain的AI助手。有什么可以帮到你的吗？", false);
                            
                            // 绑定清空对话事件
                            document.getElementById("clearButton").addEventListener("click", function(){
                                document.getElementById("chatHistory").innerHTML = "";
                                // 恢复初始欢迎语
                                addMessageToChat("你好！我是小浪助手，一个专门帮助你学习LangChain的AI助手。有什么可以帮到你的吗？", false);
                            });
                            
                            // 绑定回车键发送消息
                            document.getElementById("chatInput").addEventListener("keypress", function(e){
                                if (e.key === "Enter") {
                                    document.getElementById("PlayerButton").click();
                                }
                            });
                            
                            document.getElementById("PlayerButton").addEventListener("click", function(){
                                var videoPlayer = document.getElementById("videoPlayer")
                                var audioPlayer = document.getElementById("audioPlayer")
                                videoPlayer.muted = false
                                audioPlayer.muted = false
                                videoPlayer.play()
                                audioPlayer.play()
                                
                                // 获取用户消息
                                const userMessage = document.getElementById("chatInput").value;
                                if (userMessage.trim() !== "") {
                                    // 添加用户消息到对话记录
                                    addMessageToChat(userMessage, true);
                                    
                                    // 通过WebSocket发送消息
                                    sendMessageToWebSocket(userMessage);
                                    
                                    // 清空输入框
                                    document.getElementById("chatInput").value = "";
                                }
                            })
                         }
                    ).catch(
                        (error) => {
                            console.log("[" + (new Date()).toISOString() + "] Unable to start avatar. Result ID: " + error);
                            updateWebRTCStatus("disconnected", "数字人启动失败");
                            
                            // 更新加载动画中的文字
                            const loadingText = document.querySelector('#loadingOverlay .loading-text');
                            loadingText.textContent = '数字人启动失败，请刷新页面重试';
                            loadingText.style.color = '#ff6b6b';
                        }
                    );
                }
            })
            xhr.send()
            if (!!window.SpeechSDK) {
                SpeechSDK = window.SpeechSDK;
            }
        })
        
        // 添加消息到对话记录
        function addMessageToChat(message, isUser) {
            const chatHistory = document.getElementById("chatHistory");
            const messageElement = document.createElement("div");
            messageElement.className = isUser ? "user-message" : "ai-message";
            
            // 如果是AI消息，处理可能包含的代码块
            if (!isUser) {
                // 处理代码块格式化
                message = formatCodeBlocks(message);
            }
            
            messageElement.innerHTML = (isUser ? "<strong>您:</strong> " : "<strong>小浪助手:</strong> ") + message;
            chatHistory.appendChild(messageElement);
            
            // 如果有代码块，应用语法高亮
            if (!isUser) {
                setTimeout(function() {
                    messageElement.querySelectorAll('pre code').forEach((block) => {
                        hljs.highlightBlock(block);
                        
                        // 增加代码块的可读性
                        block.style.padding = '12px';
                        block.style.borderRadius = '6px';
                        block.style.margin = '10px 0';
                        
                        // 添加语言标签显示
                        const language = block.className.replace('language-', '');
                        if (language && language !== 'python') {
                            const langTag = document.createElement('div');
                            langTag.className = 'code-lang-tag';
                            langTag.textContent = language;
                            langTag.style.position = 'relative';
                            langTag.style.top = '0';
                            langTag.style.right = '0';
                            langTag.style.fontSize = '12px';
                            langTag.style.padding = '2px 8px';
                            langTag.style.background = '#444';
                            langTag.style.color = '#eee';
                            langTag.style.borderRadius = '0 5px 0 5px';
                            langTag.style.float = 'right';
                            block.parentNode.insertBefore(langTag, block);
                        }
                    });
                }, 10);
            }
            
            // 自动滚动到底部
            chatHistory.scrollTop = chatHistory.scrollHeight;
        }
        
        // 旧的HTTP请求方式的chatWithAi函数已不再使用，但保留以备不时之需
        var chatWithAi = function(avatarSynthesizer){
            // 此函数已被WebSocket替代
            console.log("此函数已被WebSocket替代，不再使用");
        }
        
        var speakerHander = function(avatarSynthesizer,msg,qingxu,action){
            let yinse = document.getElementById("yinse").value
            let spokenSsml = `<speak version='1.0' xmlns='http://www.w3.org/2001/10/synthesis' xmlns:mstts='http://www.w3.org/2001/mstts' xml:lang='en-US'><voice name='${yinse}'> <mstts:express-as style='${qingxu}' role="YoungAdultFemale" styledegree="2">'<bookmark mark="gesture.'${action}'"/>${msg}'</mstts:express-as></voice></speak>`
            avatarSynthesizer.speakSsmlAsync(spokenSsml).then(
                (result) => {
                    if (result.reason === SpeechSDK.ResultReason.SynthesizingAudioCompleted) {
                        console.log("Speech and avatar synthesized to video stream.")
                    } else {
                        console.log("Unable to speak. Result ID: " + result.resultId)
                        if (result.reason === SpeechSDK.ResultReason.Canceled) {
                            let cancellationDetails = SpeechSDK.CancellationDetails.fromResult(result)
                            console.log(cancellationDetails.reason)
                            if (cancellationDetails.reason === SpeechSDK.CancellationReason.Error) {
                                console.log(cancellationDetails.errorDetails)
                            }
                        }
                    }
            }).catch((error) => {
                console.log(error)
                avatarSynthesizer.close()
            });
        }
    </script>
</body>
</html>