{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## langgraph - 人机互动\n", "***\n", "- 基本运用：等待用户输入\n", "- 基本运用：审查工具调用\n", "- 基本使用：编辑图的状态\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 等待用户输入或介入\n", "****\n", "在节点与节点间增加人类反馈节点"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from typing_extensions import TypedDict\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.types import Command, interrupt\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from IPython.display import Image, display\n", "\n", "\n", "class State(TypedDict):\n", "    input: str\n", "    user_feedback: str\n", "\n", "\n", "def step_1(state):\n", "    print(\"---Step 1---\")\n", "    pass\n", "\n", "\n", "def human_feedback(state):\n", "    print(\"---human_feedback---\")\n", "    feedback = interrupt(\"Please provide feedback:\")\n", "    return {\"user_feedback\": feedback}\n", "\n", "\n", "def step_3(state):\n", "    print(\"---Step 3---\")\n", "    pass\n", "\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(\"step_1\", step_1)\n", "builder.add_node(\"human_feedback\", human_feedback)\n", "builder.add_node(\"step_3\", step_3)\n", "builder.add_edge(START, \"step_1\")\n", "builder.add_edge(\"step_1\", \"human_feedback\")\n", "builder.add_edge(\"human_feedback\", \"step_3\")\n", "builder.add_edge(\"step_3\", END)\n", "\n", "# Set up memory\n", "memory = MemorySaver()\n", "\n", "# Add\n", "graph = builder.compile(checkpointer=memory)\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---Step 1---\n", "{'step_1': None}\n", "\n", "\n", "---human_feedback---\n", "{'__interrupt__': (Interrupt(value='Please provide feedback:', resumable=True, ns=['human_feedback:90f82932-04be-971f-eea2-d1366a5cfdd1']),)}\n", "\n", "\n"]}], "source": ["# Input\n", "initial_input = {\"input\": \"你好\"}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["添加人类反馈"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---human_feedback---\n", "{'human_feedback': {'user_feedback': 'go to step 3!'}}\n", "\n", "\n", "---Step 3---\n", "{'step_3': None}\n", "\n", "\n"]}], "source": ["# 继续执行\n", "for event in graph.stream(\n", "    Command(resume=\"go to step 3!\"), thread, stream_mode=\"updates\"\n", "):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在智能体中引入人工介入环节"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 设置状态\n", "from langgraph.graph import MessagesState, START\n", "\n", "# 设置工具\n", "# 我们将有一个真实工具 - 搜索工具\n", "# 我们还将有一个\"假\"工具 - \"ask_human\"（询问人类）工具\n", "# 这里我们定义任何实际工具\n", "from langchain_core.tools import tool\n", "from langgraph.prebuilt import ToolNode\n", "\n", "\n", "@tool\n", "def search(query: str):\n", "    \"\"\"调用此函数来浏览网络。\"\"\"\n", "    # 这只是实际实现的占位符\n", "    # 不要让大语言模型知道这一点哦 😊\n", "    return f\"我查询了：{query}。结果：北京天气晴朗，温度25度。\"\n", "\n", "\n", "tools = [search]\n", "tool_node = ToolNode(tools)\n", "\n", "# 设置模型\n", "from langchain_deepseek import ChatDeepSeek\n", "import os\n", "\n", "model = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "from pydantic import BaseModel\n", "\n", "\n", "# 我们将\"绑定\"所有工具到模型\n", "# 我们有上面的实际工具，但我们还需要一个模拟工具来询问人类\n", "# 由于`bind_tools`接受工具但也接受工具定义，\n", "# 我们可以为`ask_human`定义一个工具定义\n", "class AskHuman(BaseModel):\n", "    \"\"\"向人类提问\"\"\"\n", "\n", "    question: str\n", "\n", "\n", "model = model.bind_tools(tools + [AskHuman])\n", "\n", "# 定义节点和条件边\n", "\n", "\n", "# 定义确定是否继续的函数\n", "def should_continue(state):\n", "    messages = state[\"messages\"]\n", "    last_message = messages[-1]\n", "    # 如果没有函数调用，则结束\n", "    if not last_message.tool_calls:\n", "        return END\n", "    # 如果工具调用是询问人类，我们返回该节点\n", "    # 你也可以在这里添加逻辑，让某些系统知道有需要人类输入的内容\n", "    # 例如，发送slack消息等\n", "    elif last_message.tool_calls[0][\"name\"] == \"AskHuman\":\n", "        return \"ask_human\"\n", "    # 否则如果有函数调用，我们继续\n", "    else:\n", "        return \"action\"\n", "\n", "\n", "# 定义调用模型的函数\n", "def call_model(state):\n", "    messages = state[\"messages\"]\n", "    response = model.invoke(messages)\n", "    # 我们返回一个列表，因为这将被添加到现有列表中\n", "    return {\"messages\": [response]}\n", "\n", "\n", "# 我们定义一个假节点来询问人类\n", "def ask_human(state):\n", "    tool_call_id = state[\"messages\"][-1].tool_calls[0][\"id\"]\n", "    ask = AskHuman.model_validate(state[\"messages\"][-1].tool_calls[0][\"args\"])\n", "    location = interrupt(ask.question)\n", "    tool_message = [{\"tool_call_id\": tool_call_id, \"type\": \"tool\", \"content\": location}]\n", "    return {\"messages\": tool_message}\n", "\n", "\n", "# 构建图\n", "\n", "from langgraph.graph import END, StateGraph\n", "\n", "# 定义一个新图\n", "workflow = StateGraph(MessagesState)\n", "\n", "# 定义我们将循环的三个节点\n", "workflow.add_node(\"agent\", call_model)\n", "workflow.add_node(\"action\", tool_node)\n", "workflow.add_node(\"ask_human\", ask_human)\n", "\n", "# 将入口点设置为`agent`\n", "# 这意味着这个节点是第一个被调用的\n", "workflow.add_edge(START, \"agent\")\n", "\n", "# 现在添加一个条件边\n", "workflow.add_conditional_edges(\n", "    # 首先，我们定义起始节点。我们使用`agent`。\n", "    # 这意味着这些是在调用`agent`节点后采取的边。\n", "    \"agent\",\n", "    # 接下来，我们传入将确定下一个调用哪个节点的函数。\n", "    should_continue,\n", ")\n", "\n", "# 现在我们从`tools`到`agent`添加一个普通边。\n", "# 这意味着在调用`tools`之后，下一步调用`agent`节点。\n", "workflow.add_edge(\"action\", \"agent\")\n", "\n", "# 在我们获得人类响应后，我们回到代理\n", "workflow.add_edge(\"ask_human\", \"agent\")\n", "\n", "# 设置内存\n", "from langgraph.checkpoint.memory import MemorySaver\n", "\n", "memory = MemorySaver()\n", "\n", "# 最后，我们编译它！\n", "# 这将它编译成一个LangChain Runnable，\n", "# 意味着你可以像使用任何其他runnable一样使用它\n", "# 我们在`ask_human`节点之前添加一个断点，所以它永远不会执行\n", "app = workflow.compile(checkpointer=memory)\n", "\n", "display(Image(app.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "询问用户他们在哪里，然后查询那里的天气\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  AskHuman (0195e74db433e3a9929cbe262e48703d)\n", " Call ID: 0195e74db433e3a9929cbe262e48703d\n", "  Args:\n", "    question: 你在哪里？我想查询那里的天气。\n"]}], "source": ["config = {\"configurable\": {\"thread_id\": \"2\"}}\n", "for event in app.stream(\n", "    {\n", "        \"messages\": [\n", "            (\n", "                \"user\",\n", "                \"询问用户他们在哪里，然后查询那里的天气\",\n", "            )\n", "        ]\n", "    },\n", "    config,\n", "    stream_mode=\"values\",\n", "):\n", "    event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["可以看到这个流被中断了，并在会一直等待人类的输入，来确认人类的地点"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  AskHuman (0195e74db433e3a9929cbe262e48703d)\n", " Call ID: 0195e74db433e3a9929cbe262e48703d\n", "  Args:\n", "    question: 你在哪里？我想查询那里的天气。\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "\n", "北京\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  search (0195e74dbfa94f07c9c89ad0c7c949be)\n", " Call ID: 0195e74dbfa94f07c9c89ad0c7c949be\n", "  Args:\n", "    query: 北京天气\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: search\n", "\n", "我查询了：北京天气。结果：北京天气晴朗，温度25度。\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "北京目前天气晴朗，温度为25度。\n"]}], "source": ["for event in app.stream(Command(resume=\"北京\"), config, stream_mode=\"values\"):\n", "    event[\"messages\"][-1].pretty_print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 审查工具调用\n", "****\n", "人工介入智能体的工具调用过程，实现人机协同"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from typing_extensions import TypedDict, Literal\n", "from langgraph.graph import StateGraph, START, END, MessagesState\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from langgraph.types import Command, interrupt\n", "from langchain_core.tools import tool\n", "from langchain_core.messages import AIMessage\n", "from IPython.display import Image, display\n", "\n", "\n", "@tool\n", "def weather_search(city: str):\n", "    \"\"\"搜索天气\"\"\"\n", "    print(\"----\")\n", "    print(f\"正在搜索：{city}\")\n", "    print(\"----\")\n", "    return \"晴朗！\"\n", "\n", "\n", "# 设置模型\n", "from langchain_deepseek import ChatDeepSeek\n", "import os\n", "\n", "deepseek = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "model = deepseek.bind_tools(\n", "    [weather_search]\n", ")\n", "\n", "\n", "class State(MessagesState):\n", "    \"\"\"简单状态。\"\"\"\n", "\n", "\n", "def call_llm(state):\n", "    return {\"messages\": [model.invoke(state[\"messages\"])]}\n", "\n", "\n", "def human_review_node(state) -> Command[Literal[\"call_llm\", \"run_tool\"]]:\n", "    last_message = state[\"messages\"][-1]\n", "    tool_call = last_message.tool_calls[-1]\n", "\n", "    # 这是我们将通过Command(resume=<human_review>)提供的值\n", "    human_review = interrupt(\n", "        {\n", "            \"question\": \"这是正确的吗？\",\n", "            # 显示工具调用以供审核\n", "            \"tool_call\": tool_call,\n", "        }\n", "    )\n", "\n", "    review_action = human_review[\"action\"]\n", "    review_data = human_review.get(\"data\")\n", "\n", "    # 如果批准，调用工具\n", "    if review_action == \"continue\":\n", "        return Command(goto=\"run_tool\")\n", "\n", "    # 更新AI消息并调用工具\n", "    elif review_action == \"update\":\n", "        updated_message = {\n", "            \"role\": \"ai\",\n", "            \"content\": last_message.content,\n", "            \"tool_calls\": [\n", "                {\n", "                    \"id\": tool_call[\"id\"],\n", "                    \"name\": tool_call[\"name\"],\n", "                    # 这是人类提供的更新\n", "                    \"args\": review_data,\n", "                }\n", "            ],\n", "            # 这很重要 - 这需要与你替换的消息相同！\n", "            # 否则，它将显示为一个单独的消息\n", "            \"id\": last_message.id,\n", "        }\n", "        return Command(goto=\"run_tool\", update={\"messages\": [updated_message]})\n", "\n", "    # 向LLM提供反馈\n", "    elif review_action == \"feedback\":\n", "        # 注意：我们将反馈消息添加为ToolMessage\n", "        # 以保持消息历史中的正确顺序\n", "        # （带有工具调用的AI消息需要后跟工具调用消息）\n", "        tool_message = {\n", "            \"role\": \"tool\",\n", "            # 这是我们的自然语言反馈\n", "            \"content\": review_data,\n", "            \"name\": tool_call[\"name\"],\n", "            \"tool_call_id\": tool_call[\"id\"],\n", "        }\n", "        return Command(goto=\"call_llm\", update={\"messages\": [tool_message]})\n", "\n", "\n", "def run_tool(state):\n", "    new_messages = []\n", "    tools = {\"weather_search\": weather_search}\n", "    tool_calls = state[\"messages\"][-1].tool_calls\n", "    for tool_call in tool_calls:\n", "        tool = tools[tool_call[\"name\"]]\n", "        result = tool.invoke(tool_call[\"args\"])\n", "        new_messages.append(\n", "            {\n", "                \"role\": \"tool\",\n", "                \"name\": tool_call[\"name\"],\n", "                \"content\": result,\n", "                \"tool_call_id\": tool_call[\"id\"],\n", "            }\n", "        )\n", "    return {\"messages\": new_messages}\n", "\n", "\n", "def route_after_llm(state) -> Literal[END, \"human_review_node\"]:\n", "    if len(state[\"messages\"][-1].tool_calls) == 0:\n", "        return END\n", "    else:\n", "        return \"human_review_node\"\n", "\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(call_llm)\n", "builder.add_node(run_tool)\n", "builder.add_node(human_review_node)\n", "builder.add_edge(START, \"call_llm\")\n", "builder.add_conditional_edges(\"call_llm\", route_after_llm)\n", "builder.add_edge(\"run_tool\", \"call_llm\")\n", "\n", "# 设置内存\n", "memory = MemorySaver()\n", "\n", "# 添加\n", "graph = builder.compile(checkpointer=memory)\n", "\n", "# 查看\n", "display(Image(graph.get_graph().draw_mermaid_png()))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["当不涉及工具调用的时候，不会触发人工审核"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'call_llm': {'messages': [AIMessage(content='你好！有什么可以帮您的吗？😊', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 10, 'prompt_tokens': 72, 'total_tokens': 82, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-d6e21c61-8191-4c11-b445-81c6cf8640ab-0', usage_metadata={'input_tokens': 72, 'output_tokens': 10, 'total_tokens': 82, 'input_token_details': {}, 'output_token_details': {}})]}}\n", "\n", "\n"]}], "source": ["# Input\n", "initial_input = {\"messages\": [{\"role\": \"user\", \"content\": \"你好！\"}]}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "for event in graph.stream(initial_input, thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["一旦涉及到工具调用 就会触发人工介入"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'call_llm': {'messages': [AIMessage(content='', additional_kwargs={'tool_calls': [{'id': '0195e75e635a549ed6975331ce04f284', 'function': {'arguments': '{\"city\":\"北京\"}', 'name': 'weather_search'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 18, 'prompt_tokens': 74, 'total_tokens': 92, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-d8d99608-8d50-4e78-b84f-e51087c5d15d-0', tool_calls=[{'name': 'weather_search', 'args': {'city': '北京'}, 'id': '0195e75e635a549ed6975331ce04f284', 'type': 'tool_call'}], usage_metadata={'input_tokens': 74, 'output_tokens': 18, 'total_tokens': 92, 'input_token_details': {}, 'output_token_details': {}})]}}\n", "\n", "\n", "{'__interrupt__': (Interrupt(value={'question': '这是正确的吗？', 'tool_call': {'name': 'weather_search', 'args': {'city': '北京'}, 'id': '0195e75e635a549ed6975331ce04f284', 'type': 'tool_call'}}, resumable=True, ns=['human_review_node:49b9a360-2842-7a3f-7aec-6f289b289eb4']),)}\n", "\n", "\n"]}], "source": ["# Input\n", "initial_input = {\"messages\": [{\"role\": \"user\", \"content\": \"北京的天气如何?\"}]}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"2\"}}\n", "\n", "for event in graph.stream(initial_input, thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用Command进行人机交互"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'human_review_node': None}\n", "\n", "\n", "----\n", "正在搜索：北京\n", "----\n", "{'run_tool': {'messages': [{'role': 'tool', 'name': 'weather_search', 'content': '晴朗！', 'tool_call_id': '0195e75e635a549ed6975331ce04f284'}]}}\n", "\n", "\n", "{'call_llm': {'messages': [AIMessage(content='北京的天气是晴朗的！', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 6, 'prompt_tokens': 106, 'total_tokens': 112, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-533e1756-b31f-4af0-8535-99bb9828c2e6-0', usage_metadata={'input_tokens': 106, 'output_tokens': 6, 'total_tokens': 112, 'input_token_details': {}, 'output_token_details': {}})]}}\n", "\n", "\n"]}], "source": ["for event in graph.stream(\n", "    # 输入值\n", "    Command(resume={\"action\": \"continue\"}),\n", "    thread,\n", "    stream_mode=\"updates\",\n", "):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["更进一步，对智能体调用的工具进行参数编辑"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'call_llm': {'messages': [AIMessage(content='', additional_kwargs={'tool_calls': [{'id': '0195e763ebf746ef6ead6925e2f4d738', 'function': {'arguments': '{\"city\":\"深圳\"}', 'name': 'weather_search'}, 'type': 'function'}], 'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 18, 'prompt_tokens': 75, 'total_tokens': 93, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'tool_calls', 'logprobs': None}, id='run-0424c502-b71d-46be-8e21-f420381fab70-0', tool_calls=[{'name': 'weather_search', 'args': {'city': '深圳'}, 'id': '0195e763ebf746ef6ead6925e2f4d738', 'type': 'tool_call'}], usage_metadata={'input_tokens': 75, 'output_tokens': 18, 'total_tokens': 93, 'input_token_details': {}, 'output_token_details': {}})]}}\n", "\n", "\n", "{'__interrupt__': (Interrupt(value={'question': '这是正确的吗？', 'tool_call': {'name': 'weather_search', 'args': {'city': '深圳'}, 'id': '0195e763ebf746ef6ead6925e2f4d738', 'type': 'tool_call'}}, resumable=True, ns=['human_review_node:061cb420-e50f-3dab-1cab-719b704216c1']),)}\n", "\n", "\n"]}], "source": ["# Input\n", "initial_input = {\"messages\": [{\"role\": \"user\", \"content\": \"深圳的天气如何?\"}]}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"3\"}}\n", "\n", "for event in graph.stream(initial_input, thread, stream_mode=\"updates\"):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'human_review_node': {'messages': [{'role': 'ai', 'content': '', 'tool_calls': [{'id': '0195e763ebf746ef6ead6925e2f4d738', 'name': 'weather_search', 'args': {'city': '上海,中国'}}], 'id': 'run-0424c502-b71d-46be-8e21-f420381fab70-0'}]}}\n", "\n", "\n", "----\n", "正在搜索：上海,中国\n", "----\n", "{'run_tool': {'messages': [{'role': 'tool', 'name': 'weather_search', 'content': '晴朗！', 'tool_call_id': '0195e763ebf746ef6ead6925e2f4d738'}]}}\n", "\n", "\n", "{'call_llm': {'messages': [AIMessage(content='深圳的天气是晴朗的！', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 7, 'prompt_tokens': 118, 'total_tokens': 125, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'finish_reason': 'stop', 'logprobs': None}, id='run-995428f3-4bb4-4468-a144-2abbd298c62e-0', usage_metadata={'input_tokens': 118, 'output_tokens': 7, 'total_tokens': 125, 'input_token_details': {}, 'output_token_details': {}})]}}\n", "\n", "\n"]}], "source": ["# 直接对工具的参数进行编辑\n", "for event in graph.stream(\n", "    Command(resume={\"action\": \"update\", \"data\": {\"city\": \"上海,中国\"}}),\n", "    thread,\n", "    stream_mode=\"updates\",\n", "):\n", "    print(event)\n", "    print(\"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 人机交互 - 对图状态进行编辑\n", "****\n", "使用断点对节点间的消息进行人工干预\n", "![](langgraph3.png)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from typing_extensions import TypedDict\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.checkpoint.memory import MemorySaver\n", "from IPython.display import Image, display\n", "\n", "\n", "class State(TypedDict):\n", "    input: str\n", "\n", "\n", "def step_1(state):\n", "    print(\"---Step 1---\")\n", "    pass\n", "\n", "\n", "def step_2(state):\n", "    print(\"---Step 2---\")\n", "    pass\n", "\n", "\n", "def step_3(state):\n", "    print(\"---Step 3---\")\n", "    pass\n", "\n", "\n", "builder = StateGraph(State)\n", "builder.add_node(\"step_1\", step_1)\n", "builder.add_node(\"step_2\", step_2)\n", "builder.add_node(\"step_3\", step_3)\n", "builder.add_edge(START, \"step_1\")\n", "builder.add_edge(\"step_1\", \"step_2\")\n", "builder.add_edge(\"step_2\", \"step_3\")\n", "builder.add_edge(\"step_3\", END)\n", "\n", "# Set up memory\n", "memory = MemorySaver()\n", "\n", "# Add 注意interrupt_before\n", "graph = builder.compile(checkpointer=memory, interrupt_before=[\"step_2\"])\n", "\n", "# View\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'input': '你好'}\n", "---Step 1---\n"]}], "source": ["# Input\n", "initial_input = {\"input\": \"你好\"}\n", "\n", "# Thread\n", "thread = {\"configurable\": {\"thread_id\": \"1\"}}\n", "\n", "# Run the graph until the first interruption\n", "for event in graph.stream(initial_input, thread, stream_mode=\"values\"):\n", "    print(event)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["此时可以人工介入"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---\n", "---\n", "Updated state!\n", "{'input': '你好 1goto.ai!'}\n"]}], "source": ["graph.update_state(thread, {\"input\": \"你好 1goto.ai!\"})\n", "print(\"---\\n---\\nUpdated state!\")\n", "print(graph.get_state(thread).values)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'input': '你好 1goto.ai!'}\n", "---Step 2---\n", "---Step 3---\n"]}], "source": ["# 继续执行\n", "for event in graph.stream(None, thread, stream_mode=\"values\"):\n", "    print(event)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}