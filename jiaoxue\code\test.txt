Deepseek R1 是⽀持复杂推理、多模态处理、技术⽂档⽣成的⾼性能通⽤⼤语⾔模型。本⼿册
为技术团队提供完整的本地部署指南，涵盖硬件配置、国产芯⽚适配、量化⽅案、云端替代⽅
案及完整671B MoE模型的Ollama部署⽅法。模型 参数规
模
计算精
度
最低显存需
求 最低算⼒需求
DeepSeek-R1 (671B) 671B FP8 ≥890GB
2*XE9680（16*H20
GPU）
DeepSeek-R1-Distill-
70B
70B BF16 ≥180GB 4*L20 或 2*H20 GPU
三、国产芯⽚与硬件适配⽅案
1. 国内⽣态合作伙伴动态
企业 适配内容 性能对标（vs
NVIDIA）
华为昇
腾
昇腾910B原⽣⽀持R1全系列，提供端到端推理优化
⽅案 等效A100（FP16）
沐曦
GPU
MXN系列⽀持70B模型BF16推理，显存利⽤率提升
30% 等效RTX 3090
海光
DCU 适配V3/R1模型，性能对标NVIDIA A100 等效A100（BF16）
2. 国产硬件推荐配置
模型参数 推荐⽅案 适⽤场景
1.5B 太初T100加速卡 个⼈开发者原型验证
14B 昆仑芯K200集群 企业级复杂任务推理
32B 壁彻算⼒平台+昇腾910B集群 科研计算与多模态处理
12月26日，Deepseek发布了全新系列模型DeepSeek-v3，一夜之间霸榜开源模型，并在性能上和世界顶尖的闭源模型GPT-4o以及 Claude-3.5-Sonnet相提并论。

该模型为MOE架构，大大降低了训练成本，据说训练成本仅600万美元，成本降低10倍，资源运用效率极高。有AI投资机构负责人直言，DeepSeek发布的53页的技术论文是黄金。

那就先让我们看看论文是怎么说的吧，老规矩，先上资源地址：

Github: GitHub - deepseek-ai/DeepSeek-V3

模型地址：https://huggingface.co/deepseek-ai

论文地址：https://github.com/deepseek-ai/DeepSeek-V3/blob/main/DeepSeek_V3.pdf

以下为技术解读：

前言
DeepSeek-AI 发布了其最新的大型语言模型 DeepSeek-V3，这款模型在性能和效率方面都取得了显著的进步，成为当前最强大的开源基础模型之一。DeepSeek-V3 是一款拥有 671B参数的大型混合专家 (MoE) 模型，其中每个 token 会有 37 B参数被激活。


为了实现高效的推理和成本效益的训练，DeepSeek-V3 采用了多头潜在注意力 (MLA) 和 DeepSeekMoE 架构，这两个架构在 DeepSeek-V2 中已经得到了充分验证。此外，DeepSeek-V3 还开创了一种无辅助损失策略来平衡负载，并设置了多 token 预测训练目标以进一步提升性能。


架构：创新负载平衡策略和训练目标
基本架构
DeepSeek-V3 的基本架构仍然基于 Transformer 框架，但其采用了 MLA 和 DeepSeekMoE 架构来实现高效推理和成本效益的训练。


多头潜在注意力 (MLA)

MLA 架构的核心思想是对注意力键和值进行低秩联合压缩，从而减少推理过程中的 Key-Value (KV) 缓存。它通过以下步骤实现：

压缩: 将注意力输入 h_t 映射到一个压缩的潜在向量 c_KV_t。

生成键: 使用 W_UK 和 W_VU 矩阵将 c_KV_t 映射到压缩的键和值。

生成解码器: 使用 RoPE 矩阵生成带有旋转位置嵌入 (RoPE) 的解码器。

计算注意力: 使用 softmax 函数计算注意力权重，并生成最终的注意力输出 u_t。

MLA 架构只需要缓存压缩后的潜在向量和带有 RoPE 的解码器，从而显著减少了 KV 缓存，同时保持了与标准多头注意力 (MHA) 相当的性能。

DeepSeekMoE：辅助损失免费负载平衡
DeepSeekMoE 架构使用更细粒度的专家，并将一些专家隔离为共享专家。每个 token 的 FFN 输出 h’_t 通过以下步骤计算：

共享专家: 使用共享专家 FFN( ) (·) 计算共享专家的输出。

路由专家: 使用路由专家 FFN( ) (·) 计算路由专家的输出，并使用门控值 g_i,t 选择激活的专家。

输出: 将共享专家和路由专家的输出相加，得到最终的 FFN 输出 h’_t。

DeepSeek-V3 还引入了一种辅助损失免费负载平衡策略，通过引入偏置项 b_i 并将其添加到相应的亲和度分数 s_i,t 中，来确定 top-K 路由。通过动态调整偏置项，DeepSeek-V3 能够在整个训练过程中保持平衡的专家负载，并取得比纯粹使用辅助损失的模型更好的性能。

多 token 预测
DeepSeek-V3 采用了一种名为多 token 预测 (MTP) 的训练目标，该目标扩展了预测范围，以便在每个位置预测多个未来的 token。MTP 目标可以提高数据效率和模型的预测能力，并通过预先规划未来的 token 的表示来提升性能。

MTP 实现了 D 个连续的模块来预测 D 个额外的 token，每个模块都包含一个共享嵌入层、一个共享输出头、一个 Transformer 模块和一个投影矩阵。每个 MTP 模块都使用线性投影将 token 的表示和嵌入相连接，然后通过 Transformer 模块生成输出表示，并计算额外的预测 token 的概率分布。

基础设施：高效训练的基石
DeepSeek-V3 的训练过程依赖于高效的计算集群和训练框架。


计算集群
DeepSeek-V3 在一个配备 2048 个 NVIDIA H800 GPU 的集群上进行训练。每个节点包含 8 个 GPU，通过 NVLink 和 NVSwitch 相互连接。跨节点之间使用 InfiniBand (IB) 进行通信。


训练框架
DeepSeek-V3 的训练框架基于 HAI-LLM 框架，该框架为高效训练提供了强大的支持。DeepSeek-V3 应用了 16 路 Pipeline Parallelism (PP)、64 路 Expert Parallelism (EP) 和 ZeRO-1 Data Parallelism (DP)。

双向管道并行 (DualPipe)

为了解决跨节点专家并行导致的通信开销问题，DeepSeek-V3 设计了一种名为 DualPipe 的新型管道并行算法。DualPipe 通过重叠正向和反向计算通信阶段，不仅提高了模型训练速度，还减少了管道气泡的数量。

跨节点全连接通信

DeepSeek-V3 开发了高效的跨节点全连接通信内核，以充分利用 IB 和 NVLink 的带宽，并节省专门用于通信的 Streaming Multiprocessors (SMs)。

极低的内存占用

DeepSeek-V3 通过以下技术来降低训练过程中的内存占用：

RMSNorm 和 MLA 上投影的重新计算: 在反向传播过程中重新计算所有 RMSNorm 操作和 MLA 上投影，从而消除了永久存储其输出激活的需求。

CPU 上的指数移动平均: 在训练过程中保存模型参数的指数移动平均 (EMA)，用于早期估计模型性能，并异步更新 EMA 参数，从而避免额外的内存和时间开销。

多 token 预测中的共享嵌入和输出头: 利用 DualPipe 策略，将模型的最浅层和最深层部署在同一个 PP 路径上，从而实现共享嵌入和输出头的参数和梯度，进一步提高内存效率。

FP8 训练
DeepSeek-V3 支持使用 FP8 数据格式进行混合精度训练，以实现加速训练和降低 GPU 内存使用。


混合精度框架

混合精度框架使用 FP8 格式进行大多数计算密集型操作，而一些关键操作则保留其原始数据格式，以平衡训练效率和数值稳定性。


量化精度提升

为了提高低精度训练的精度，DeepSeek-V3 引入了几种策略：

细粒度量化: 将激活和权重分组并分别进行缩放，以更好地适应异常值。

增加累积精度: 将部分结果复制到 FP32 寄存器中进行全精度累积，以提高精度。

尾数超过指数: 采用 E4M3 格式，即 4 位指数和 3 位尾数，以提高精度。

低精度存储和通信

DeepSeek-V3 通过以下方式进一步降低内存和通信开销：

低精度优化器状态: 使用 BF16 格式跟踪 AdamW 优化器的第一和第二矩。
低精度激活: 使用 FP8 格式缓存 Linear 操作的激活，并对一些关键激活使用 E5M6 格式，或重新计算其输出。
低精度通信: 将激活在 MoE 上投影之前量化为 FP8，并使用调度组件，与 MoE 上投影中的 FP8 Fprop 兼容。
预训练：迈向终极训练效率
DeepSeek-V3 在一个包含 14.8 万亿高质量和多样化 token 的语料库上进行预训练。预训练过程非常稳定，没有遇到不可恢复的损失峰值或需要回滚的情况。

数据构建
预训练语料库经过优化，数学和编程样本的比例更高，并扩展了多语言覆盖范围，包括英语和中文。数据处理流程也得到了改进，以减少冗余并保持语料库的多样性。

超参数设置
DeepSeek-V3 的超参数包括 Transformer 层数、隐藏维度、注意力头数、每头维度、KV 压缩维度、查询压缩维度、RoPE 维度、MoE 层数、共享专家数量、路由专家数量、中间隐藏维度、激活专家数量、节点限制路由数量、多 token 预测深度、学习率、批大小等。


长上下文扩展
DeepSeek-V3 采用与 DeepSeek-V2 相似的方法来启用长上下文功能。在预训练阶段之后，应用 YaRN 进行上下文扩展，并进行两个额外的训练阶段，将上下文窗口逐步扩展到 32K 和 128K。

评估
DeepSeek-V3 在一系列基准测试中进行了评估，包括多学科多项选择题、语言理解和推理、闭卷问答、阅读理解、参考消歧、语言模型、中文理解和文化、数学、代码和标准化考试等。DeepSeek-V3 在大多数基准测试中都取得了最强大的性能，尤其是在数学和代码任务上。


讨论
DeepSeek-V3 中的 MTP 策略和多 token 预测策略都取得了显著的性能提升。辅助损失免费负载平衡策略也取得了更好的性能，并且专家具有更强的专业模式。与序列级辅助损失相比，批量级负载平衡方法也表现出一致的效率优势，但其也面临着潜在的挑战，例如序列或小批量中的负载不平衡以及推理过程中域转换引起的负载不平衡。



后训练：知识蒸馏与强化学习
DeepSeek-V3 通过监督微调和强化学习进行后训练，以使其与人类偏好保持一致并进一步释放其潜力。

监督微调（Supervised Fine-Tuning ）
DeepSeek-V3 使用一个包含 150 万个实例的数据集进行监督微调，涵盖了多个领域。对于推理相关的数据集，例如数学、代码竞赛问题和逻辑谜题，使用内部 DeepSeek-R1 模型生成数据。对于非推理数据，例如创意写作、角色扮演和简单问答，使用 DeepSeek-V2.5 生成。并通过拒绝抽样方法筛选高质量数据，以确保最终训练数据的准确性和简洁性。

SFT 设置：DeepSeek-V3 使用余弦退火学习率调度进行两个 epoch 的训练，初始学习率为 5 × 10^-6，并逐渐降低到 1 × 10^-6。在训练过程中，每个序列由多个样本打包，并使用样本掩码策略确保这些示例保持隔离并相互不可见。

强化学习
DeepSeek-V3 采用基于规则的奖励模型 (RM) 和基于模型的 RM 来确定模型的反馈。对于可以验证的特定规则的问题，使用基于规则的奖励系统来确定反馈。对于具有自由格式真实答案的问题，使用奖励模型来确定答案是否与预期的真实答案匹配。对于没有明确真实答案的问题，奖励模型负责根据问题和答案提供反馈。

DeepSeek-V3 使用组相对策略优化 (GRPO) 进行强化学习，该优化方法放弃了与策略模型相同大小的评论模型，而是从组分数中估计基线。在 RL 过程中，模型使用高温采样生成包含来自 DeepSeek-R1 生成数据和原始数据的模式的响应，即使在缺乏明确系统提示的情况下也能做到。

评估
DeepSeek-V3 在一系列基准测试中进行了评估，包括 IFEval、FRAMES、LongBench v2、GPQA、SimpleQA、C-SimpleQA、SWE-Bench Verified、Aider 1、LiveCodeBench、Codeforces、中国高中数学奥林匹克 (CNMO) 2024 和美国邀请数学考试 (AIME) 2024 等。DeepSeek-V3 在大多数基准测试中都取得了最强大的性能，尤其是在代码、数学和长上下文理解任务上。




讨论
DeepSeek-V3 从 DeepSeek-R1 系列模型中蒸馏推理能力取得了成功，显著提高了其在数学和代码基准测试中的性能。同时，DeepSeek-V3 还采用了宪法 AI 方法，利用 DeepSeek-V3 自身的投票评估结果作为反馈来源，进一步提高了其在主观评估中的性能。

DeepSeek-V3 中的多 token 预测技术可以显著加速模型的解码速度，而额外的预测 token 的接受率在 85% 到 90% 之间，这表明其具有高度的可靠性。

结论、局限性和未来方向
DeepSeek-V3 是一款性能强大且成本效益高的开源大型语言模型，它在推理和生成任务中都取得了显著的成果。DeepSeek-V3 的训练成本非常低，只需 2.788M H800 GPU 小时即可完成其全部训练，包括预训练、上下文长度扩展和后训练。

尽管 DeepSeek-V3 在性能和效率方面取得了显著成果，但它仍然存在一些局限性，尤其是在部署方面。DeepSeek-V3 的推荐部署单元相对较大，这可能对小型团队构成负担。此外，尽管 DeepSeek-V3 的部署策略已经实现了比 DeepSeek-V2 高两倍的端到端生成速度，但仍然存在进一步提升的空间。

DeepSeek-V3 开发了创新的负载平衡策略和训练目标，以实现高效训练。它还引入了 FP8 训练和一系列高效的工程优化措施，以进一步降低训练成本。
DeepSeek-V3 还在后训练阶段取得了成功，通过知识蒸馏和强化学习技术，显著提高了其在数学和代码基准测试中的性能。
DeepSeek-V3 在一系列基准测试中取得了最强大的性能，尤其是在数学、代码和长上下文理解任务上。
DeepSeek-V3 的局限性主要在于部署方面，包括较大的部署单元和潜在的性能提升空间。
DeepSeek-V3 采用了宪法 AI （constitutional AI） 方法，利用 DeepSeek-V3 自身的投票评估结果作为反馈来源，进一步提高了其在主观评估中的性能。
DeepSeek-V3 中的多 token 预测技术可以显著加速模型的解码速度，而额外的预测 token 的接受率在 85% 到 90% 之间，这表明其具有高度的可靠性。
DeepSeek 持续致力于开源模型的道路，并计划在未来进行以下方面的研究：

进一步改进模型架构，以提高训练和推理效率，并尝试突破 Transformer 架构的限制。
持续迭代训练数据的质量和数量，并探索其他训练信号来源，以推动数据扩展到更广泛的维度。
持续探索和迭代模型的深度思考能力，以增强其智能和问题解决能力，并扩展其推理长度和深度。
探索更全面和多维度的模型评估方法，以防止在研究过程中优化固定的一组基准测试，从而产生对模型能力的误导印象并影响我们的基础评估。
DeepSeek-V3 的发布标志着开源大型语言模型领域的一个重大里程碑，并为未来的研究和应用开辟了新的可能性。

简单测试
DeepSeek-V3开源模型，我肯定是没有资源部署了，所以只能通过它的服务网站进行测试了。

地址：DeepSeek


算一下星舰从地球到火星的飞行时间：


让它分析一下自己的技术文档：




最后让它比较了一下自己与GPT-4o-0513


... 略...


——完——

@北方的郎 · 专注模型与代码

概述
前置准备
1. 申请 DeepSeek API
2. 注册 Dify
集成步骤
1. 将 DeepSeek 接入至 Dify
2. 搭建 DeepSeek AI 应用
3. 为 AI 应用启用文本分析能力
4. 分享 AI 应用
阅读更多
Edit on GitHub



阅读更多
应用案例
DeepSeek 与 Dify 集成指南：打造具备多轮思考的 AI 应用
概述
DeepSeek 作为具备多轮推理能力的开源大语言模型，以高性能、低成本、易部署的特性成为智能应用开发的理想基座。通过其 API 服务，开发者可快速调用 DeepSeek 的复杂逻辑推理与内容生成能力。在传统开发模式下，构建生产级 AI 应用往往需要独立完成模型适配、接口开发、交互设计等环节。

Dify 作为同样开源的生成式 AI 应用开发平台，能够帮助开发者基于 DeepSeek 大模型快速开发出更加智能的 AI 应用，你可以在 Dify 平台内获得以下开发体验：

可视化构建 - 通过可视化编排界面，3 分钟搭建基于 DeepSeek R1 的 AI 应用

知识库增强 - 关联内部文档，开启 RAG 能力并构建精准问答系统

工作流扩展 - 提供多种第三方工具插件、可视化拖拽式编排应用功能节点，实现复杂业务逻辑

数据洞察力 - 内置总对话数、应用使用用户数等数据监控模块，支持与更加专业的监控平台集成 ...

本文将详解 DeepSeek API 与 Dify 的集成步骤，助你快速实现两大核心场景：

智能对话机器人开发 - 直接调用 DeepSeek R1 的思维链推理能力

知识增强型应用构建 - 通过私有知识库实现精准信息检索与生成

针对金融、法律等高合规需求场景，Dify 提供 本地私有化部署 DeepSeek + Dify，支持 DeepSeek 模型与 Dify 平台同步部署至内网

通过 Dify × DeepSeek 的技术组合，开发者可跳过底层架构搭建，跃迁至场景化 AI 能力落地阶段，让大模型技术快速转化为业务生产力。

前置准备
1. 申请 DeepSeek API
访问 DeepSeek API 开放平台，按照页面提示进行申请 API Key。

若提示链接无法访问，你也可以考虑在本地部署 DeepSeek 模型。详细说明请参考 本地部署指南

2. 注册 Dify
Dify 是一个能够帮助你快速搭建生成式 AI 应用的平台，通过接入 DeepSeek API，你可以快速搭建出一个能够易于使用的 DeepSeek AI 应用。

集成步骤
1. 将 DeepSeek 接入至 Dify
访问 Dify 平台，点击右上角头像 → 设置 → 模型供应商，找到 DeepSeek，将上文获取的 API Key 粘贴至其中。点击保存，校验通过后将出现成功提示。


2. 搭建 DeepSeek AI 应用
轻点 Dify 平台首页左侧的"创建空白应用"，选择"聊天助手"类型应用并进行简单的命名。


选择 deepseek-reasoner 模型

deepseek-reasoner 模型又称为 deepseek-r1 模型。


配置完成后即可在聊天框中进行互动。


3. 为 AI 应用启用文本分析能力
RAG（检索增强生成）是一种先进的信息处理技术，它通过检索相关知识，向 LLM 提供必要的上下文信息，融入 LLM 的内容生成过程，提升回答的准确性和专业度。当你上传内部文档或专业资料后，AI 能够基于这些知识提供更有针对性的解答。

3.1 创建知识库
将需要 AI 分析处理的文档上传至知识库中。为确保 DeepSeek 模型能够准确理解文档内容，建议使用"父子分段"模式进行文本处理 - 这种模式能够更好地保留文档的层级结构和上下文关系。如需了解详细的配置步骤，请参考：创建知识库。


3.2 将知识库集成至 AI 应用
在 AI 应用的"上下文"内添加知识库，在对话框内输入相关问题。LLM 将首先从知识库内获取与问题相关上下文，在此基础上进行总结并给出更高质量的回答。


4. 分享 AI 应用
构建完成后，你可以将该 AI 应用分享给他人使用或集成至其它网站内。


阅读更多
除了构建简单的 AI 应用外，你还可以创建 Chatflow / Workflow 搭建更多复杂功能的应用（例如具备文件识别、图像识别、语音识别等能力）。详细说明请参考以下文档：