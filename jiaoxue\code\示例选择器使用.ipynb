{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 示例选择器使用\n", "****\n", "-  根据长度动态选择\n", "-  根据语义相似度动态选择\n", "-  使用最大边际相关性进行选择"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 根据长度动态选择\n", "*****\n", "- 根据用户的输入、提示词总长度来动态计算可容纳的示例个数"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# 根据输入的提示词长度综合计算最终长度，智能截取或者添加提示词的示例\n", "# Example of intelligently intercepting or adding prompts by calculating the final length based on the length of the input prompts.\n", "from langchain_core.example_selectors import LengthBasedExampleSelector\n", "from langchain_core.prompts import FewShotPromptTemplate, PromptTemplate\n", "\n", "#假设已经有这么多的提示词示例组：\n", "# Suppose there are so many prompt examples:\n", "examples = [\n", "    {\"input\":\"happy\",\"output\":\"sad\"},\n", "    {\"input\":\"tall\",\"output\":\"short\"},\n", "    {\"input\":\"sunny\",\"output\":\"gloomy\"},\n", "    {\"input\":\"windy\",\"output\":\"calm\"},\n", "    {\"input\":\"高兴\",\"output\":\"悲伤\"}\n", "]\n", "\n", "#构造提示词模板\n", "# Construct prompt template\n", "example_prompt = PromptTemplate(\n", "    input_variables=[\"input\",\"output\"],\n", "    template=\"原词：{input}\\n反义：{output}\"\n", ")\n", "\n", "#调用长度示例选择器\n", "# Call the length example selector\n", "example_selector = LengthBasedExampleSelector(\n", "    #传入提示词示例组\n", "    # Pass in the prompt example group\n", "    examples=examples,\n", "    #传入提示词模板\n", "    example_prompt=example_prompt,\n", "    #设置格式化后的提示词最大长度\n", "    # Set the maximum length of the formatted prompt\n", "    max_length=25,\n", "    #内置的get_text_length,如果默认分词计算方式不满足，可以自己扩展\n", "    # Built-in get_text_length, if the default word segmentation calculation method does not meet the requirements, you can expand it yourself\n", "    #get_text_length:Callable[[str],int] = lambda x:len(re.split(\"\\n| \",x))\n", ")\n", "\n", "#使用小样本提示词模版来实现动态示例的调用\n", "# Use the small sample prompt template to realize the call of dynamic examples\n", "dynamic_prompt = FewShotPromptTemplate(\n", "    example_selector=example_selector,\n", "    example_prompt=example_prompt,\n", "    prefix=\"给出每个输入词的反义词\",\n", "    suffix=\"原词：{adjective}\\n反义：\",\n", "    input_variables=[\"adjective\"]\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["input_variables=['adjective'] input_types={} partial_variables={} example_selector=LengthBasedExampleSelector(examples=[{'input': 'happy', 'output': 'sad'}, {'input': 'tall', 'output': 'short'}, {'input': 'sunny', 'output': 'gloomy'}, {'input': 'windy', 'output': 'calm'}, {'input': '高兴', 'output': '悲伤'}], example_prompt=PromptTemplate(input_variables=['input', 'output'], input_types={}, partial_variables={}, template='原词：{input}\\n反义：{output}'), get_text_length=<function _get_length_based at 0x1287440e0>, max_length=25, example_text_lengths=[2, 2, 2, 2, 2]) example_prompt=PromptTemplate(input_variables=['input', 'output'], input_types={}, partial_variables={}, template='原词：{input}\\n反义：{output}') suffix='原词：{adjective}\\n反义：' prefix='给出每个输入词的反义词'\n"]}], "source": ["print(dynamic_prompt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#小样本获得所有示例,这样可以有效减少输入的提示词长度\n", "# Small sample to get all examples, which can effectively reduce the length of the input prompts\n", "print(dynamic_prompt.format(adjective=\"big\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#如果输入长度很长，则最终输出会根据长度要求减少\n", "# If the input length is very long, the final output will be reduced according to the length requirements\n", "long_string = \"big and huge adn massive and large and gigantic and tall and much much much much much much bigger then everyone\"\n", "print(dynamic_prompt.format(adjective=long_string))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 根据输入的语义相似度动态选择\n", "*****\n", "- 筛选示例组中与输入的语义相似度最高的示例\n", "- 本质：将问题与示例嵌入向量空间后进行搜索比对\n", "- 依赖：向量数据库"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](flow.png)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: langchain_community in ./.venv/lib/python3.13/site-packages (0.3.18)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.37 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.3.37)\n", "Requirement already satisfied: langchain<1.0.0,>=0.3.19 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.3.19)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in ./.venv/lib/python3.13/site-packages (from langchain_community) (2.0.38)\n", "Requirement already satisfied: requests<3,>=2 in ./.venv/lib/python3.13/site-packages (from langchain_community) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in ./.venv/lib/python3.13/site-packages (from langchain_community) (6.0.2)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in ./.venv/lib/python3.13/site-packages (from langchain_community) (3.11.12)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in ./.venv/lib/python3.13/site-packages (from langchain_community) (9.0.0)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.6.7)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in ./.venv/lib/python3.13/site-packages (from langchain_community) (2.8.0)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.3.10)\n", "Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in ./.venv/lib/python3.13/site-packages (from langchain_community) (0.4.0)\n", "Requirement already satisfied: numpy<3,>=1.26.2 in ./.venv/lib/python3.13/site-packages (from langchain_community) (2.2.3)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (2.4.6)\n", "Requirement already satisfied: aiosignal>=1.1.2 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (25.1.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (6.1.0)\n", "Requirement already satisfied: propcache>=0.2.0 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (0.3.0)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in ./.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain_community) (1.18.3)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in ./.venv/lib/python3.13/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in ./.venv/lib/python3.13/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain_community) (0.9.0)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.6 in ./.venv/lib/python3.13/site-packages (from langchain<1.0.0,>=0.3.19->langchain_community) (0.3.6)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in ./.venv/lib/python3.13/site-packages (from langchain<1.0.0,>=0.3.19->langchain_community) (2.10.6)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.37->langchain_community) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.37->langchain_community) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in ./.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.37->langchain_community) (4.12.2)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in ./.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.125->langchain_community) (0.23.0)\n", "Requirement already satisfied: python-dotenv>=0.21.0 in ./.venv/lib/python3.13/site-packages (from pydantic-settings<3.0.0,>=2.4.0->langchain_community) (1.0.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (2.3.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in ./.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain_community) (2025.1.31)\n", "Requirement already satisfied: anyio in ./.venv/lib/python3.13/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (4.8.0)\n", "Requirement already satisfied: httpcore==1.* in ./.venv/lib/python3.13/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in ./.venv/lib/python3.13/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in ./.venv/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.37->langchain_community) (3.0.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.19->langchain_community) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain<1.0.0,>=0.3.19->langchain_community) (2.27.2)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in ./.venv/lib/python3.13/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain_community) (1.0.0)\n", "Requirement already satisfied: sniffio>=1.1 in ./.venv/lib/python3.13/site-packages (from anyio->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain_community) (1.3.1)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install langchain_community -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# 使用最大余弦相似度来检索相关示例，以使示例尽量符合输入\n", "# Use the maximum cosine similarity to retrieve relevant examples to make the examples as close as possible to the input\n", "\n", "from langchain_community.vectorstores import Chroma\n", "from langchain_core.example_selectors import SemanticSimilarityExampleSelector\n", "from langchain_core.prompts import FewShotPromptTemplate, PromptTemplate\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "import os\n", "api_base = os.getenv(\"OPENAI_API_BASE\")\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "\n", "example_prompt = PromptTemplate(\n", "    input_variables=[\"input\", \"output\"],\n", "    template=\"原词: {input}\\n反义: {output}\",\n", ")\n", "\n", "#  一组示例包含各种性质的反义词\n", "# Examples of a pretend task of creating antonyms.\n", "examples = [\n", "    {\"input\": \"happy\", \"output\": \"sad\"},\n", "    {\"input\": \"tall\", \"output\": \"short\"},\n", "    {\"input\": \"energetic\", \"output\": \"lethargic\"},\n", "    {\"input\": \"sunny\", \"output\": \"gloomy\"},\n", "    {\"input\": \"windy\", \"output\": \"calm\"},\n", "    {\"input\":\"高兴\",\"output\":\"悲伤\"}\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 选择chromadb向量数据库进行向量化和向量比较"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: chromadb==0.4.15 in ./.venv/lib/python3.13/site-packages (0.4.15)\n", "Requirement already satisfied: requests>=2.28 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (2.32.3)\n", "Requirement already satisfied: pydantic>=1.9 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (2.10.6)\n", "Requirement already satisfied: chroma-hnswlib==0.7.3 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (0.7.3)\n", "Requirement already satisfied: fastapi>=0.95.2 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (0.115.8)\n", "Requirement already satisfied: uvicorn>=0.18.3 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb==0.4.15) (0.34.0)\n", "Requirement already satisfied: posthog>=2.4.0 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (3.15.0)\n", "Requirement already satisfied: typing-extensions>=4.5.0 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (4.12.2)\n", "Requirement already satisfied: pulsar-client>=3.1.0 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (3.6.0)\n", "Requirement already satisfied: onnxruntime>=1.14.1 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (1.20.1)\n", "Requirement already satisfied: opentelemetry-api>=1.2.0 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (1.30.0)\n", "Requirement already satisfied: opentelemetry-exporter-otlp-proto-grpc>=1.2.0 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (1.30.0)\n", "Requirement already satisfied: opentelemetry-sdk>=1.2.0 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (1.30.0)\n", "Requirement already satisfied: tokenizers>=0.13.2 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (0.21.0)\n", "Requirement already satisfied: pypika>=0.48.9 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (0.48.9)\n", "Requirement already satisfied: tqdm>=4.65.0 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (4.67.1)\n", "Requirement already satisfied: overrides>=7.3.1 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (7.7.0)\n", "Requirement already satisfied: importlib-resources in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (6.5.2)\n", "Requirement already satisfied: grpcio>=1.58.0 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (1.70.0)\n", "Requirement already satisfied: bcrypt>=4.0.1 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (4.2.1)\n", "Requirement already satisfied: typer>=0.9.0 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (0.15.1)\n", "Requirement already satisfied: kubernetes>=28.1.0 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (32.0.1)\n", "Requirement already satisfied: tenacity>=8.2.3 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (9.0.0)\n", "Requirement already satisfied: numpy>=1.22.5 in ./.venv/lib/python3.13/site-packages (from chromadb==0.4.15) (2.2.3)\n", "Requirement already satisfied: starlette<0.46.0,>=0.40.0 in ./.venv/lib/python3.13/site-packages (from fastapi>=0.95.2->chromadb==0.4.15) (0.45.3)\n", "Requirement already satisfied: certifi>=14.05.14 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb==0.4.15) (2025.1.31)\n", "Requirement already satisfied: six>=1.9.0 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb==0.4.15) (1.17.0)\n", "Requirement already satisfied: python-dateutil>=2.5.3 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb==0.4.15) (2.9.0.post0)\n", "Requirement already satisfied: pyyaml>=5.4.1 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb==0.4.15) (6.0.2)\n", "Requirement already satisfied: google-auth>=1.0.1 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb==0.4.15) (2.38.0)\n", "Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb==0.4.15) (1.8.0)\n", "Requirement already satisfied: requests-oauthlib in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb==0.4.15) (2.0.0)\n", "Requirement already satisfied: oauthlib>=3.2.2 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb==0.4.15) (3.2.2)\n", "Requirement already satisfied: urllib3>=1.24.2 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb==0.4.15) (2.3.0)\n", "Requirement already satisfied: durationpy>=0.7 in ./.venv/lib/python3.13/site-packages (from kubernetes>=28.1.0->chromadb==0.4.15) (0.9)\n", "Requirement already satisfied: coloredlogs in ./.venv/lib/python3.13/site-packages (from onnxruntime>=1.14.1->chromadb==0.4.15) (15.0.1)\n", "Requirement already satisfied: flatbuffers in ./.venv/lib/python3.13/site-packages (from onnxruntime>=1.14.1->chromadb==0.4.15) (25.2.10)\n", "Requirement already satisfied: packaging in ./.venv/lib/python3.13/site-packages (from onnxruntime>=1.14.1->chromadb==0.4.15) (24.2)\n", "Requirement already satisfied: protobuf in ./.venv/lib/python3.13/site-packages (from onnxruntime>=1.14.1->chromadb==0.4.15) (5.29.3)\n", "Requirement already satisfied: sympy in ./.venv/lib/python3.13/site-packages (from onnxruntime>=1.14.1->chromadb==0.4.15) (1.13.3)\n", "Requirement already satisfied: deprecated>=1.2.6 in ./.venv/lib/python3.13/site-packages (from opentelemetry-api>=1.2.0->chromadb==0.4.15) (1.2.18)\n", "Requirement already satisfied: importlib-metadata<=8.5.0,>=6.0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-api>=1.2.0->chromadb==0.4.15) (8.5.0)\n", "Requirement already satisfied: googleapis-common-protos~=1.52 in ./.venv/lib/python3.13/site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb==0.4.15) (1.68.0)\n", "Requirement already satisfied: opentelemetry-exporter-otlp-proto-common==1.30.0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb==0.4.15) (1.30.0)\n", "Requirement already satisfied: opentelemetry-proto==1.30.0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb==0.4.15) (1.30.0)\n", "Requirement already satisfied: opentelemetry-semantic-conventions==0.51b0 in ./.venv/lib/python3.13/site-packages (from opentelemetry-sdk>=1.2.0->chromadb==0.4.15) (0.51b0)\n", "Requirement already satisfied: monotonic>=1.5 in ./.venv/lib/python3.13/site-packages (from posthog>=2.4.0->chromadb==0.4.15) (1.6)\n", "Requirement already satisfied: backoff>=1.10.0 in ./.venv/lib/python3.13/site-packages (from posthog>=2.4.0->chromadb==0.4.15) (2.2.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.13/site-packages (from pydantic>=1.9->chromadb==0.4.15) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.13/site-packages (from pydantic>=1.9->chromadb==0.4.15) (2.27.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.13/site-packages (from requests>=2.28->chromadb==0.4.15) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in ./.venv/lib/python3.13/site-packages (from requests>=2.28->chromadb==0.4.15) (3.10)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.16.4 in ./.venv/lib/python3.13/site-packages (from tokenizers>=0.13.2->chromadb==0.4.15) (0.29.1)\n", "Requirement already satisfied: click>=8.0.0 in ./.venv/lib/python3.13/site-packages (from typer>=0.9.0->chromadb==0.4.15) (8.1.8)\n", "Requirement already satisfied: shellingham>=1.3.0 in ./.venv/lib/python3.13/site-packages (from typer>=0.9.0->chromadb==0.4.15) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in ./.venv/lib/python3.13/site-packages (from typer>=0.9.0->chromadb==0.4.15) (13.9.4)\n", "Requirement already satisfied: h11>=0.8 in ./.venv/lib/python3.13/site-packages (from uvicorn>=0.18.3->uvicorn[standard]>=0.18.3->chromadb==0.4.15) (0.14.0)\n", "Requirement already satisfied: httptools>=0.6.3 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb==0.4.15) (0.6.4)\n", "Requirement already satisfied: python-dotenv>=0.13 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb==0.4.15) (1.0.1)\n", "Requirement already satisfied: uvloop!=0.15.0,!=0.15.1,>=0.14.0 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb==0.4.15) (0.21.0)\n", "Requirement already satisfied: watchfiles>=0.13 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb==0.4.15) (1.0.4)\n", "Requirement already satisfied: websockets>=10.4 in ./.venv/lib/python3.13/site-packages (from uvicorn[standard]>=0.18.3->chromadb==0.4.15) (15.0)\n", "Requirement already satisfied: wrapt<2,>=1.10 in ./.venv/lib/python3.13/site-packages (from deprecated>=1.2.6->opentelemetry-api>=1.2.0->chromadb==0.4.15) (1.17.2)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in ./.venv/lib/python3.13/site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb==0.4.15) (5.5.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in ./.venv/lib/python3.13/site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb==0.4.15) (0.4.1)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in ./.venv/lib/python3.13/site-packages (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb==0.4.15) (4.9)\n", "Requirement already satisfied: filelock in ./.venv/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb==0.4.15) (3.17.0)\n", "Requirement already satisfied: fsspec>=2023.5.0 in ./.venv/lib/python3.13/site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb==0.4.15) (2025.2.0)\n", "Requirement already satisfied: zipp>=3.20 in ./.venv/lib/python3.13/site-packages (from importlib-metadata<=8.5.0,>=6.0->opentelemetry-api>=1.2.0->chromadb==0.4.15) (3.21.0)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in ./.venv/lib/python3.13/site-packages (from rich>=10.11.0->typer>=0.9.0->chromadb==0.4.15) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in ./.venv/lib/python3.13/site-packages (from rich>=10.11.0->typer>=0.9.0->chromadb==0.4.15) (2.19.1)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in ./.venv/lib/python3.13/site-packages (from starlette<0.46.0,>=0.40.0->fastapi>=0.95.2->chromadb==0.4.15) (4.8.0)\n", "Requirement already satisfied: humanfriendly>=9.1 in ./.venv/lib/python3.13/site-packages (from coloredlogs->onnxruntime>=1.14.1->chromadb==0.4.15) (10.0)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in ./.venv/lib/python3.13/site-packages (from sympy->onnxruntime>=1.14.1->chromadb==0.4.15) (1.3.0)\n", "Requirement already satisfied: sniffio>=1.1 in ./.venv/lib/python3.13/site-packages (from anyio<5,>=3.6.2->starlette<0.46.0,>=0.40.0->fastapi>=0.95.2->chromadb==0.4.15) (1.3.1)\n", "Requirement already satisfied: mdurl~=0.1 in ./.venv/lib/python3.13/site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer>=0.9.0->chromadb==0.4.15) (0.1.2)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in ./.venv/lib/python3.13/site-packages (from pyasn1-modules>=0.2.1->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb==0.4.15) (0.6.1)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install chromadb==0.4.15 -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["example_selector = SemanticSimilarityExampleSelector.from_examples(\n", "    # 传入示例组.\n", "    # Pass in the example group.\n", "    examples,\n", "    # 使用openAI嵌入来做相似性搜索\n", "    # Use openAI embeddings for similarity search\n", "    OpenAIEmbeddings(openai_api_key=api_key,openai_api_base=api_base),\n", "    # 使用Chroma向量数据库来实现对相似结果的过程存储\n", "    # Use the Chroma vector database to implement the process storage of similar results\n", "    Chroma,\n", "    # 结果条数\n", "    # Number of results\n", "    k=2,\n", ")\n", "\n", "#使用小样本提示词模板\n", "similar_prompt = FewShotPromptTemplate(\n", "    # 传入选择器和模板以及前缀后缀和输入变量\n", "    # Pass in the selector and template, as well as the prefix and suffix and input variables\n", "    example_selector=example_selector,\n", "    example_prompt=example_prompt,\n", "    prefix=\"给出每个输入词的反义词\",\n", "    suffix=\"原词: {adjective}\\n反义:\",\n", "    input_variables=[\"adjective\"],\n", ")\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["给出每个输入词的反义词\n", "\n", "原词: 高兴\n", "反义: 悲伤\n", "\n", "原词: happy\n", "反义: sad\n", "\n", "原词: 难过\n", "反义:\n"]}], "source": ["# 输入一个形容感觉的词语，应该查找近似的 happy/sad 示例\n", "print(similar_prompt.format(adjective=\"难过\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 使用最大边际相关性动态选择示例(MMR)\n", "*****\n", "- 筛选示例组中符合MMR规则的示例\n", "- 本质：将问题与示例嵌入向量空间后进行搜索比对\n", "- 依赖：向量数据库\n", "- MMR: 是一种在信息检索中常用的方法，它的目标是在相关性和多样性之间找到一个平衡。MMR会首先找出与输入最相似（即余弦相似度最大）的样本。然后在迭代添加样本的过程中，对于与已选择样本过于接近（即相似度过高）的样本进行惩罚。MMR既能确保选出的样本与输入高度相关，又能保证选出的样本之间有足够的多样性。关注如何在相关性和多样性之间找到一个平衡。"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["#使用MMR来检索相关示例，以使示例尽量符合输入\n", "# Use MMR to retrieve relevant examples to make the examples as close as possible to the input\n", "\n", "import os\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_core.example_selectors import (\n", "    MaxMarginalRelevanceExampleSelector,\n", ")\n", "from langchain_core.prompts import FewShotPromptTemplate, PromptTemplate\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "api_base = os.getenv(\"OPENAI_API_BASE\")\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "#假设已经有这么多的提示词示例组：\n", "# Suppose there are so many prompt examples:\n", "examples = [\n", "    {\"input\":\"happy\",\"output\":\"sad\"},\n", "    {\"input\":\"tall\",\"output\":\"short\"},\n", "    {\"input\":\"sunny\",\"output\":\"gloomy\"},\n", "    {\"input\":\"windy\",\"output\":\"calm\"},\n", "    {\"input\":\"高兴\",\"output\":\"悲伤\"}\n", "]\n", "\n", "#构造提示词模版\n", "# Construct prompt template\n", "example_prompt = PromptTemplate(\n", "    input_variables=[\"input\",\"output\"],\n", "    template=\"原词：{input}\\n反义：{output}\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 依赖FAISS向量数据库能力，需要安装"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: faiss-cpu in ./.venv/lib/python3.13/site-packages (1.10.0)\n", "Requirement already satisfied: numpy<3.0,>=1.25.0 in ./.venv/lib/python3.13/site-packages (from faiss-cpu) (2.2.3)\n", "Requirement already satisfied: packaging in ./.venv/lib/python3.13/site-packages (from faiss-cpu) (24.2)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.3.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install faiss-cpu -i https://pypi.tuna.tsinghua.edu.cn/simple"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["#调用MMR\n", "# Call MMR\n", "example_selector = MaxMarginalRelevanceExampleSelector.from_examples(\n", "    #传入示例组\n", "    # Pass in the example group\n", "    examples,\n", "    #使用openai的嵌入来做相似性搜索\n", "    # Use openai's embedding for similarity search\n", "    OpenAIEmbeddings(openai_api_base=api_base,openai_api_key=api_key),\n", "    #设置使用的向量数据库是什么\n", "    # Set what vector database is used\n", "    FAISS,\n", "    #结果条数\n", "    # Number of results\n", "    k=2,\n", ")\n", "\n", "#使用小样本模版\n", "mmr_prompt = FewShotPromptTemplate(\n", "    example_selector=example_selector,\n", "    example_prompt=example_prompt,\n", "    prefix=\"给出每个输入词的反义词\",\n", "    suffix=\"原词：{adjective}\\n反义：\",\n", "    input_variables=[\"adjective\"]\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["给出每个输入词的反义词\n", "\n", "原词：高兴\n", "反义：悲伤\n", "\n", "原词：tall\n", "反义：short\n", "\n", "原词：难过\n", "反义：\n"]}], "source": ["#当我们输入一个描述情绪的词语的时候，应该选择同样是描述情绪的一对示例组来填充提示词模版\n", "# When we enter a word describing emotions, we should choose a pair of examples that also describe emotions to fill the prompt template\n", "print(mmr_prompt.format(adjective=\"难过\"))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}