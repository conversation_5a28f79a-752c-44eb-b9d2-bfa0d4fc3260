"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var _Riza_instances, _a, _Riza_baseURLOverridden;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UnprocessableEntityError = exports.PermissionDeniedError = exports.InternalServerError = exports.AuthenticationError = exports.BadRequestError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.APIUserAbortError = exports.APIConnectionTimeoutError = exports.APIConnectionError = exports.APIError = exports.RizaError = exports.fileFromPath = exports.toFile = exports.Riza = void 0;
const Core = __importStar(require("./core.js"));
const Errors = __importStar(require("./error.js"));
const Pagination = __importStar(require("./pagination.js"));
const Uploads = __importStar(require("./uploads.js"));
const API = __importStar(require("./resources/index.js"));
const command_1 = require("./resources/command.js");
const executions_1 = require("./resources/executions.js");
const secrets_1 = require("./resources/secrets.js");
const tools_1 = require("./resources/tools.js");
const runtimes_1 = require("./resources/runtimes/runtimes.js");
/**
 * API Client for interfacing with the Riza API.
 */
class Riza extends Core.APIClient {
    /**
     * API Client for interfacing with the Riza API.
     *
     * @param {string | undefined} [opts.apiKey=process.env['RIZA_API_KEY'] ?? undefined]
     * @param {string} [opts.baseURL=process.env['RIZA_BASE_URL'] ?? https://api.riza.io] - Override the default base URL for the API.
     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.
     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.
     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.
     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.
     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.
     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.
     */
    constructor({ baseURL = Core.readEnv('RIZA_BASE_URL'), apiKey = Core.readEnv('RIZA_API_KEY'), ...opts } = {}) {
        if (apiKey === undefined) {
            throw new Errors.RizaError("The RIZA_API_KEY environment variable is missing or empty; either provide it, or instantiate the Riza client with an apiKey option, like new Riza({ apiKey: 'My API Key' }).");
        }
        const options = {
            apiKey,
            ...opts,
            baseURL: baseURL || `https://api.riza.io`,
        };
        super({
            baseURL: options.baseURL,
            baseURLOverridden: baseURL ? baseURL !== 'https://api.riza.io' : false,
            timeout: options.timeout ?? 60000 /* 1 minute */,
            httpAgent: options.httpAgent,
            maxRetries: options.maxRetries,
            fetch: options.fetch,
        });
        _Riza_instances.add(this);
        this.secrets = new API.Secrets(this);
        this.tools = new API.Tools(this);
        this.command = new API.Command(this);
        this.runtimes = new API.Runtimes(this);
        this.executions = new API.Executions(this);
        this._options = options;
        this.apiKey = apiKey;
    }
    defaultQuery() {
        return this._options.defaultQuery;
    }
    defaultHeaders(opts) {
        return {
            ...super.defaultHeaders(opts),
            ...this._options.defaultHeaders,
        };
    }
    authHeaders(opts) {
        return { Authorization: `Bearer ${this.apiKey}` };
    }
}
exports.Riza = Riza;
_a = Riza, _Riza_instances = new WeakSet(), _Riza_baseURLOverridden = function _Riza_baseURLOverridden() {
    return this.baseURL !== 'https://api.riza.io';
};
Riza.Riza = _a;
Riza.DEFAULT_TIMEOUT = 60000; // 1 minute
Riza.RizaError = Errors.RizaError;
Riza.APIError = Errors.APIError;
Riza.APIConnectionError = Errors.APIConnectionError;
Riza.APIConnectionTimeoutError = Errors.APIConnectionTimeoutError;
Riza.APIUserAbortError = Errors.APIUserAbortError;
Riza.NotFoundError = Errors.NotFoundError;
Riza.ConflictError = Errors.ConflictError;
Riza.RateLimitError = Errors.RateLimitError;
Riza.BadRequestError = Errors.BadRequestError;
Riza.AuthenticationError = Errors.AuthenticationError;
Riza.InternalServerError = Errors.InternalServerError;
Riza.PermissionDeniedError = Errors.PermissionDeniedError;
Riza.UnprocessableEntityError = Errors.UnprocessableEntityError;
Riza.toFile = Uploads.toFile;
Riza.fileFromPath = Uploads.fileFromPath;
Riza.Secrets = secrets_1.Secrets;
Riza.SecretsSecretsPagination = secrets_1.SecretsSecretsPagination;
Riza.Tools = tools_1.Tools;
Riza.ToolsToolsPagination = tools_1.ToolsToolsPagination;
Riza.Command = command_1.Command;
Riza.Runtimes = runtimes_1.Runtimes;
Riza.RuntimesRuntimesPagination = runtimes_1.RuntimesRuntimesPagination;
Riza.Executions = executions_1.Executions;
Riza.ExecutionsDefaultPagination = executions_1.ExecutionsDefaultPagination;
var uploads_1 = require("./uploads.js");
Object.defineProperty(exports, "toFile", { enumerable: true, get: function () { return uploads_1.toFile; } });
Object.defineProperty(exports, "fileFromPath", { enumerable: true, get: function () { return uploads_1.fileFromPath; } });
var error_1 = require("./error.js");
Object.defineProperty(exports, "RizaError", { enumerable: true, get: function () { return error_1.RizaError; } });
Object.defineProperty(exports, "APIError", { enumerable: true, get: function () { return error_1.APIError; } });
Object.defineProperty(exports, "APIConnectionError", { enumerable: true, get: function () { return error_1.APIConnectionError; } });
Object.defineProperty(exports, "APIConnectionTimeoutError", { enumerable: true, get: function () { return error_1.APIConnectionTimeoutError; } });
Object.defineProperty(exports, "APIUserAbortError", { enumerable: true, get: function () { return error_1.APIUserAbortError; } });
Object.defineProperty(exports, "NotFoundError", { enumerable: true, get: function () { return error_1.NotFoundError; } });
Object.defineProperty(exports, "ConflictError", { enumerable: true, get: function () { return error_1.ConflictError; } });
Object.defineProperty(exports, "RateLimitError", { enumerable: true, get: function () { return error_1.RateLimitError; } });
Object.defineProperty(exports, "BadRequestError", { enumerable: true, get: function () { return error_1.BadRequestError; } });
Object.defineProperty(exports, "AuthenticationError", { enumerable: true, get: function () { return error_1.AuthenticationError; } });
Object.defineProperty(exports, "InternalServerError", { enumerable: true, get: function () { return error_1.InternalServerError; } });
Object.defineProperty(exports, "PermissionDeniedError", { enumerable: true, get: function () { return error_1.PermissionDeniedError; } });
Object.defineProperty(exports, "UnprocessableEntityError", { enumerable: true, get: function () { return error_1.UnprocessableEntityError; } });
exports = module.exports = Riza;
exports.default = Riza;
//# sourceMappingURL=index.js.map