{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 标准事件绑定\n", "*******\n", "- invoke\n", "- steam\n", "- batch\n", "- astream_events"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Requirement already satisfied: langchain_openai in ./.venv/lib/python3.11/site-packages (0.3.5)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.34 in ./.venv/lib/python3.11/site-packages (from langchain_openai) (0.3.34)\n", "Requirement already satisfied: openai<2.0.0,>=1.58.1 in ./.venv/lib/python3.11/site-packages (from langchain_openai) (1.61.1)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in ./.venv/lib/python3.11/site-packages (from langchain_openai) (0.8.0)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in ./.venv/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain_openai) (0.3.8)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in ./.venv/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain_openai) (9.0.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in ./.venv/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain_openai) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in ./.venv/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain_openai) (6.0.2)\n", "Requirement already satisfied: packaging<25,>=23.2 in ./.venv/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain_openai) (24.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in ./.venv/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain_openai) (4.12.2)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.5.2 in ./.venv/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.34->langchain_openai) (2.10.6)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in ./.venv/lib/python3.11/site-packages (from openai<2.0.0,>=1.58.1->langchain_openai) (4.8.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in ./.venv/lib/python3.11/site-packages (from openai<2.0.0,>=1.58.1->langchain_openai) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in ./.venv/lib/python3.11/site-packages (from openai<2.0.0,>=1.58.1->langchain_openai) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in ./.venv/lib/python3.11/site-packages (from openai<2.0.0,>=1.58.1->langchain_openai) (0.8.2)\n", "Requirement already satisfied: sniffio in ./.venv/lib/python3.11/site-packages (from openai<2.0.0,>=1.58.1->langchain_openai) (1.3.1)\n", "Requirement already satisfied: tqdm>4 in ./.venv/lib/python3.11/site-packages (from openai<2.0.0,>=1.58.1->langchain_openai) (4.67.1)\n", "Requirement already satisfied: regex>=2022.1.18 in ./.venv/lib/python3.11/site-packages (from tiktoken<1,>=0.7->langchain_openai) (2024.11.6)\n", "Requirement already satisfied: requests>=2.26.0 in ./.venv/lib/python3.11/site-packages (from tiktoken<1,>=0.7->langchain_openai) (2.32.3)\n", "Requirement already satisfied: idna>=2.8 in ./.venv/lib/python3.11/site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.58.1->langchain_openai) (3.10)\n", "Requirement already satisfied: certifi in ./.venv/lib/python3.11/site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.58.1->langchain_openai) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in ./.venv/lib/python3.11/site-packages (from httpx<1,>=0.23.0->openai<2.0.0,>=1.58.1->langchain_openai) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in ./.venv/lib/python3.11/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai<2.0.0,>=1.58.1->langchain_openai) (0.14.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in ./.venv/lib/python3.11/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.34->langchain_openai) (3.0.0)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in ./.venv/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain_openai) (3.10.15)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in ./.venv/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain_openai) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in ./.venv/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.34->langchain_openai) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in ./.venv/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.5.2->langchain-core<1.0.0,>=0.3.34->langchain_openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in ./.venv/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.5.2->langchain-core<1.0.0,>=0.3.34->langchain_openai) (2.27.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in ./.venv/lib/python3.11/site-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain_openai) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in ./.venv/lib/python3.11/site-packages (from requests>=2.26.0->tiktoken<1,>=0.7->langchain_openai) (2.3.0)\n", "\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m23.2.1\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m25.0.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}], "source": ["! pip install langchain_openai"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 定义模型\n", "***"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "import os\n", "import asyncio\n", "\n", "llm = ChatOpenAI(\n", "    model=\"gpt-4\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"OPENAI_API_KEY\"),\n", "    base_url=os.environ.get(\"OPENAI_API_BASE\"),\n", "    )\n", "question = \"langchain是什么?\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Invoke事件\n", "****"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='Langchain是一种基于区块链技术的语言翻译平台。它使用人工智能和机器学习技术，以提供更准确、更自然的翻译结果。此外，Langchain还利用区块链技术来确保翻译过程的透明度和安全性，同时也为翻译者提供了一个公平的奖励机制。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 120, 'prompt_tokens': 14, 'total_tokens': 134, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'id': 'chatcmpl-BFJuVYaAUlgAEZtgpjshiO2q2tYSG', 'finish_reason': 'stop', 'logprobs': None}, id='run-829ab7c3-4bbd-4c34-9665-9f792a0529bb-0', usage_metadata={'input_tokens': 14, 'output_tokens': 120, 'total_tokens': 134, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.invoke(question)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stream事件\n", "****"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|\n", "|\n", "Lang|\n", "chain|\n", "是|\n", "一|\n", "种|\n", "基|\n", "于|\n", "区|\n", "块|\n", "链|\n", "技|\n", "术|\n", "的|\n", "语|\n", "言|\n", "翻|\n", "译|\n", "平|\n", "台|\n", "。|\n", "它|\n", "的|\n", "目|\n", "标|\n", "是|\n", "通过|\n", "利|\n", "用|\n", "人|\n", "工|\n", "智|\n", "能|\n", "和|\n", "区|\n", "块|\n", "链|\n", "技|\n", "术|\n", "，|\n", "创建|\n", "一个|\n", "去|\n", "中|\n", "心|\n", "化|\n", "的|\n", "、|\n", "公|\n", "平|\n", "的|\n", "、|\n", "透|\n", "明|\n", "的|\n", "和|\n", "高|\n", "效|\n", "的|\n", "全|\n", "球|\n", "语|\n", "言|\n", "服务|\n", "市|\n", "场|\n", "。|\n", "在|\n", "Lang|\n", "chain|\n", "平|\n", "台|\n", "上|\n", "，|\n", "翻|\n", "译|\n", "者|\n", "可以|\n", "直|\n", "接|\n", "与|\n", "客|\n", "户|\n", "进行|\n", "交|\n", "易|\n", "，|\n", "无|\n", "需|\n", "通过|\n", "任|\n", "何|\n", "中|\n", "介|\n", "或|\n", "翻|\n", "译|\n", "公司|\n", "。|\n", "此|\n", "外|\n", "，|\n", "Lang|\n", "chain|\n", "还|\n", "利|\n", "用|\n", "人|\n", "工|\n", "智|\n", "能|\n", "技|\n", "术|\n", "来|\n", "提|\n", "高|\n", "翻|\n", "译|\n", "的|\n", "质|\n", "量|\n", "和|\n", "效|\n", "率|\n", "。|\n", "|\n", "|\n"]}], "source": ["for chunk in llm.stream(question):\n", "    print(chunk.content+\"|\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON>件\n", "***"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["[AIMessage(content='Langchain的作者信息并未公开，可能是由一个团队或公司开发的。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 23, 'prompt_tokens': 14, 'total_tokens': 37, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-bced5fdc-aca6-4128-b637-523eddd07b17-0', usage_metadata={'input_tokens': 14, 'output_tokens': 23, 'total_tokens': 37, 'input_token_details': {}, 'output_token_details': {}}),\n", " AIMessage(content='Langchain的竞品可能包括以下几个：\\n\\n1. Google翻译：Google翻译是一款由Google开发的免费多语言机器翻译服务，可以翻译网页、文档和其他内容。\\n\\n2. DeepL：DeepL是一款基于人工智能的翻译工具，提供多种语言的翻译服务。\\n\\n3. Microsoft翻译：Microsoft翻译是微软公司开发的一款翻译软件，支持多种语言之间的翻译。\\n\\n4. IBM Watson语言翻译：IBM Watson语言翻译是IBM的一款基于云的人工智能翻译服务。\\n\\n5. Amazon翻译：Amazon翻译是亚马逊提供的一款机器翻译服务，支持多种语言之间的翻译。\\n\\n6. SDL翻译：SDL是一家全球领先的翻译服务提供商，提供专业的人工翻译和机器翻译服务。\\n\\n以上这些都是Langchain的潜在竞品，它们都提供了类似的翻译服务。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 335, 'prompt_tokens': 18, 'total_tokens': 353, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'gpt-4', 'system_fingerprint': None, 'finish_reason': 'stop', 'logprobs': None}, id='run-aa85f09b-d8e3-4fbb-8302-da4f8e50a813-0', usage_metadata={'input_tokens': 18, 'output_tokens': 335, 'total_tokens': 353, 'input_token_details': {}, 'output_token_details': {}})]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.batch([\"langchain作者是谁？\", \"Langchain的竞品有哪些？\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### astream_events\n", "***"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["event=on_chat_model_start | name=ChatOpenAI | data={'input': '介绍下LangChain'}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='Lang', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='Chain', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='是', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='一个', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='基', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='于', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='区', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='块', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='链', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='技', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='术', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='全', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='球', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='语', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='言', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='服务', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='平', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='台', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='它', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='目', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='标', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='是', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='通过', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='去', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='中', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='心', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='化', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='方式', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='解', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='决', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='传', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='统', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='语', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='言', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='服务', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='市', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='场', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='中', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='存在', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='问题', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='如', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='高', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='昂', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='中', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='介', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='费', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='用', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='、', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='服务', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='质', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='量', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='不', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='一', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='、', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='交', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='易', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='不', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='透', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='明', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='等', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='。\\n\\n', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='Lang', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='Chain', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='利', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='用', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='区', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='块', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='链', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='技', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='术', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='将', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='语', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='言', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='服务', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='需', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='求', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='方', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='和', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='提', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='供', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='方', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='直', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='接', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='连接', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='起', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='来', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='消', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='除', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='了', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='中', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='间', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='商', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='降', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='低', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='了', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='交', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='易', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='成', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='本', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='同时', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='通过', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='智', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='能', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='合', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='约', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='和', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='分', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='布', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='式', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='账', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='本', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='技', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='术', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='确', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='保', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='交', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='易', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='透', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='明', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='性', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='和', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='公', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='正', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='性', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='。\\n\\n', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='此', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='外', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='Lang', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='Chain', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='还', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='利', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='用', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='人', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='工', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='智', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='能', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='技', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='术', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='提', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='供', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='自', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='动', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='翻', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='译', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='和', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='校', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='对', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='服务', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='提', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='高', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='了', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='语', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='言', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='服务', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='效', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='率', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='和', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='质', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='量', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='同时', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='Lang', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='Chain', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='也', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='为', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='语', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='言', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='服务', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='提', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='供', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='者', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='提', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='供', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='了', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='一个', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='公', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='平', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='竞', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='争', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='环', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='境', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='他', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='们', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='可以', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='根', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='据', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='自', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='己', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='能', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='力', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='和', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='贡', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='献', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='获', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='得', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='相', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='应', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='报', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='酬', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='。\\n\\n', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='总', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='来', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='说', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='Lang', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='Chain', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='是', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='一个', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='利', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='用', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='区', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='块', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='链', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='和', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='人', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='工', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='智', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='能', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='技', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='术', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='，', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='改', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='变', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='传', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='统', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='语', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='言', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='服务', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='市', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='场', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='的', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='创', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='新', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='平', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='台', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='。', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'gpt-4'}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf')}\n", "event=on_chat_model_stream | name=ChatOpenAI | data={'chunk': AIMessageChunk(content='', additional_kwargs={}, response_metadata={}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf', usage_metadata={'input_tokens': 13, 'output_tokens': 298, 'total_tokens': 311, 'input_token_details': {}, 'output_token_details': {}})}\n", "event=on_chat_model_end | name=ChatOpenAI | data={'output': AIMessageChunk(content='LangChain是一个基于区块链技术的全球语言服务平台。它的目标是通过去中心化的方式，解决传统语言服务市场中存在的问题，如高昂的中介费用、服务质量不一、交易不透明等。\\n\\nLangChain利用区块链技术，将语言服务的需求方和提供方直接连接起来，消除了中间商，降低了交易成本。同时，通过智能合约和分布式账本技术，确保交易的透明性和公正性。\\n\\n此外，LangChain还利用人工智能技术，提供自动翻译和校对服务，提高了语言服务的效率和质量。同时，LangChain也为语言服务提供者提供了一个公平的竞争环境，他们可以根据自己的能力和贡献获得相应的报酬。\\n\\n总的来说，LangChain是一个利用区块链和人工智能技术，改变传统语言服务市场的创新平台。', additional_kwargs={}, response_metadata={'finish_reason': 'stop', 'model_name': 'gpt-4'}, id='run-f6536718-cde2-477f-96dc-ebc58b587aaf', usage_metadata={'input_tokens': 13, 'output_tokens': 298, 'total_tokens': 311, 'input_token_details': {}, 'output_token_details': {}})}\n"]}], "source": ["async for event in llm.astream_events(\"介绍下LangChain\", version=\"v2\"):\n", "        print(f\"event={event['event']} | name={event['name']} | data={event['data']}\")\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 4}