{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Graph_RAG\n", "***\n", "使用知识图谱来增强检索"]}, {"cell_type": "markdown", "metadata": {}, "source": ["数据准备"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["! pip install -qU graph_rag_example_helpers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["下载测试文档"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from graph_rag_example_helpers.datasets.animals import fetch_documents\n", "animals = fetch_documents()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from langchain_openai import OpenAIEmbeddings\n", "\n", "embeddings = OpenAIEmbeddings(model=\"text-embedding-3-large\",\n", "                              api_key = \"hk-72n0gg10000506100b90ecb51c341412d508ccd25ed0e3ba\",\n", "                              base_url = \"https://api.openai-hk.com/v1\",\n", "                   \n", "                              )"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting langchain-graph-retriever[chroma]\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/5c/b9/347f2bb6d50f7733c7b78b84ce872c3cc4b64bbfa479bc7f367a80b48957/langchain_graph_retriever-0.8.0-py3-none-any.whl (33 kB)\n", "Requirement already satisfied: backoff>=2.2.1 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langchain-graph-retriever[chroma]) (2.2.1)\n", "Requirement already satisfied: graph-retriever in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langchain-graph-retriever[chroma]) (0.8.0)\n", "Requirement already satisfied: immutabledict>=4.2.1 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langchain-graph-retriever[chroma]) (4.2.1)\n", "Requirement already satisfied: langchain-core>=0.3.29 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langchain-graph-retriever[chroma]) (0.3.67)\n", "Collecting networkx>=3.4.2 (from langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/eb/8d/776adee7bbf76365fdd7f2552710282c79a4ead5d2a46408c9043a2b70ba/networkx-3.5-py3-none-any.whl (2.0 MB)\n", "     ---------------------------------------- 0.0/2.0 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.0 MB ? eta -:--:--\n", "     ----- ---------------------------------- 0.3/2.0 MB ? eta -:--:--\n", "     ----- ---------------------------------- 0.3/2.0 MB ? eta -:--:--\n", "     ---------- ----------------------------- 0.5/2.0 MB 703.9 kB/s eta 0:00:03\n", "     ---------- ----------------------------- 0.5/2.0 MB 703.9 kB/s eta 0:00:03\n", "     --------------- ------------------------ 0.8/2.0 MB 659.1 kB/s eta 0:00:02\n", "     --------------- ------------------------ 0.8/2.0 MB 659.1 kB/s eta 0:00:02\n", "     --------------- ------------------------ 0.8/2.0 MB 659.1 kB/s eta 0:00:02\n", "     -------------------- ------------------- 1.0/2.0 MB 584.9 kB/s eta 0:00:02\n", "     -------------------- ------------------- 1.0/2.0 MB 584.9 kB/s eta 0:00:02\n", "     ------------------------- -------------- 1.3/2.0 MB 577.6 kB/s eta 0:00:02\n", "     ------------------------- -------------- 1.3/2.0 MB 577.6 kB/s eta 0:00:02\n", "     ------------------------------ --------- 1.6/2.0 MB 589.5 kB/s eta 0:00:01\n", "     ------------------------------ --------- 1.6/2.0 MB 589.5 kB/s eta 0:00:01\n", "     ------------------------------------ --- 1.8/2.0 MB 596.6 kB/s eta 0:00:01\n", "     ------------------------------------ --- 1.8/2.0 MB 596.6 kB/s eta 0:00:01\n", "     ------------------------------------ --- 1.8/2.0 MB 596.6 kB/s eta 0:00:01\n", "     ---------------------------------------- 2.0/2.0 MB 553.4 kB/s eta 0:00:00\n", "Requirement already satisfied: pydantic>=2.10.4 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langchain-graph-retriever[chroma]) (2.11.5)\n", "Requirement already satisfied: typing-extensions>=4.12.2 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langchain-graph-retriever[chroma]) (4.14.0)\n", "Collecting chromadb>=0.5.23 (from langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a1/30/6890da607358993f87a01e80bcce916b4d91515ce865f07dc06845cb472f/chromadb-1.0.15-cp39-abi3-win_amd64.whl (19.5 MB)\n", "     ---------------------------------------- 0.0/19.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/19.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/19.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/19.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/19.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/19.5 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/19.5 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/19.5 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/19.5 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/19.5 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/19.5 MB ? eta -:--:--\n", "      --------------------------------------- 0.3/19.5 MB ? eta -:--:--\n", "     - ------------------------------------- 0.5/19.5 MB 206.7 kB/s eta 0:01:32\n", "     - ------------------------------------- 0.5/19.5 MB 206.7 kB/s eta 0:01:32\n", "     - ------------------------------------- 0.5/19.5 MB 206.7 kB/s eta 0:01:32\n", "     - ------------------------------------- 0.5/19.5 MB 206.7 kB/s eta 0:01:32\n", "     - ------------------------------------- 0.5/19.5 MB 206.7 kB/s eta 0:01:32\n", "     - ------------------------------------- 0.5/19.5 MB 206.7 kB/s eta 0:01:32\n", "     - ------------------------------------- 0.8/19.5 MB 210.9 kB/s eta 0:01:29\n", "     - ------------------------------------- 0.8/19.5 MB 210.9 kB/s eta 0:01:29\n", "     - ------------------------------------- 0.8/19.5 MB 210.9 kB/s eta 0:01:29\n", "     - ------------------------------------- 0.8/19.5 MB 210.9 kB/s eta 0:01:29\n", "     - ------------------------------------- 0.8/19.5 MB 210.9 kB/s eta 0:01:29\n", "     - ------------------------------------- 0.8/19.5 MB 210.9 kB/s eta 0:01:29\n", "     - ------------------------------------- 0.8/19.5 MB 210.9 kB/s eta 0:01:29\n", "     -- ------------------------------------ 1.0/19.5 MB 200.0 kB/s eta 0:01:33\n", "     -- ------------------------------------ 1.0/19.5 MB 200.0 kB/s eta 0:01:33\n", "     -- ------------------------------------ 1.0/19.5 MB 200.0 kB/s eta 0:01:33\n", "     -- ------------------------------------ 1.0/19.5 MB 200.0 kB/s eta 0:01:33\n", "     -- ------------------------------------ 1.0/19.5 MB 200.0 kB/s eta 0:01:33\n", "     -- ------------------------------------ 1.0/19.5 MB 200.0 kB/s eta 0:01:33\n", "     -- ------------------------------------ 1.0/19.5 MB 200.0 kB/s eta 0:01:33\n", "     -- ------------------------------------ 1.0/19.5 MB 200.0 kB/s eta 0:01:33\n", "     -- ------------------------------------ 1.3/19.5 MB 180.1 kB/s eta 0:01:42\n", "     -- ------------------------------------ 1.3/19.5 MB 180.1 kB/s eta 0:01:42\n", "     -- ------------------------------------ 1.3/19.5 MB 180.1 kB/s eta 0:01:42\n", "     -- ------------------------------------ 1.3/19.5 MB 180.1 kB/s eta 0:01:42\n", "     -- ------------------------------------ 1.3/19.5 MB 180.1 kB/s eta 0:01:42\n", "     -- ------------------------------------ 1.3/19.5 MB 180.1 kB/s eta 0:01:42\n", "     --- ----------------------------------- 1.6/19.5 MB 188.8 kB/s eta 0:01:36\n", "     --- ----------------------------------- 1.6/19.5 MB 188.8 kB/s eta 0:01:36\n", "     --- ----------------------------------- 1.6/19.5 MB 188.8 kB/s eta 0:01:36\n", "     --- ----------------------------------- 1.8/19.5 MB 205.7 kB/s eta 0:01:26\n", "     --- ----------------------------------- 1.8/19.5 MB 205.7 kB/s eta 0:01:26\n", "     ---- ---------------------------------- 2.1/19.5 MB 225.7 kB/s eta 0:01:18\n", "     ---- ---------------------------------- 2.1/19.5 MB 225.7 kB/s eta 0:01:18\n", "     ---- ---------------------------------- 2.1/19.5 MB 225.7 kB/s eta 0:01:18\n", "     ---- ---------------------------------- 2.4/19.5 MB 244.8 kB/s eta 0:01:11\n", "     ---- ---------------------------------- 2.4/19.5 MB 244.8 kB/s eta 0:01:11\n", "     ----- --------------------------------- 2.6/19.5 MB 257.3 kB/s eta 0:01:06\n", "     ----- --------------------------------- 2.6/19.5 MB 257.3 kB/s eta 0:01:06\n", "     ----- --------------------------------- 2.9/19.5 MB 273.4 kB/s eta 0:01:01\n", "     ----- --------------------------------- 2.9/19.5 MB 273.4 kB/s eta 0:01:01\n", "     ----- --------------------------------- 2.9/19.5 MB 273.4 kB/s eta 0:01:01\n", "     ------ -------------------------------- 3.1/19.5 MB 287.0 kB/s eta 0:00:58\n", "     ------ -------------------------------- 3.1/19.5 MB 287.0 kB/s eta 0:00:58\n", "     ------ -------------------------------- 3.4/19.5 MB 295.6 kB/s eta 0:00:55\n", "     ------ -------------------------------- 3.4/19.5 MB 295.6 kB/s eta 0:00:55\n", "     ------ -------------------------------- 3.4/19.5 MB 295.6 kB/s eta 0:00:55\n", "     ------ -------------------------------- 3.4/19.5 MB 295.6 kB/s eta 0:00:55\n", "     ------- ------------------------------- 3.7/19.5 MB 301.4 kB/s eta 0:00:53\n", "     ------- ------------------------------- 3.7/19.5 MB 301.4 kB/s eta 0:00:53\n", "     ------- ------------------------------- 3.9/19.5 MB 307.7 kB/s eta 0:00:51\n", "     ------- ------------------------------- 3.9/19.5 MB 307.7 kB/s eta 0:00:51\n", "     ------- ------------------------------- 3.9/19.5 MB 307.7 kB/s eta 0:00:51\n", "     -------- ------------------------------ 4.2/19.5 MB 312.6 kB/s eta 0:00:50\n", "     -------- ------------------------------ 4.2/19.5 MB 312.6 kB/s eta 0:00:50\n", "     -------- ------------------------------ 4.2/19.5 MB 312.6 kB/s eta 0:00:50\n", "     -------- ------------------------------ 4.2/19.5 MB 312.6 kB/s eta 0:00:50\n", "     -------- ------------------------------ 4.5/19.5 MB 314.9 kB/s eta 0:00:48\n", "     -------- ------------------------------ 4.5/19.5 MB 314.9 kB/s eta 0:00:48\n", "     -------- ------------------------------ 4.5/19.5 MB 314.9 kB/s eta 0:00:48\n", "     -------- ------------------------------ 4.5/19.5 MB 314.9 kB/s eta 0:00:48\n", "     --------- ----------------------------- 4.7/19.5 MB 316.0 kB/s eta 0:00:47\n", "     --------- ----------------------------- 4.7/19.5 MB 316.0 kB/s eta 0:00:47\n", "     --------- ----------------------------- 5.0/19.5 MB 323.3 kB/s eta 0:00:45\n", "     --------- ----------------------------- 5.0/19.5 MB 323.3 kB/s eta 0:00:45\n", "     ---------- ---------------------------- 5.2/19.5 MB 329.6 kB/s eta 0:00:44\n", "     ---------- ---------------------------- 5.2/19.5 MB 329.6 kB/s eta 0:00:44\n", "     ---------- ---------------------------- 5.5/19.5 MB 337.7 kB/s eta 0:00:42\n", "     ---------- ---------------------------- 5.5/19.5 MB 337.7 kB/s eta 0:00:42\n", "     ---------- ---------------------------- 5.5/19.5 MB 337.7 kB/s eta 0:00:42\n", "     ----------- --------------------------- 5.8/19.5 MB 343.8 kB/s eta 0:00:41\n", "     ----------- --------------------------- 5.8/19.5 MB 343.8 kB/s eta 0:00:41\n", "     ------------ -------------------------- 6.0/19.5 MB 347.6 kB/s eta 0:00:39\n", "     ------------ -------------------------- 6.0/19.5 MB 347.6 kB/s eta 0:00:39\n", "     ------------ -------------------------- 6.0/19.5 MB 347.6 kB/s eta 0:00:39\n", "     ------------ -------------------------- 6.0/19.5 MB 347.6 kB/s eta 0:00:39\n", "     ------------ -------------------------- 6.3/19.5 MB 348.6 kB/s eta 0:00:38\n", "     ------------ -------------------------- 6.3/19.5 MB 348.6 kB/s eta 0:00:38\n", "     ------------- ------------------------- 6.6/19.5 MB 352.1 kB/s eta 0:00:37\n", "     ------------- ------------------------- 6.6/19.5 MB 352.1 kB/s eta 0:00:37\n", "     ------------- ------------------------- 6.6/19.5 MB 352.1 kB/s eta 0:00:37\n", "     ------------- ------------------------- 6.8/19.5 MB 355.7 kB/s eta 0:00:36\n", "     ------------- ------------------------- 6.8/19.5 MB 355.7 kB/s eta 0:00:36\n", "     ------------- ------------------------- 6.8/19.5 MB 355.7 kB/s eta 0:00:36\n", "     -------------- ------------------------ 7.1/19.5 MB 357.2 kB/s eta 0:00:35\n", "     -------------- ------------------------ 7.1/19.5 MB 357.2 kB/s eta 0:00:35\n", "     -------------- ------------------------ 7.3/19.5 MB 360.6 kB/s eta 0:00:34\n", "     -------------- ------------------------ 7.3/19.5 MB 360.6 kB/s eta 0:00:34\n", "     -------------- ------------------------ 7.3/19.5 MB 360.6 kB/s eta 0:00:34\n", "     --------------- ----------------------- 7.6/19.5 MB 361.9 kB/s eta 0:00:33\n", "     --------------- ----------------------- 7.6/19.5 MB 361.9 kB/s eta 0:00:33\n", "     --------------- ----------------------- 7.6/19.5 MB 361.9 kB/s eta 0:00:33\n", "     --------------- ----------------------- 7.9/19.5 MB 366.2 kB/s eta 0:00:32\n", "     --------------- ----------------------- 7.9/19.5 MB 366.2 kB/s eta 0:00:32\n", "     --------------- ----------------------- 7.9/19.5 MB 366.2 kB/s eta 0:00:32\n", "     ---------------- ---------------------- 8.1/19.5 MB 365.7 kB/s eta 0:00:32\n", "     ---------------- ---------------------- 8.1/19.5 MB 365.7 kB/s eta 0:00:32\n", "     ---------------- ---------------------- 8.1/19.5 MB 365.7 kB/s eta 0:00:32\n", "     ---------------- ---------------------- 8.4/19.5 MB 368.5 kB/s eta 0:00:31\n", "     ---------------- ---------------------- 8.4/19.5 MB 368.5 kB/s eta 0:00:31\n", "     ----------------- --------------------- 8.7/19.5 MB 372.1 kB/s eta 0:00:30\n", "     ----------------- --------------------- 8.7/19.5 MB 372.1 kB/s eta 0:00:30\n", "     ----------------- --------------------- 8.7/19.5 MB 372.1 kB/s eta 0:00:30\n", "     ----------------- --------------------- 8.9/19.5 MB 374.6 kB/s eta 0:00:29\n", "     ----------------- --------------------- 8.9/19.5 MB 374.6 kB/s eta 0:00:29\n", "     ----------------- --------------------- 8.9/19.5 MB 374.6 kB/s eta 0:00:29\n", "     ------------------ -------------------- 9.2/19.5 MB 375.3 kB/s eta 0:00:28\n", "     ------------------ -------------------- 9.2/19.5 MB 375.3 kB/s eta 0:00:28\n", "     ------------------ -------------------- 9.2/19.5 MB 375.3 kB/s eta 0:00:28\n", "     ------------------ -------------------- 9.4/19.5 MB 374.2 kB/s eta 0:00:27\n", "     ------------------ -------------------- 9.4/19.5 MB 374.2 kB/s eta 0:00:27\n", "     ------------------ -------------------- 9.4/19.5 MB 374.2 kB/s eta 0:00:27\n", "     ------------------ -------------------- 9.4/19.5 MB 374.2 kB/s eta 0:00:27\n", "     ------------------- ------------------- 9.7/19.5 MB 373.4 kB/s eta 0:00:27\n", "     ------------------- ------------------- 9.7/19.5 MB 373.4 kB/s eta 0:00:27\n", "     ------------------- ------------------- 9.7/19.5 MB 373.4 kB/s eta 0:00:27\n", "     ------------------- ------------------- 9.7/19.5 MB 373.4 kB/s eta 0:00:27\n", "     ------------------- ------------------ 10.0/19.5 MB 372.1 kB/s eta 0:00:26\n", "     ------------------- ------------------ 10.0/19.5 MB 372.1 kB/s eta 0:00:26\n", "     ------------------- ------------------ 10.0/19.5 MB 372.1 kB/s eta 0:00:26\n", "     ------------------- ------------------ 10.2/19.5 MB 373.0 kB/s eta 0:00:25\n", "     ------------------- ------------------ 10.2/19.5 MB 373.0 kB/s eta 0:00:25\n", "     -------------------- ----------------- 10.5/19.5 MB 375.8 kB/s eta 0:00:25\n", "     -------------------- ----------------- 10.5/19.5 MB 375.8 kB/s eta 0:00:25\n", "     -------------------- ----------------- 10.7/19.5 MB 377.5 kB/s eta 0:00:24\n", "     -------------------- ----------------- 10.7/19.5 MB 377.5 kB/s eta 0:00:24\n", "     -------------------- ----------------- 10.7/19.5 MB 377.5 kB/s eta 0:00:24\n", "     -------------------- ----------------- 10.7/19.5 MB 377.5 kB/s eta 0:00:24\n", "     --------------------- ---------------- 11.0/19.5 MB 375.6 kB/s eta 0:00:23\n", "     --------------------- ---------------- 11.0/19.5 MB 375.6 kB/s eta 0:00:23\n", "     --------------------- ---------------- 11.3/19.5 MB 379.6 kB/s eta 0:00:22\n", "     --------------------- ---------------- 11.3/19.5 MB 379.6 kB/s eta 0:00:22\n", "     --------------------- ---------------- 11.3/19.5 MB 379.6 kB/s eta 0:00:22\n", "     ---------------------- --------------- 11.5/19.5 MB 381.8 kB/s eta 0:00:21\n", "     ---------------------- --------------- 11.5/19.5 MB 381.8 kB/s eta 0:00:21\n", "     ---------------------- --------------- 11.5/19.5 MB 381.8 kB/s eta 0:00:21\n", "     ---------------------- --------------- 11.5/19.5 MB 381.8 kB/s eta 0:00:21\n", "     ---------------------- --------------- 11.8/19.5 MB 385.6 kB/s eta 0:00:21\n", "     ---------------------- --------------- 11.8/19.5 MB 385.6 kB/s eta 0:00:21\n", "     ---------------------- --------------- 11.8/19.5 MB 385.6 kB/s eta 0:00:21\n", "     ----------------------- -------------- 12.1/19.5 MB 386.3 kB/s eta 0:00:20\n", "     ----------------------- -------------- 12.1/19.5 MB 386.3 kB/s eta 0:00:20\n", "     ----------------------- -------------- 12.1/19.5 MB 386.3 kB/s eta 0:00:20\n", "     ----------------------- -------------- 12.1/19.5 MB 386.3 kB/s eta 0:00:20\n", "     ----------------------- -------------- 12.3/19.5 MB 393.0 kB/s eta 0:00:19\n", "     ----------------------- -------------- 12.3/19.5 MB 393.0 kB/s eta 0:00:19\n", "     ----------------------- -------------- 12.3/19.5 MB 393.0 kB/s eta 0:00:19\n", "     ----------------------- -------------- 12.3/19.5 MB 393.0 kB/s eta 0:00:19\n", "     ------------------------ ------------- 12.6/19.5 MB 401.5 kB/s eta 0:00:18\n", "     ------------------------ ------------- 12.6/19.5 MB 401.5 kB/s eta 0:00:18\n", "     ------------------------ ------------- 12.6/19.5 MB 401.5 kB/s eta 0:00:18\n", "     ------------------------- ------------ 12.8/19.5 MB 398.8 kB/s eta 0:00:17\n", "     ------------------------- ------------ 12.8/19.5 MB 398.8 kB/s eta 0:00:17\n", "     ------------------------- ------------ 12.8/19.5 MB 398.8 kB/s eta 0:00:17\n", "     ------------------------- ------------ 12.8/19.5 MB 398.8 kB/s eta 0:00:17\n", "     ------------------------- ------------ 12.8/19.5 MB 398.8 kB/s eta 0:00:17\n", "     ------------------------- ------------ 13.1/19.5 MB 412.9 kB/s eta 0:00:16\n", "     ------------------------- ------------ 13.1/19.5 MB 412.9 kB/s eta 0:00:16\n", "     ------------------------- ------------ 13.1/19.5 MB 412.9 kB/s eta 0:00:16\n", "     -------------------------- ----------- 13.4/19.5 MB 410.9 kB/s eta 0:00:15\n", "     -------------------------- ----------- 13.4/19.5 MB 410.9 kB/s eta 0:00:15\n", "     -------------------------- ----------- 13.4/19.5 MB 410.9 kB/s eta 0:00:15\n", "     -------------------------- ----------- 13.4/19.5 MB 410.9 kB/s eta 0:00:15\n", "     -------------------------- ----------- 13.6/19.5 MB 415.4 kB/s eta 0:00:15\n", "     -------------------------- ----------- 13.6/19.5 MB 415.4 kB/s eta 0:00:15\n", "     -------------------------- ----------- 13.6/19.5 MB 415.4 kB/s eta 0:00:15\n", "     -------------------------- ----------- 13.6/19.5 MB 415.4 kB/s eta 0:00:15\n", "     -------------------------- ----------- 13.6/19.5 MB 415.4 kB/s eta 0:00:15\n", "     --------------------------- ---------- 13.9/19.5 MB 410.1 kB/s eta 0:00:14\n", "     --------------------------- ---------- 13.9/19.5 MB 410.1 kB/s eta 0:00:14\n", "     --------------------------- ---------- 13.9/19.5 MB 410.1 kB/s eta 0:00:14\n", "     --------------------------- ---------- 13.9/19.5 MB 410.1 kB/s eta 0:00:14\n", "     --------------------------- ---------- 13.9/19.5 MB 410.1 kB/s eta 0:00:14\n", "     --------------------------- ---------- 14.2/19.5 MB 398.5 kB/s eta 0:00:14\n", "     --------------------------- ---------- 14.2/19.5 MB 398.5 kB/s eta 0:00:14\n", "     --------------------------- ---------- 14.2/19.5 MB 398.5 kB/s eta 0:00:14\n", "     --------------------------- ---------- 14.2/19.5 MB 398.5 kB/s eta 0:00:14\n", "     --------------------------- ---------- 14.2/19.5 MB 398.5 kB/s eta 0:00:14\n", "     ---------------------------- --------- 14.4/19.5 MB 393.7 kB/s eta 0:00:13\n", "     ---------------------------- --------- 14.4/19.5 MB 393.7 kB/s eta 0:00:13\n", "     ---------------------------- --------- 14.4/19.5 MB 393.7 kB/s eta 0:00:13\n", "     ---------------------------- --------- 14.4/19.5 MB 393.7 kB/s eta 0:00:13\n", "     ---------------------------- --------- 14.4/19.5 MB 393.7 kB/s eta 0:00:13\n", "     ---------------------------- --------- 14.7/19.5 MB 380.9 kB/s eta 0:00:13\n", "     ---------------------------- --------- 14.7/19.5 MB 380.9 kB/s eta 0:00:13\n", "     ---------------------------- --------- 14.7/19.5 MB 380.9 kB/s eta 0:00:13\n", "     ---------------------------- --------- 14.7/19.5 MB 380.9 kB/s eta 0:00:13\n", "     ---------------------------- --------- 14.7/19.5 MB 380.9 kB/s eta 0:00:13\n", "     ---------------------------- --------- 14.7/19.5 MB 380.9 kB/s eta 0:00:13\n", "     ----------------------------- -------- 14.9/19.5 MB 371.4 kB/s eta 0:00:13\n", "     ----------------------------- -------- 14.9/19.5 MB 371.4 kB/s eta 0:00:13\n", "     ----------------------------- -------- 14.9/19.5 MB 371.4 kB/s eta 0:00:13\n", "     ----------------------------- -------- 14.9/19.5 MB 371.4 kB/s eta 0:00:13\n", "     ----------------------------- -------- 14.9/19.5 MB 371.4 kB/s eta 0:00:13\n", "     ----------------------------- -------- 14.9/19.5 MB 371.4 kB/s eta 0:00:13\n", "     ----------------------------- -------- 15.2/19.5 MB 364.3 kB/s eta 0:00:12\n", "     ----------------------------- -------- 15.2/19.5 MB 364.3 kB/s eta 0:00:12\n", "     ----------------------------- -------- 15.2/19.5 MB 364.3 kB/s eta 0:00:12\n", "     ----------------------------- -------- 15.2/19.5 MB 364.3 kB/s eta 0:00:12\n", "     ----------------------------- -------- 15.2/19.5 MB 364.3 kB/s eta 0:00:12\n", "     ----------------------------- -------- 15.2/19.5 MB 364.3 kB/s eta 0:00:12\n", "     ----------------------------- -------- 15.2/19.5 MB 364.3 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.5/19.5 MB 353.5 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.5/19.5 MB 353.5 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.5/19.5 MB 353.5 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.5/19.5 MB 353.5 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.5/19.5 MB 353.5 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.5/19.5 MB 353.5 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.5/19.5 MB 353.5 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.7/19.5 MB 336.0 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.7/19.5 MB 336.0 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.7/19.5 MB 336.0 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.7/19.5 MB 336.0 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.7/19.5 MB 336.0 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.7/19.5 MB 336.0 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.7/19.5 MB 336.0 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.7/19.5 MB 336.0 kB/s eta 0:00:12\n", "     ------------------------------ ------- 15.7/19.5 MB 336.0 kB/s eta 0:00:12\n", "     ------------------------------- ------ 16.0/19.5 MB 318.1 kB/s eta 0:00:12\n", "     ------------------------------- ------ 16.0/19.5 MB 318.1 kB/s eta 0:00:12\n", "     ------------------------------- ------ 16.0/19.5 MB 318.1 kB/s eta 0:00:12\n", "     ------------------------------- ------ 16.0/19.5 MB 318.1 kB/s eta 0:00:12\n", "     ------------------------------- ------ 16.0/19.5 MB 318.1 kB/s eta 0:00:12\n", "     ------------------------------- ------ 16.0/19.5 MB 318.1 kB/s eta 0:00:12\n", "     ------------------------------- ------ 16.0/19.5 MB 318.1 kB/s eta 0:00:12\n", "     ------------------------------- ------ 16.0/19.5 MB 318.1 kB/s eta 0:00:12\n", "     ------------------------------- ------ 16.0/19.5 MB 318.1 kB/s eta 0:00:12\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     ------------------------------- ------ 16.3/19.5 MB 299.2 kB/s eta 0:00:11\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.5/19.5 MB 266.5 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.8/19.5 MB 240.9 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.8/19.5 MB 240.9 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.8/19.5 MB 240.9 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.8/19.5 MB 240.9 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.8/19.5 MB 240.9 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.8/19.5 MB 240.9 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.8/19.5 MB 240.9 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.8/19.5 MB 240.9 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.8/19.5 MB 240.9 kB/s eta 0:00:12\n", "     -------------------------------- ----- 16.8/19.5 MB 240.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.0/19.5 MB 220.8 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     --------------------------------- ---- 17.3/19.5 MB 192.9 kB/s eta 0:00:12\n", "     ---------------------------------- --- 17.6/19.5 MB 168.8 kB/s eta 0:00:12\n", "     ---------------------------------- --- 17.6/19.5 MB 168.8 kB/s eta 0:00:12\n", "     ---------------------------------- --- 17.6/19.5 MB 168.8 kB/s eta 0:00:12\n", "     ---------------------------------- --- 17.6/19.5 MB 168.8 kB/s eta 0:00:12\n", "     ---------------------------------- --- 17.6/19.5 MB 168.8 kB/s eta 0:00:12\n", "     ---------------------------------- --- 17.6/19.5 MB 168.8 kB/s eta 0:00:12\n", "     ---------------------------------- --- 17.6/19.5 MB 168.8 kB/s eta 0:00:12\n", "     ---------------------------------- --- 17.8/19.5 MB 161.2 kB/s eta 0:00:11\n", "     ---------------------------------- --- 17.8/19.5 MB 161.2 kB/s eta 0:00:11\n", "     ---------------------------------- --- 17.8/19.5 MB 161.2 kB/s eta 0:00:11\n", "     ---------------------------------- --- 17.8/19.5 MB 161.2 kB/s eta 0:00:11\n", "     ---------------------------------- --- 17.8/19.5 MB 161.2 kB/s eta 0:00:11\n", "     ---------------------------------- --- 17.8/19.5 MB 161.2 kB/s eta 0:00:11\n", "     ---------------------------------- --- 17.8/19.5 MB 161.2 kB/s eta 0:00:11\n", "     ---------------------------------- --- 17.8/19.5 MB 161.2 kB/s eta 0:00:11\n", "     ---------------------------------- --- 17.8/19.5 MB 161.2 kB/s eta 0:00:11\n", "     ----------------------------------- -- 18.1/19.5 MB 151.2 kB/s eta 0:00:10\n", "     ----------------------------------- -- 18.1/19.5 MB 151.2 kB/s eta 0:00:10\n", "     ----------------------------------- -- 18.1/19.5 MB 151.2 kB/s eta 0:00:10\n", "     ----------------------------------- -- 18.1/19.5 MB 151.2 kB/s eta 0:00:10\n", "     ----------------------------------- -- 18.4/19.5 MB 152.1 kB/s eta 0:00:08\n", "     ----------------------------------- -- 18.4/19.5 MB 152.1 kB/s eta 0:00:08\n", "     ----------------------------------- -- 18.4/19.5 MB 152.1 kB/s eta 0:00:08\n", "     ----------------------------------- -- 18.4/19.5 MB 152.1 kB/s eta 0:00:08\n", "     ------------------------------------ - 18.6/19.5 MB 153.3 kB/s eta 0:00:06\n", "     ------------------------------------ - 18.6/19.5 MB 153.3 kB/s eta 0:00:06\n", "     ------------------------------------ - 18.6/19.5 MB 153.3 kB/s eta 0:00:06\n", "     ------------------------------------ - 18.6/19.5 MB 153.3 kB/s eta 0:00:06\n", "     ------------------------------------ - 18.6/19.5 MB 153.3 kB/s eta 0:00:06\n", "     ------------------------------------ - 18.6/19.5 MB 153.3 kB/s eta 0:00:06\n", "     ------------------------------------ - 18.9/19.5 MB 151.6 kB/s eta 0:00:05\n", "     ------------------------------------ - 18.9/19.5 MB 151.6 kB/s eta 0:00:05\n", "     ------------------------------------ - 18.9/19.5 MB 151.6 kB/s eta 0:00:05\n", "     ------------------------------------ - 18.9/19.5 MB 151.6 kB/s eta 0:00:05\n", "     ------------------------------------ - 18.9/19.5 MB 151.6 kB/s eta 0:00:05\n", "     ------------------------------------ - 18.9/19.5 MB 151.6 kB/s eta 0:00:05\n", "     -------------------------------------  19.1/19.5 MB 151.4 kB/s eta 0:00:03\n", "     -------------------------------------  19.1/19.5 MB 151.4 kB/s eta 0:00:03\n", "     -------------------------------------  19.1/19.5 MB 151.4 kB/s eta 0:00:03\n", "     -------------------------------------  19.1/19.5 MB 151.4 kB/s eta 0:00:03\n", "     -------------------------------------  19.1/19.5 MB 151.4 kB/s eta 0:00:03\n", "     -------------------------------------  19.1/19.5 MB 151.4 kB/s eta 0:00:03\n", "     -------------------------------------  19.4/19.5 MB 150.8 kB/s eta 0:00:01\n", "     -------------------------------------  19.4/19.5 MB 150.8 kB/s eta 0:00:01\n", "     -------------------------------------  19.4/19.5 MB 150.8 kB/s eta 0:00:01\n", "     -------------------------------------  19.4/19.5 MB 150.8 kB/s eta 0:00:01\n", "     -------------------------------------- 19.5/19.5 MB 148.1 kB/s eta 0:00:00\n", "Collecting langchain-chroma>=0.2.0 (from langchain-graph-retriever[chroma])\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/61/70/384bcdcaac7cb74e239a8411a9bd16079c7e0d9174391d66c635c45514f4/langchain_chroma-0.2.4-py3-none-any.whl (11 kB)\n", "Requirement already satisfied: build>=1.0.3 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=0.5.23->langchain-graph-retriever[chroma]) (1.2.2.post1)\n", "Collecting pybase64>=1.4.1 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/c0/f5/a7eed9f3692209a9869a28bdd92deddf8cbffb06b40954f89f4577e5c96e/pybase64-1.4.1-cp313-cp313-win_amd64.whl (36 kB)\n", "Requirement already satisfied: uvicorn>=0.18.3 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from uvicorn[standard]>=0.18.3->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (0.34.2)\n", "Requirement already satisfied: numpy>=1.22.5 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=0.5.23->langchain-graph-retriever[chroma]) (2.3.1)\n", "Collecting posthog<6.0.0,>=2.4.0 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/4f/98/e480cab9a08d1c09b1c59a93dade92c1bb7544826684ff2acbfd10fcfbd4/posthog-5.4.0-py3-none-any.whl (105 kB)\n", "Collecting onnxruntime>=1.14.1 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/3e/89/2f64e250945fa87140fb917ba377d6d0e9122e029c8512f389a9b7f953f4/onnxruntime-1.22.0-cp313-cp313-win_amd64.whl (12.7 MB)\n", "Collecting opentelemetry-api>=1.2.0 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/a5/3a/2ba85557e8dc024c0842ad22c570418dc02c36cbd1ab4b832a93edf071b8/opentelemetry_api-1.34.1-py3-none-any.whl (65 kB)\n", "Collecting opentelemetry-exporter-otlp-proto-grpc>=1.2.0 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/b4/42/0a4dd47e7ef54edf670c81fc06a83d68ea42727b82126a1df9dd0477695d/opentelemetry_exporter_otlp_proto_grpc-1.34.1-py3-none-any.whl (18 kB)\n", "Collecting opentelemetry-sdk>=1.2.0 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/07/1b/def4fe6aa73f483cabf4c748f4c25070d5f7604dcc8b52e962983491b29e/opentelemetry_sdk-1.34.1-py3-none-any.whl (118 kB)\n", "Collecting tokenizers>=0.13.2 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/13/c3/cc2755ee10be859c4338c962a35b9a663788c0c0b50c0bdd8078fb6870cf/tokenizers-0.21.2-cp39-abi3-win_amd64.whl (2.5 MB)\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.3/2.5 MB ? eta -:--:--\n", "     -------- ------------------------------- 0.5/2.5 MB 176.0 kB/s eta 0:00:12\n", "     -------- ------------------------------- 0.5/2.5 MB 176.0 kB/s eta 0:00:12\n", "     -------- ------------------------------- 0.5/2.5 MB 176.0 kB/s eta 0:00:12\n", "     -------- ------------------------------- 0.5/2.5 MB 176.0 kB/s eta 0:00:12\n", "     -------- ------------------------------- 0.5/2.5 MB 176.0 kB/s eta 0:00:12\n", "     -------- ------------------------------- 0.5/2.5 MB 176.0 kB/s eta 0:00:12\n", "     ------------ --------------------------- 0.8/2.5 MB 186.1 kB/s eta 0:00:10\n", "     ------------ --------------------------- 0.8/2.5 MB 186.1 kB/s eta 0:00:10\n", "     ------------ --------------------------- 0.8/2.5 MB 186.1 kB/s eta 0:00:10\n", "     ------------ --------------------------- 0.8/2.5 MB 186.1 kB/s eta 0:00:10\n", "     ------------ --------------------------- 0.8/2.5 MB 186.1 kB/s eta 0:00:10\n", "     ------------ --------------------------- 0.8/2.5 MB 186.1 kB/s eta 0:00:10\n", "     ------------ --------------------------- 0.8/2.5 MB 186.1 kB/s eta 0:00:10\n", "     ---------------- ----------------------- 1.0/2.5 MB 189.7 kB/s eta 0:00:08\n", "     ---------------- ----------------------- 1.0/2.5 MB 189.7 kB/s eta 0:00:08\n", "     ---------------- ----------------------- 1.0/2.5 MB 189.7 kB/s eta 0:00:08\n", "     ---------------- ----------------------- 1.0/2.5 MB 189.7 kB/s eta 0:00:08\n", "     ---------------- ----------------------- 1.0/2.5 MB 189.7 kB/s eta 0:00:08\n", "     ---------------- ----------------------- 1.0/2.5 MB 189.7 kB/s eta 0:00:08\n", "     -------------------- ------------------- 1.3/2.5 MB 195.6 kB/s eta 0:00:07\n", "     -------------------- ------------------- 1.3/2.5 MB 195.6 kB/s eta 0:00:07\n", "     -------------------- ------------------- 1.3/2.5 MB 195.6 kB/s eta 0:00:07\n", "     ------------------------- -------------- 1.6/2.5 MB 217.8 kB/s eta 0:00:05\n", "     ------------------------- -------------- 1.6/2.5 MB 217.8 kB/s eta 0:00:05\n", "     ------------------------- -------------- 1.6/2.5 MB 217.8 kB/s eta 0:00:05\n", "     ----------------------------- ---------- 1.8/2.5 MB 231.1 kB/s eta 0:00:03\n", "     ----------------------------- ---------- 1.8/2.5 MB 231.1 kB/s eta 0:00:03\n", "     ----------------------------- ---------- 1.8/2.5 MB 231.1 kB/s eta 0:00:03\n", "     ----------------------------- ---------- 1.8/2.5 MB 231.1 kB/s eta 0:00:03\n", "     ----------------------------- ---------- 1.8/2.5 MB 231.1 kB/s eta 0:00:03\n", "     --------------------------------- ------ 2.1/2.5 MB 239.9 kB/s eta 0:00:02\n", "     --------------------------------- ------ 2.1/2.5 MB 239.9 kB/s eta 0:00:02\n", "     --------------------------------- ------ 2.1/2.5 MB 239.9 kB/s eta 0:00:02\n", "     ------------------------------------- -- 2.4/2.5 MB 250.6 kB/s eta 0:00:01\n", "     ------------------------------------- -- 2.4/2.5 MB 250.6 kB/s eta 0:00:01\n", "     ---------------------------------------- 2.5/2.5 MB 257.9 kB/s eta 0:00:00\n", "Collecting pypika>=0.48.9 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/c7/2c/94ed7b91db81d61d7096ac8f2d325ec562fc75e35f3baea8749c85b28784/PyPika-0.48.9.tar.gz (67 kB)\n", "  Installing build dependencies: started\n", "  Installing build dependencies: finished with status 'done'\n", "  Getting requirements to build wheel: started\n", "  Getting requirements to build wheel: finished with status 'done'\n", "  Preparing metadata (pyproject.toml): started\n", "  Preparing metadata (pyproject.toml): finished with status 'done'\n", "Requirement already satisfied: tqdm>=4.65.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=0.5.23->langchain-graph-retriever[chroma]) (4.67.1)\n", "Collecting overrides>=7.3.1 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Using cached https://pypi.tuna.tsinghua.edu.cn/packages/2c/ab/fc8290c6a4c722e5514d80f62b2dc4c4df1a68a41d1364e625c35990fcf3/overrides-7.7.0-py3-none-any.whl (17 kB)\n", "Collecting importlib-resources (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a4/ed/1f1afb2e9e7f38a545d628f864d562a5ae64fe6f7a10e28ffb9b185b4e89/importlib_resources-6.5.2-py3-none-any.whl (37 kB)\n", "Collecting grpcio>=1.58.0 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/c2/d7/77ac689216daee10de318db5aa1b88d159432dc76a130948a56b3aa671a2/grpcio-1.73.1-cp313-cp313-win_amd64.whl (4.3 MB)\n", "     ---------------------------------------- 0.0/4.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/4.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/4.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/4.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/4.3 MB ? eta -:--:--\n", "     -- ------------------------------------- 0.3/4.3 MB ? eta -:--:--\n", "     -- ------------------------------------- 0.3/4.3 MB ? eta -:--:--\n", "     -- ------------------------------------- 0.3/4.3 MB ? eta -:--:--\n", "     -- ------------------------------------- 0.3/4.3 MB ? eta -:--:--\n", "     ---- ----------------------------------- 0.5/4.3 MB 262.7 kB/s eta 0:00:15\n", "     ---- ----------------------------------- 0.5/4.3 MB 262.7 kB/s eta 0:00:15\n", "     ---- ----------------------------------- 0.5/4.3 MB 262.7 kB/s eta 0:00:15\n", "     ---- ----------------------------------- 0.5/4.3 MB 262.7 kB/s eta 0:00:15\n", "     ------- -------------------------------- 0.8/4.3 MB 293.2 kB/s eta 0:00:13\n", "     ------- -------------------------------- 0.8/4.3 MB 293.2 kB/s eta 0:00:13\n", "     ------- -------------------------------- 0.8/4.3 MB 293.2 kB/s eta 0:00:13\n", "     --------- ------------------------------ 1.0/4.3 MB 332.6 kB/s eta 0:00:10\n", "     --------- ------------------------------ 1.0/4.3 MB 332.6 kB/s eta 0:00:10\n", "     --------- ------------------------------ 1.0/4.3 MB 332.6 kB/s eta 0:00:10\n", "     ------------ --------------------------- 1.3/4.3 MB 347.9 kB/s eta 0:00:09\n", "     ------------ --------------------------- 1.3/4.3 MB 347.9 kB/s eta 0:00:09\n", "     ------------ --------------------------- 1.3/4.3 MB 347.9 kB/s eta 0:00:09\n", "     ------------ --------------------------- 1.3/4.3 MB 347.9 kB/s eta 0:00:09\n", "     ------------ --------------------------- 1.3/4.3 MB 347.9 kB/s eta 0:00:09\n", "     ------------ --------------------------- 1.3/4.3 MB 347.9 kB/s eta 0:00:09\n", "     ------------ --------------------------- 1.3/4.3 MB 347.9 kB/s eta 0:00:09\n", "     ------------ --------------------------- 1.3/4.3 MB 347.9 kB/s eta 0:00:09\n", "     -------------- ------------------------- 1.6/4.3 MB 278.8 kB/s eta 0:00:10\n", "     -------------- ------------------------- 1.6/4.3 MB 278.8 kB/s eta 0:00:10\n", "     -------------- ------------------------- 1.6/4.3 MB 278.8 kB/s eta 0:00:10\n", "     -------------- ------------------------- 1.6/4.3 MB 278.8 kB/s eta 0:00:10\n", "     ---------------- ----------------------- 1.8/4.3 MB 284.2 kB/s eta 0:00:09\n", "     ---------------- ----------------------- 1.8/4.3 MB 284.2 kB/s eta 0:00:09\n", "     ---------------- ----------------------- 1.8/4.3 MB 284.2 kB/s eta 0:00:09\n", "     ---------------- ----------------------- 1.8/4.3 MB 284.2 kB/s eta 0:00:09\n", "     ------------------- -------------------- 2.1/4.3 MB 288.7 kB/s eta 0:00:08\n", "     ------------------- -------------------- 2.1/4.3 MB 288.7 kB/s eta 0:00:08\n", "     ------------------- -------------------- 2.1/4.3 MB 288.7 kB/s eta 0:00:08\n", "     ------------------- -------------------- 2.1/4.3 MB 288.7 kB/s eta 0:00:08\n", "     --------------------- ------------------ 2.4/4.3 MB 291.2 kB/s eta 0:00:07\n", "     --------------------- ------------------ 2.4/4.3 MB 291.2 kB/s eta 0:00:07\n", "     --------------------- ------------------ 2.4/4.3 MB 291.2 kB/s eta 0:00:07\n", "     --------------------- ------------------ 2.4/4.3 MB 291.2 kB/s eta 0:00:07\n", "     --------------------- ------------------ 2.4/4.3 MB 291.2 kB/s eta 0:00:07\n", "     ------------------------ --------------- 2.6/4.3 MB 281.5 kB/s eta 0:00:07\n", "     ------------------------ --------------- 2.6/4.3 MB 281.5 kB/s eta 0:00:07\n", "     ------------------------ --------------- 2.6/4.3 MB 281.5 kB/s eta 0:00:07\n", "     ------------------------ --------------- 2.6/4.3 MB 281.5 kB/s eta 0:00:07\n", "     ------------------------ --------------- 2.6/4.3 MB 281.5 kB/s eta 0:00:07\n", "     -------------------------- ------------- 2.9/4.3 MB 276.0 kB/s eta 0:00:06\n", "     -------------------------- ------------- 2.9/4.3 MB 276.0 kB/s eta 0:00:06\n", "     -------------------------- ------------- 2.9/4.3 MB 276.0 kB/s eta 0:00:06\n", "     -------------------------- ------------- 2.9/4.3 MB 276.0 kB/s eta 0:00:06\n", "     -------------------------- ------------- 2.9/4.3 MB 276.0 kB/s eta 0:00:06\n", "     -------------------------- ------------- 2.9/4.3 MB 276.0 kB/s eta 0:00:06\n", "     -------------------------- ------------- 2.9/4.3 MB 276.0 kB/s eta 0:00:06\n", "     ----------------------------- ---------- 3.1/4.3 MB 265.1 kB/s eta 0:00:05\n", "     ----------------------------- ---------- 3.1/4.3 MB 265.1 kB/s eta 0:00:05\n", "     ----------------------------- ---------- 3.1/4.3 MB 265.1 kB/s eta 0:00:05\n", "     ----------------------------- ---------- 3.1/4.3 MB 265.1 kB/s eta 0:00:05\n", "     ----------------------------- ---------- 3.1/4.3 MB 265.1 kB/s eta 0:00:05\n", "     ----------------------------- ---------- 3.1/4.3 MB 265.1 kB/s eta 0:00:05\n", "     ----------------------------- ---------- 3.1/4.3 MB 265.1 kB/s eta 0:00:05\n", "     ----------------------------- ---------- 3.1/4.3 MB 265.1 kB/s eta 0:00:05\n", "     ------------------------------- -------- 3.4/4.3 MB 248.4 kB/s eta 0:00:04\n", "     ------------------------------- -------- 3.4/4.3 MB 248.4 kB/s eta 0:00:04\n", "     ------------------------------- -------- 3.4/4.3 MB 248.4 kB/s eta 0:00:04\n", "     ------------------------------- -------- 3.4/4.3 MB 248.4 kB/s eta 0:00:04\n", "     ------------------------------- -------- 3.4/4.3 MB 248.4 kB/s eta 0:00:04\n", "     ------------------------------- -------- 3.4/4.3 MB 248.4 kB/s eta 0:00:04\n", "     ------------------------------- -------- 3.4/4.3 MB 248.4 kB/s eta 0:00:04\n", "     ------------------------------- -------- 3.4/4.3 MB 248.4 kB/s eta 0:00:04\n", "     ------------------------------- -------- 3.4/4.3 MB 248.4 kB/s eta 0:00:04\n", "     --------------------------------- ------ 3.7/4.3 MB 236.5 kB/s eta 0:00:03\n", "     --------------------------------- ------ 3.7/4.3 MB 236.5 kB/s eta 0:00:03\n", "     --------------------------------- ------ 3.7/4.3 MB 236.5 kB/s eta 0:00:03\n", "     --------------------------------- ------ 3.7/4.3 MB 236.5 kB/s eta 0:00:03\n", "     --------------------------------- ------ 3.7/4.3 MB 236.5 kB/s eta 0:00:03\n", "     ------------------------------------ --- 3.9/4.3 MB 234.4 kB/s eta 0:00:02\n", "     ------------------------------------ --- 3.9/4.3 MB 234.4 kB/s eta 0:00:02\n", "     ------------------------------------ --- 3.9/4.3 MB 234.4 kB/s eta 0:00:02\n", "     ------------------------------------ --- 3.9/4.3 MB 234.4 kB/s eta 0:00:02\n", "     ------------------------------------ --- 3.9/4.3 MB 234.4 kB/s eta 0:00:02\n", "     ------------------------------------ --- 3.9/4.3 MB 234.4 kB/s eta 0:00:02\n", "     ------------------------------------ --- 3.9/4.3 MB 234.4 kB/s eta 0:00:02\n", "     ------------------------------------ --- 3.9/4.3 MB 234.4 kB/s eta 0:00:02\n", "     -------------------------------------- - 4.2/4.3 MB 228.2 kB/s eta 0:00:01\n", "     -------------------------------------- - 4.2/4.3 MB 228.2 kB/s eta 0:00:01\n", "     -------------------------------------- - 4.2/4.3 MB 228.2 kB/s eta 0:00:01\n", "     -------------------------------------- - 4.2/4.3 MB 228.2 kB/s eta 0:00:01\n", "     ---------------------------------------- 4.3/4.3 MB 225.8 kB/s eta 0:00:00\n", "Collecting bcrypt>=4.0.1 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a9/cf/45fb5261ece3e6b9817d3d82b2f343a505fd58674a92577923bc500bd1aa/bcrypt-4.3.0-cp39-abi3-win_amd64.whl (152 kB)\n", "Requirement already satisfied: typer>=0.9.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=0.5.23->langchain-graph-retriever[chroma]) (0.16.0)\n", "Collecting kubernetes>=28.1.0 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/89/43/d9bebfc3db7dea6ec80df5cb2aad8d274dd18ec2edd6c4f21f32c237cbbb/kubernetes-33.1.0-py2.py3-none-any.whl (1.9 MB)\n", "     ---------------------------------------- 0.0/1.9 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/1.9 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/1.9 MB ? eta -:--:--\n", "     ----- ---------------------------------- 0.3/1.9 MB ? eta -:--:--\n", "     ----- ---------------------------------- 0.3/1.9 MB ? eta -:--:--\n", "     ----- ---------------------------------- 0.3/1.9 MB ? eta -:--:--\n", "     ---------- ----------------------------- 0.5/1.9 MB 435.7 kB/s eta 0:00:04\n", "     ---------- ----------------------------- 0.5/1.9 MB 435.7 kB/s eta 0:00:04\n", "     ---------------- ----------------------- 0.8/1.9 MB 439.1 kB/s eta 0:00:03\n", "     ---------------- ----------------------- 0.8/1.9 MB 439.1 kB/s eta 0:00:03\n", "     ---------------- ----------------------- 0.8/1.9 MB 439.1 kB/s eta 0:00:03\n", "     ---------------- ----------------------- 0.8/1.9 MB 439.1 kB/s eta 0:00:03\n", "     --------------------- ------------------ 1.0/1.9 MB 418.7 kB/s eta 0:00:03\n", "     --------------------- ------------------ 1.0/1.9 MB 418.7 kB/s eta 0:00:03\n", "     --------------------- ------------------ 1.0/1.9 MB 418.7 kB/s eta 0:00:03\n", "     --------------------------- ------------ 1.3/1.9 MB 413.8 kB/s eta 0:00:02\n", "     --------------------------- ------------ 1.3/1.9 MB 413.8 kB/s eta 0:00:02\n", "     --------------------------- ------------ 1.3/1.9 MB 413.8 kB/s eta 0:00:02\n", "     --------------------------- ------------ 1.3/1.9 MB 413.8 kB/s eta 0:00:02\n", "     -------------------------------- ------- 1.6/1.9 MB 377.1 kB/s eta 0:00:01\n", "     -------------------------------- ------- 1.6/1.9 MB 377.1 kB/s eta 0:00:01\n", "     -------------------------------- ------- 1.6/1.9 MB 377.1 kB/s eta 0:00:01\n", "     -------------------------------- ------- 1.6/1.9 MB 377.1 kB/s eta 0:00:01\n", "     -------------------------------- ------- 1.6/1.9 MB 377.1 kB/s eta 0:00:01\n", "     -------------------------------- ------- 1.6/1.9 MB 377.1 kB/s eta 0:00:01\n", "     ------------------------------------- -- 1.8/1.9 MB 330.9 kB/s eta 0:00:01\n", "     ------------------------------------- -- 1.8/1.9 MB 330.9 kB/s eta 0:00:01\n", "     ---------------------------------------- 1.9/1.9 MB 324.3 kB/s eta 0:00:00\n", "Requirement already satisfied: tenacity>=8.2.3 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=0.5.23->langchain-graph-retriever[chroma]) (9.1.2)\n", "Requirement already satisfied: pyyaml>=6.0.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=0.5.23->langchain-graph-retriever[chroma]) (6.0.2)\n", "Collecting mmh3>=4.0.1 (from chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/09/33/9fb90ef822f7b734955a63851907cf72f8a3f9d8eb3c5706bfa6772a2a77/mmh3-5.1.0-cp313-cp313-win_amd64.whl (41 kB)\n", "Requirement already satisfied: orjson>=3.9.12 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=0.5.23->langchain-graph-retriever[chroma]) (3.10.18)\n", "Requirement already satisfied: httpx>=0.27.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=0.5.23->langchain-graph-retriever[chroma]) (0.28.1)\n", "Requirement already satisfied: rich>=10.11.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=0.5.23->langchain-graph-retriever[chroma]) (14.0.0)\n", "Requirement already satisfied: jsonschema>=4.19.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from chromadb>=0.5.23->langchain-graph-retriever[chroma]) (4.24.0)\n", "Requirement already satisfied: requests<3.0,>=2.7 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from posthog<6.0.0,>=2.4.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (2.32.3)\n", "Requirement already satisfied: six>=1.5 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from posthog<6.0.0,>=2.4.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (1.17.0)\n", "Requirement already satisfied: python-dateutil>=2.2 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from posthog<6.0.0,>=2.4.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (2.9.0.post0)\n", "Requirement already satisfied: distro>=1.5.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from posthog<6.0.0,>=2.4.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (1.9.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from requests<3.0,>=2.7->posthog<6.0.0,>=2.4.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from requests<3.0,>=2.7->posthog<6.0.0,>=2.4.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from requests<3.0,>=2.7->posthog<6.0.0,>=2.4.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from requests<3.0,>=2.7->posthog<6.0.0,>=2.4.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (2025.4.26)\n", "Requirement already satisfied: packaging>=19.1 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from build>=1.0.3->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (24.2)\n", "Requirement already satisfied: pyproject_hooks in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from build>=1.0.3->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (1.2.0)\n", "Requirement already satisfied: colorama in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from build>=1.0.3->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (0.4.6)\n", "Requirement already satisfied: anyio in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from httpx>=0.27.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (4.9.0)\n", "Requirement already satisfied: httpcore==1.* in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from httpx>=0.27.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from httpcore==1.*->httpx>=0.27.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (0.16.0)\n", "Requirement already satisfied: attrs>=22.2.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from jsonschema>=4.19.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (25.3.0)\n", "Requirement already satisfied: jsonschema-specifications>=2023.03.6 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from jsonschema>=4.19.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (2025.4.1)\n", "Requirement already satisfied: referencing>=0.28.4 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from jsonschema>=4.19.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (0.36.2)\n", "Requirement already satisfied: rpds-py>=0.7.1 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from jsonschema>=4.19.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (0.25.1)\n", "Collecting google-auth>=1.0.1 (from kubernetes>=28.1.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/17/63/b19553b658a1692443c62bd07e5868adaa0ad746a0751ba62c59568cd45b/google_auth-2.40.3-py2.py3-none-any.whl (216 kB)\n", "Requirement already satisfied: websocket-client!=0.40.0,!=0.41.*,!=0.42.*,>=0.32.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from kubernetes>=28.1.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (1.8.0)\n", "Collecting requests-oauthlib (from kubernetes>=28.1.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/3b/5d/63d4ae3b9daea098d5d6f5da83984853c1bbacd5dc826764b249fe119d24/requests_oauthlib-2.0.0-py2.py3-none-any.whl (24 kB)\n", "Collecting oauthlib>=3.2.2 (from kubernetes>=28.1.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/be/9c/92789c596b8df838baa98fa71844d84283302f7604ed565dafe5a6b5041a/oauthlib-3.3.1-py3-none-any.whl (160 kB)\n", "Collecting durationpy>=0.7 (from kubernetes>=28.1.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/b0/0d/9feae160378a3553fa9a339b0e9c1a048e147a4127210e286ef18b730f03/durationpy-0.10-py3-none-any.whl (3.9 kB)\n", "Collecting cachetools<6.0,>=2.0.0 (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/72/76/20fa66124dbe6be5cafeb312ece67de6b61dd91a0247d1ea13db4ebb33c2/cachetools-5.5.2-py3-none-any.whl (10 kB)\n", "Collecting pyasn1-modules>=0.2.1 (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/47/8d/d529b5d697919ba8c11ad626e835d4039be708a35b0d22de83a269a6682c/pyasn1_modules-0.4.2-py3-none-any.whl (181 kB)\n", "Collecting rsa<5,>=3.1.4 (from google-auth>=1.0.1->kubernetes>=28.1.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/64/8d/0133e4eb4beed9e425d9a98ed6e081a55d195481b7632472be1af08d2f6b/rsa-4.9.1-py3-none-any.whl (34 kB)\n", "Collecting pyasn1>=0.1.3 (from rsa<5,>=3.1.4->google-auth>=1.0.1->kubernetes>=28.1.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/c8/f1/d6a797abb14f6283c0ddff96bbdd46937f64122b8c925cab503dd37f8214/pyasn1-0.6.1-py3-none-any.whl (83 kB)\n", "Requirement already satisfied: langsmith>=0.3.45 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langchain-core>=0.3.29->langchain-graph-retriever[chroma]) (0.3.45)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langchain-core>=0.3.29->langchain-graph-retriever[chroma]) (1.33)\n", "Requirement already satisfied: jsonpointer>=1.9 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from jsonpatch<2.0,>=1.33->langchain-core>=0.3.29->langchain-graph-retriever[chroma]) (3.0.0)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langsmith>=0.3.45->langchain-core>=0.3.29->langchain-graph-retriever[chroma]) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from langsmith>=0.3.45->langchain-core>=0.3.29->langchain-graph-retriever[chroma]) (0.23.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from pydantic>=2.10.4->langchain-graph-retriever[chroma]) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from pydantic>=2.10.4->langchain-graph-retriever[chroma]) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from pydantic>=2.10.4->langchain-graph-retriever[chroma]) (0.4.1)\n", "Collecting coloredlogs (from onnxruntime>=1.14.1->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a7/06/3d6badcf13db419e25b07041d9c7b4a2c331d3f4e7134445ec5df57714cd/coloredlogs-15.0.1-py2.py3-none-any.whl (46 kB)\n", "Collecting flatbuffers (from onnxruntime>=1.14.1->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/b8/25/155f9f080d5e4bc0082edfda032ea2bc2b8fab3f4d25d46c1e9dd22a1a89/flatbuffers-25.2.10-py2.py3-none-any.whl (30 kB)\n", "Collecting protobuf (from onnxruntime>=1.14.1->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/44/3a/b15c4347dd4bf3a1b0ee882f384623e2063bb5cf9fa9d57990a4f7df2fb6/protobuf-6.31.1-cp310-abi3-win_amd64.whl (435 kB)\n", "Collecting sympy (from onnxruntime>=1.14.1->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/a2/09/77d55d46fd61b4a135c444fc97158ef34a095e5681d0a6c10b75bf356191/sympy-1.14.0-py3-none-any.whl (6.3 MB)\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "     - -------------------------------------- 0.3/6.3 MB ? eta -:--:--\n", "     - -------------------------------------- 0.3/6.3 MB ? eta -:--:--\n", "     - -------------------------------------- 0.3/6.3 MB ? eta -:--:--\n", "     - -------------------------------------- 0.3/6.3 MB ? eta -:--:--\n", "     --- ------------------------------------ 0.5/6.3 MB 288.9 kB/s eta 0:00:20\n", "     --- ------------------------------------ 0.5/6.3 MB 288.9 kB/s eta 0:00:20\n", "     --- ------------------------------------ 0.5/6.3 MB 288.9 kB/s eta 0:00:20\n", "     --- ------------------------------------ 0.5/6.3 MB 288.9 kB/s eta 0:00:20\n", "     ---- ----------------------------------- 0.8/6.3 MB 304.2 kB/s eta 0:00:19\n", "     ---- ----------------------------------- 0.8/6.3 MB 304.2 kB/s eta 0:00:19\n", "     ---- ----------------------------------- 0.8/6.3 MB 304.2 kB/s eta 0:00:19\n", "     ---- ----------------------------------- 0.8/6.3 MB 304.2 kB/s eta 0:00:19\n", "     ---- ----------------------------------- 0.8/6.3 MB 304.2 kB/s eta 0:00:19\n", "     ------ --------------------------------- 1.0/6.3 MB 293.3 kB/s eta 0:00:18\n", "     ------ --------------------------------- 1.0/6.3 MB 293.3 kB/s eta 0:00:18\n", "     ------ --------------------------------- 1.0/6.3 MB 293.3 kB/s eta 0:00:18\n", "     ------ --------------------------------- 1.0/6.3 MB 293.3 kB/s eta 0:00:18\n", "     -------- ------------------------------- 1.3/6.3 MB 299.9 kB/s eta 0:00:17\n", "     -------- ------------------------------- 1.3/6.3 MB 299.9 kB/s eta 0:00:17\n", "     -------- ------------------------------- 1.3/6.3 MB 299.9 kB/s eta 0:00:17\n", "     -------- ------------------------------- 1.3/6.3 MB 299.9 kB/s eta 0:00:17\n", "     --------- ------------------------------ 1.6/6.3 MB 295.9 kB/s eta 0:00:16\n", "     --------- ------------------------------ 1.6/6.3 MB 295.9 kB/s eta 0:00:16\n", "     --------- ------------------------------ 1.6/6.3 MB 295.9 kB/s eta 0:00:16\n", "     --------- ------------------------------ 1.6/6.3 MB 295.9 kB/s eta 0:00:16\n", "     --------- ------------------------------ 1.6/6.3 MB 295.9 kB/s eta 0:00:16\n", "     --------- ------------------------------ 1.6/6.3 MB 295.9 kB/s eta 0:00:16\n", "     ----------- ---------------------------- 1.8/6.3 MB 274.4 kB/s eta 0:00:17\n", "     ----------- ---------------------------- 1.8/6.3 MB 274.4 kB/s eta 0:00:17\n", "     ----------- ---------------------------- 1.8/6.3 MB 274.4 kB/s eta 0:00:17\n", "     ----------- ---------------------------- 1.8/6.3 MB 274.4 kB/s eta 0:00:17\n", "     ----------- ---------------------------- 1.8/6.3 MB 274.4 kB/s eta 0:00:17\n", "     ----------- ---------------------------- 1.8/6.3 MB 274.4 kB/s eta 0:00:17\n", "     ------------- -------------------------- 2.1/6.3 MB 261.0 kB/s eta 0:00:17\n", "     ------------- -------------------------- 2.1/6.3 MB 261.0 kB/s eta 0:00:17\n", "     ------------- -------------------------- 2.1/6.3 MB 261.0 kB/s eta 0:00:17\n", "     ------------- -------------------------- 2.1/6.3 MB 261.0 kB/s eta 0:00:17\n", "     ------------- -------------------------- 2.1/6.3 MB 261.0 kB/s eta 0:00:17\n", "     -------------- ------------------------- 2.4/6.3 MB 260.2 kB/s eta 0:00:16\n", "     -------------- ------------------------- 2.4/6.3 MB 260.2 kB/s eta 0:00:16\n", "     -------------- ------------------------- 2.4/6.3 MB 260.2 kB/s eta 0:00:16\n", "     ---------------- ----------------------- 2.6/6.3 MB 272.7 kB/s eta 0:00:14\n", "     ---------------- ----------------------- 2.6/6.3 MB 272.7 kB/s eta 0:00:14\n", "     ---------------- ----------------------- 2.6/6.3 MB 272.7 kB/s eta 0:00:14\n", "     ------------------ --------------------- 2.9/6.3 MB 282.0 kB/s eta 0:00:13\n", "     ------------------ --------------------- 2.9/6.3 MB 282.0 kB/s eta 0:00:13\n", "     ------------------ --------------------- 2.9/6.3 MB 282.0 kB/s eta 0:00:13\n", "     ------------------ --------------------- 2.9/6.3 MB 282.0 kB/s eta 0:00:13\n", "     ------------------- -------------------- 3.1/6.3 MB 286.8 kB/s eta 0:00:11\n", "     ------------------- -------------------- 3.1/6.3 MB 286.8 kB/s eta 0:00:11\n", "     ------------------- -------------------- 3.1/6.3 MB 286.8 kB/s eta 0:00:11\n", "     ------------------- -------------------- 3.1/6.3 MB 286.8 kB/s eta 0:00:11\n", "     --------------------- ------------------ 3.4/6.3 MB 288.1 kB/s eta 0:00:11\n", "     --------------------- ------------------ 3.4/6.3 MB 288.1 kB/s eta 0:00:11\n", "     --------------------- ------------------ 3.4/6.3 MB 288.1 kB/s eta 0:00:11\n", "     ----------------------- ---------------- 3.7/6.3 MB 294.7 kB/s eta 0:00:09\n", "     ----------------------- ---------------- 3.7/6.3 MB 294.7 kB/s eta 0:00:09\n", "     ----------------------- ---------------- 3.7/6.3 MB 294.7 kB/s eta 0:00:09\n", "     ------------------------ --------------- 3.9/6.3 MB 302.3 kB/s eta 0:00:08\n", "     ------------------------ --------------- 3.9/6.3 MB 302.3 kB/s eta 0:00:08\n", "     -------------------------- ------------- 4.2/6.3 MB 311.9 kB/s eta 0:00:07\n", "     -------------------------- ------------- 4.2/6.3 MB 311.9 kB/s eta 0:00:07\n", "     -------------------------- ------------- 4.2/6.3 MB 311.9 kB/s eta 0:00:07\n", "     ---------------------------- ----------- 4.5/6.3 MB 317.6 kB/s eta 0:00:06\n", "     ---------------------------- ----------- 4.5/6.3 MB 317.6 kB/s eta 0:00:06\n", "     ---------------------------- ----------- 4.5/6.3 MB 317.6 kB/s eta 0:00:06\n", "     ---------------------------- ----------- 4.5/6.3 MB 317.6 kB/s eta 0:00:06\n", "     ----------------------------- ---------- 4.7/6.3 MB 317.2 kB/s eta 0:00:05\n", "     ----------------------------- ---------- 4.7/6.3 MB 317.2 kB/s eta 0:00:05\n", "     ------------------------------- -------- 5.0/6.3 MB 322.7 kB/s eta 0:00:05\n", "     ------------------------------- -------- 5.0/6.3 MB 322.7 kB/s eta 0:00:05\n", "     ------------------------------- -------- 5.0/6.3 MB 322.7 kB/s eta 0:00:05\n", "     ------------------------------- -------- 5.0/6.3 MB 322.7 kB/s eta 0:00:05\n", "     --------------------------------- ------ 5.2/6.3 MB 324.3 kB/s eta 0:00:04\n", "     --------------------------------- ------ 5.2/6.3 MB 324.3 kB/s eta 0:00:04\n", "     --------------------------------- ------ 5.2/6.3 MB 324.3 kB/s eta 0:00:04\n", "     ---------------------------------- ----- 5.5/6.3 MB 328.2 kB/s eta 0:00:03\n", "     ---------------------------------- ----- 5.5/6.3 MB 328.2 kB/s eta 0:00:03\n", "     ---------------------------------- ----- 5.5/6.3 MB 328.2 kB/s eta 0:00:03\n", "     ------------------------------------ --- 5.8/6.3 MB 330.8 kB/s eta 0:00:02\n", "     ------------------------------------ --- 5.8/6.3 MB 330.8 kB/s eta 0:00:02\n", "     ------------------------------------ --- 5.8/6.3 MB 330.8 kB/s eta 0:00:02\n", "     -------------------------------------- - 6.0/6.3 MB 333.8 kB/s eta 0:00:01\n", "     -------------------------------------- - 6.0/6.3 MB 333.8 kB/s eta 0:00:01\n", "     -------------------------------------- - 6.0/6.3 MB 333.8 kB/s eta 0:00:01\n", "     ---------------------------------------  6.3/6.3 MB 335.1 kB/s eta 0:00:01\n", "     ---------------------------------------- 6.3/6.3 MB 334.4 kB/s eta 0:00:00\n", "Collecting importlib-metadata<8.8.0,>=6.0 (from opentelemetry-api>=1.2.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/20/b0/36bd937216ec521246249be3bf9855081de4c5e06a0c9b4219dbeda50373/importlib_metadata-8.7.0-py3-none-any.whl (27 kB)\n", "Collecting zipp>=3.20 (from importlib-metadata<8.8.0,>=6.0->opentelemetry-api>=1.2.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/2e/54/647ade08bf0db230bfea292f893923872fd20be6ac6f53b2b936ba839d75/zipp-3.23.0-py3-none-any.whl (10 kB)\n", "Collecting googleapis-common-protos~=1.52 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/86/f1/62a193f0227cf15a920390abe675f386dec35f7ae3ffe6da582d3ade42c7/googleapis_common_protos-1.70.0-py3-none-any.whl (294 kB)\n", "Collecting opentelemetry-exporter-otlp-proto-common==1.34.1 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/72/e8/8b292a11cc8d8d87ec0c4089ae21b6a58af49ca2e51fa916435bc922fdc7/opentelemetry_exporter_otlp_proto_common-1.34.1-py3-none-any.whl (18 kB)\n", "Collecting opentelemetry-proto==1.34.1 (from opentelemetry-exporter-otlp-proto-grpc>=1.2.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/28/ab/4591bfa54e946350ce8b3f28e5c658fe9785e7cd11e9c11b1671a867822b/opentelemetry_proto-1.34.1-py3-none-any.whl (55 kB)\n", "Collecting protobuf (from onnxruntime>=1.14.1->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/81/7f/73cefb093e1a2a7c3ffd839e6f9fcafb7a427d300c7f8aef9c64405d8ac6/protobuf-5.29.5-cp310-abi3-win_amd64.whl (434 kB)\n", "Collecting opentelemetry-semantic-conventions==0.55b1 (from opentelemetry-sdk>=1.2.0->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/1a/89/267b0af1b1d0ba828f0e60642b6a5116ac1fd917cde7fc02821627029bd1/opentelemetry_semantic_conventions-0.55b1-py3-none-any.whl (196 kB)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from rich>=10.11.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from rich>=10.11.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (2.19.1)\n", "Requirement already satisfied: mdurl~=0.1 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (0.1.2)\n", "Collecting huggingface-hub<1.0,>=0.16.4 (from tokenizers>=0.13.2->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/44/f4/5f3f22e762ad1965f01122b42dae5bf0e009286e2dba601ce1d0dba72424/huggingface_hub-0.33.2-py3-none-any.whl (515 kB)\n", "Requirement already satisfied: filelock in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (3.18.0)\n", "Collecting fsspec>=2023.5.0 (from huggingface-hub<1.0,>=0.16.4->tokenizers>=0.13.2->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/bb/61/78c7b3851add1481b048b5fdc29067397a1784e2910592bc81bb3f608635/fsspec-2025.5.1-py3-none-any.whl (199 kB)\n", "Requirement already satisfied: click>=8.0.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from typer>=0.9.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (8.2.1)\n", "Requirement already satisfied: shellingham>=1.3.0 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from typer>=0.9.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (1.5.4)\n", "Collecting httptools>=0.6.3 (from uvicorn[standard]>=0.18.3->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/4d/dc/7decab5c404d1d2cdc1bb330b1bf70e83d6af0396fd4fc76fc60c0d522bf/httptools-0.6.4-cp313-cp313-win_amd64.whl (87 kB)\n", "Requirement already satisfied: python-dotenv>=0.13 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from uvicorn[standard]>=0.18.3->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (1.1.0)\n", "Collecting watchfiles>=0.13 (from uvicorn[standard]>=0.18.3->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/25/41/2dd88054b849aa546dbeef5696019c58f8e0774f4d1c42123273304cdb2e/watchfiles-1.1.0-cp313-cp313-win_amd64.whl (292 kB)\n", "Collecting websockets>=10.4 (from uvicorn[standard]>=0.18.3->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/1b/6c/c65773d6cab416a64d191d6ee8a8b1c68a09970ea6909d16965d26bfed1e/websockets-15.0.1-cp313-cp313-win_amd64.whl (176 kB)\n", "Requirement already satisfied: sniffio>=1.1 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from anyio->httpx>=0.27.0->chromadb>=0.5.23->langchain-graph-retriever[chroma]) (1.3.1)\n", "Collecting humanfriendly>=9.1 (from coloredlogs->onnxruntime>=1.14.1->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/f0/0f/310fb31e39e2d734ccaa2c0fb981ee41f7bd5056ce9bc29b2248bd569169/humanfriendly-10.0-py2.py3-none-any.whl (86 kB)\n", "Collecting pyreadline3 (from humanfriendly>=9.1->coloredlogs->onnxruntime>=1.14.1->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/5a/dc/491b7661614ab97483abf2056be1deee4dc2490ecbf7bff9ab5cdbac86e1/pyreadline3-3.5.4-py3-none-any.whl (83 kB)\n", "Requirement already satisfied: pytest>=8.3.4 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from graph-retriever->langchain-graph-retriever[chroma]) (8.4.1)\n", "Requirement already satisfied: iniconfig>=1 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from pytest>=8.3.4->graph-retriever->langchain-graph-retriever[chroma]) (2.1.0)\n", "Requirement already satisfied: pluggy<2,>=1.5 in d:\\anaconda3\\envs\\py313\\lib\\site-packages (from pytest>=8.3.4->graph-retriever->langchain-graph-retriever[chroma]) (1.6.0)\n", "Collecting mpmath<1.4,>=1.1.0 (from sympy->onnxruntime>=1.14.1->chromadb>=0.5.23->langchain-graph-retriever[chroma])\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/43/e3/7d92a15f894aa0c9c4b49b8ee9ac9850d6e63b03c9c32c0367a13ae62209/mpmath-1.3.0-py3-none-any.whl (536 kB)\n", "     ---------------------------------------- 0.0/536.2 kB ? eta -:--:--\n", "     ---------------------------------------- 0.0/536.2 kB ? eta -:--:--\n", "     ---------------------------------------- 0.0/536.2 kB ? eta -:--:--\n", "     ------------------- -------------------- 262.1/536.2 kB ? eta -:--:--\n", "     ------------------- -------------------- 262.1/536.2 kB ? eta -:--:--\n", "     ------------------- -------------------- 262.1/536.2 kB ? eta -:--:--\n", "     ------------------------------------ 536.2/536.2 kB 443.2 kB/s eta 0:00:00\n", "Building wheels for collected packages: pypika\n", "  Building wheel for pypika (pyproject.toml): started\n", "  Building wheel for pypika (pyproject.toml): finished with status 'done'\n", "  Created wheel for pypika: filename=pypika-0.48.9-py2.py3-none-any.whl size=53916 sha256=d97beee6cdffe82f9f29a647c21ca060af4cb93288da0efe6e15ae76afea21da\n", "  Stored in directory: c:\\users\\<USER>\\appdata\\local\\pip\\cache\\wheels\\e7\\2c\\cd\\e86b5c78629cb987a041826b3b5709ceba6d12e4b5d45a616a\n", "Successfully built pypika\n", "Installing collected packages: pypika, mpmath, flatbuffers, durationpy, zipp, websockets, sympy, pyreadline3, pybase64, pyasn1, protobuf, overrides, oauthlib, networkx, mmh3, importlib-resources, httptools, grpcio, fsspec, cachetools, bcrypt, watchfiles, rsa, requests-oauthlib, pyasn1-modules, posthog, opentelemetry-proto, importlib-metadata, humanfriendly, huggingface-hub, googleapis-common-protos, tokenizers, opentelemetry-exporter-otlp-proto-common, opentelemetry-api, google-auth, coloredlogs, opentelemetry-semantic-conventions, onnxruntime, kubernetes, opentelemetry-sdk, langchain-graph-retriever, opentelemetry-exporter-otlp-proto-grpc, chromadb, langchain-chroma\n", "\n", "    ---------------------------------------  1/44 [mpmath]\n", "    ---------------------------------------  1/44 [mpmath]\n", "    ---------------------------------------  1/44 [mpmath]\n", "    ---------------------------------------  1/44 [mpmath]\n", "   --- ------------------------------------  4/44 [zipp]\n", "   ---- -----------------------------------  5/44 [websockets]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ----- ----------------------------------  6/44 [sympy]\n", "   ------ ---------------------------------  7/44 [pyreadline3]\n", "   -------- -------------------------------  9/44 [pyasn1]\n", "   --------- ------------------------------ 10/44 [protobuf]\n", "   --------- ------------------------------ 10/44 [protobuf]\n", "   ---------- ----------------------------- 12/44 [oauthlib]\n", "   ---------- ----------------------------- 12/44 [oauthlib]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ----------- ---------------------------- 13/44 [networkx]\n", "   ------------ --------------------------- 14/44 [mmh3]\n", "   --------------- ------------------------ 17/44 [grpcio]\n", "   --------------- ------------------------ 17/44 [grpcio]\n", "   ---------------- ----------------------- 18/44 [fsspec]\n", "   ---------------- ----------------------- 18/44 [fsspec]\n", "   ------------------- -------------------- 21/44 [watchfiles]\n", "   --------------------- ------------------ 24/44 [pyasn1-modules]\n", "   --------------------- ------------------ 24/44 [pyasn1-modules]\n", "   --------------------- ------------------ 24/44 [pyasn1-modules]\n", "   ---------------------- ----------------- 25/44 [posthog]\n", "   ---------------------- ----------------- 25/44 [posthog]\n", "   ------------------------ --------------- 27/44 [importlib-metadata]\n", "   -------------------------- ------------- 29/44 [huggingface-hub]\n", "   -------------------------- ------------- 29/44 [huggingface-hub]\n", "   -------------------------- ------------- 29/44 [huggingface-hub]\n", "   -------------------------- ------------- 29/44 [huggingface-hub]\n", "   -------------------------- ------------- 29/44 [huggingface-hub]\n", "   --------------------------- ------------ 30/44 [googleapis-common-protos]\n", "   --------------------------- ------------ 30/44 [googleapis-common-protos]\n", "   ------------------------------ --------- 33/44 [opentelemetry-api]\n", "   ------------------------------ --------- 34/44 [google-auth]\n", "   ------------------------------ --------- 34/44 [google-auth]\n", "   --------------------------- ----- 36/44 [opentelemetry-semantic-conventions]\n", "   --------------------------- ----- 36/44 [opentelemetry-semantic-conventions]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   --------------------------------- ------ 37/44 [onnxruntime]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ---------------------------------- ----- 38/44 [kubernetes]\n", "   ----------------------------------- ---- 39/44 [opentelemetry-sdk]\n", "   ----------------------------------- ---- 39/44 [opentelemetry-sdk]\n", "   -------------------------------------- - 42/44 [chromadb]\n", "   -------------------------------------- - 42/44 [chromadb]\n", "   -------------------------------------- - 42/44 [chromadb]\n", "   -------------------------------------- - 42/44 [chromadb]\n", "   -------------------------------------- - 42/44 [chromadb]\n", "   -------------------------------------- - 42/44 [chromadb]\n", "   -------------------------------------- - 42/44 [chromadb]\n", "   ---------------------------------------- 44/44 [langchain-chroma]\n", "\n", "Successfully installed bcrypt-4.3.0 cachetools-5.5.2 chromadb-1.0.15 coloredlogs-15.0.1 durationpy-0.10 flatbuffers-25.2.10 fsspec-2025.5.1 google-auth-2.40.3 googleapis-common-protos-1.70.0 grpcio-1.73.1 httptools-0.6.4 huggingface-hub-0.33.2 humanfriendly-10.0 importlib-metadata-8.7.0 importlib-resources-6.5.2 kubernetes-33.1.0 langchain-chroma-0.2.4 langchain-graph-retriever-0.8.0 mmh3-5.1.0 mpmath-1.3.0 networkx-3.5 oauthlib-3.3.1 onnxruntime-1.22.0 opentelemetry-api-1.34.1 opentelemetry-exporter-otlp-proto-common-1.34.1 opentelemetry-exporter-otlp-proto-grpc-1.34.1 opentelemetry-proto-1.34.1 opentelemetry-sdk-1.34.1 opentelemetry-semantic-conventions-0.55b1 overrides-7.7.0 posthog-5.4.0 protobuf-5.29.5 pyasn1-0.6.1 pyasn1-modules-0.4.2 pybase64-1.4.1 pypika-0.48.9 pyreadline3-3.5.4 requests-oauthlib-2.0.0 rsa-4.9.1 sympy-1.14.0 tokenizers-0.21.2 watchfiles-1.1.0 websockets-15.0.1 zipp-3.23.0\n"]}], "source": ["! pip install \"langchain-graph-retriever[chroma]\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["简单处理下数据"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Document(id='aardvark', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['burrowing', 'nocturnal', 'ants', 'savanna'], 'habitat': 'savanna', 'tags': [{'a': 5, 'b': 7}, {'a': 8, 'b': 10}]}, page_content='the aardvark is a nocturnal mammal known for its burrowing habits and long snout used to sniff out ants.'), Document(id='albatross', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['seabird', 'wingspan', 'ocean'], 'habitat': 'marine', 'tags': [{'a': 5, 'b': 8}, {'a': 8, 'b': 10}]}, page_content='the albatross is a large seabird with the longest wingspan of any bird, allowing it to glide effortlessly over oceans.'), Document(id='alligator', metadata={'type': 'reptile', 'number_of_legs': 4, 'keywords': ['reptile', 'jaws', 'wetlands'], 'diet': 'carnivorous'}, page_content='alligators are large reptiles with powerful jaws and are commonly found in freshwater wetlands.'), Document(id='alpaca', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['wool', 'domesticated', 'friendly'], 'origin': 'south america'}, page_content='alpacas are domesticated mammals valued for their soft wool and friendly demeanor.'), Document(id='ant', metadata={'type': 'insect', 'number_of_legs': 6, 'keywords': ['social', 'colonies', 'strength', 'pollinator'], 'diet': 'omnivorous'}, page_content='ants are social insects that live in colonies and are known for their teamwork and strength.'), Document(id='anteater', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['ants', 'tongue', 'termites'], 'diet': 'insectivore'}, page_content='anteaters use their long tongues to eat thousands of ants and termites each day.'), Document(id='antelope', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['graceful', 'herbivore', 'prey'], 'habitat': 'grasslands'}, page_content='antelopes are graceful herbivorous mammals that are often prey for large predators in the wild.'), Document(id='armadillo', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['protective', 'shell', 'rolling'], 'diet': 'insectivore'}, page_content='armadillos have hard, protective shells and are known for their ability to roll into a ball.'), Document(id='baboon', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['social', 'primates', 'group'], 'diet': 'omnivorous'}, page_content='baboons are highly social primates with complex group dynamics and strong bonds.'), Document(id='badger', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['burrowing', 'defense', 'digging'], 'diet': 'omnivorous'}, page_content='badgers are burrowing mammals known for their aggressive defense and digging skills.'), Document(id='barracuda', metadata={'type': 'fish', 'number_of_legs': 0, 'keywords': ['predatory', 'sharp teeth', 'streamlined'], 'habitat': 'marine'}, page_content='the barracuda is a fierce predatory fish with sharp teeth and streamlined bodies.'), Document(id='bat', metadata={'type': 'mammal', 'number_of_legs': 2, 'keywords': ['flight', 'pollinator', 'pest control'], 'activity': 'nocturnal'}, page_content='bats are the only mammals capable of sustained flight and are essential for pollination and pest control.'), Document(id='bear', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['large', 'habitat', 'omnivore', 'forest-floor'], 'diet': 'omnivorous', 'habitat': 'tundra'}, page_content='bears are large mammals that can be found across many habitats, from forests to tundras.'), Document(id='beaver', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['dam-building', 'ecosystem', 'rivers'], 'diet': 'herbivorous'}, page_content='beavers are known for their dam-building skills, creating entire ecosystems in rivers and streams.'), Document(id='bee', metadata={'type': 'insect', 'number_of_legs': 6, 'keywords': ['pollinator', 'agriculture', 'honey'], 'activity': 'diurnal'}, page_content='bees are an insect that is a vital pollinator, playing a crucial role in agriculture and ecosystems.'), Document(id='beetle', metadata={'type': 'insect', 'number_of_legs': 6, 'keywords': ['diverse', 'species', 'hard shell'], 'diet': 'varied'}, page_content='beetles are one of the most diverse groups of insects, with over 350,000 species identified.'), Document(id='bison', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['herbivore', 'grasslands', 'herds'], 'conservation': 'near-threatened'}, page_content='bison are massive herbivores that once roamed the grasslands of north america in vast herds.'), Document(id='blue jay', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['intelligent', 'loud', 'brightly colored'], 'diet': 'omnivorous'}, page_content='the blue jay is a brightly colored bird known for its intelligence and loud calls.'), Document(id='boar', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['wild', 'tusks', 'pigs'], 'diet': 'omnivorous'}, page_content='boars are wild relatives of pigs, known for their tough hides and tusks.'), Document(id='bobcat', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['wildcat', 'solitary', 'tufted ears', 'forest-floor'], 'habitat': 'forest-floor'}, page_content='the bobcat is a medium-sized wildcat known for its tufted ears and solitary nature.'), Document(id='buffalo', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['herbivore', 'horns', 'grazing'], 'habitat': 'grasslands'}, page_content='buffalo are large herbivorous mammals known for their horns and grazing habits.'), Document(id='butterfly', metadata={'type': 'insect', 'number_of_legs': 6, 'keywords': ['colorful', 'metamorphosis', 'pollinator'], 'activity': 'diurnal'}, page_content='butterflies are colorful insects known for their metamorphosis from caterpillars.'), Document(id='camel', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['desert', 'humps', 'survival'], 'adaptation': 'arid climates'}, page_content='camels are desert mammals known for their humps, which store fat for survival.'), Document(id='capybara', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['rodent', 'social', 'largest'], 'habitat': 'wetlands'}, page_content='capybaras are the largest rodents in the world and are highly social animals.'), Document(id='caribou', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['migratory', 'arctic', 'herbivore', 'tundra'], 'diet': 'herbivorous'}, page_content='caribou, also known as reindeer, are migratory mammals found in arctic regions.'), Document(id='cassowary', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['flightless', 'colorful', 'powerful'], 'habitat': 'rainforest'}, page_content='cassowaries are flightless birds known for their colorful necks and powerful legs.'), Document(id='cat', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['domesticated', 'hunting', 'agile'], 'diet': 'carnivorous'}, page_content='cats are domesticated mammals known for their agility, independence, and hunting skills.'), Document(id='caterpillar', metadata={'type': 'insect', 'number_of_legs': 6, 'keywords': ['larva', 'metamorphosis', 'appetite'], 'diet': 'herbivorous'}, page_content='caterpillars are the larval stage of butterflies and moths, known for their voracious appetites.'), Document(id='chameleon', metadata={'type': 'reptile', 'number_of_legs': 4, 'keywords': ['color change', 'sticky tongue', 'camouflage'], 'habitat': 'forest'}, page_content='chameleons are reptiles known for their ability to change color and their long, sticky tongues.'), Document(id='cheetah', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['fastest', 'speed', 'predator'], 'habitat': 'savanna'}, page_content='cheetahs are the fastest land animals, capable of reaching speeds up to 70 mph.'), Document(id='chicken', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['domesticated', 'eggs', 'meat'], 'diet': 'omnivorous'}, page_content='chickens are domesticated birds raised for their eggs and meat worldwide.'), Document(id='chimpanzee', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['intelligent', 'primates', 'dna'], 'habitat': 'forest'}, page_content='chimpanzees are intelligent primates that share about 98% of their dna with humans.'), Document(id='chinchilla', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['soft fur', 'rodent', 'andes'], 'diet': 'herbivorous'}, page_content='chinchillas are small rodents with incredibly soft fur, native to the andes mountains.'), Document(id='cobra', metadata={'type': 'reptile', 'number_of_legs': 0, 'keywords': ['venomous', 'hood', 'snake'], 'habitat': 'forest-floor'}, page_content='cobras are venomous snakes known for their hood displays and defensive postures.'), Document(id='cockroach', metadata={'type': 'insect', 'number_of_legs': 6, 'keywords': ['resilient', 'extreme conditions', 'scavenger'], 'diet': 'omnivorous'}, page_content='cockroaches are resilient insects capable of surviving in extreme conditions.'), Document(id='coyote', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['howls', 'adaptable', 'cunning'], 'habitat': 'grasslands'}, page_content='coyotes are adaptable mammals known for their distinctive howls and cunning nature.'), Document(id='crab', metadata={'type': 'crustacean', 'number_of_legs': 8, 'keywords': ['hard shell', 'pincers', 'coastal'], 'habitat': 'marine'}, page_content='crabs are crustaceans with hard shells and pincers, commonly found near coastal areas.'), Document(id='crane', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['elegant', 'courtship', 'dance'], 'habitat': 'wetlands'}, page_content='cranes are large, elegant birds known for their elaborate courtship dances.'), Document(id='crocodile', metadata={'type': 'reptile', 'number_of_legs': 4, 'keywords': ['reptile', 'jaws', 'long lifespan'], 'habitat': 'wetlands'}, page_content='crocodiles are large reptiles with powerful jaws and a long lifespan, often living over 70 years.'), Document(id='crow', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['intelligent', 'adaptable', 'problem-solving'], 'diet': 'omnivorous'}, page_content='crows are highly intelligent birds known for their problem-solving skills and adaptability.'), Document(id='deer', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['antlers', 'graceful', 'herbivore'], 'habitat': 'forest-floor'}, page_content='deer are graceful mammals known for their antlers, which are shed and regrown annually.'), Document(id='dingo', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['wild canine', 'australia', 'adaptable'], 'diet': 'carnivorous'}, page_content='dingoes are wild canines native to australia, known for their adaptability and hunting skills.'), Document(id='dog', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['domesticated', 'loyal', 'companion'], 'diet': 'omnivorous'}, page_content=\"dogs are domesticated mammals known as loyal companions and are widely regarded as 'man's best friend.'\"), Document(id='dolphin', metadata={'type': 'mammal', 'number_of_legs': 0, 'keywords': ['intelligent', 'marine', 'playful'], 'habitat': 'ocean'}, page_content='dolphins are intelligent marine mammals known for their playful behavior and communication skills.'), Document(id='donkey', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['hardy', 'working animal', 'strength'], 'diet': 'herbivorous'}, page_content='donkeys are hardy mammals known for their strength and use as working animals.'), Document(id='dove', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['gentle', 'peace', 'symbolism'], 'diet': 'herbivorous'}, page_content='doves are gentle birds often symbolizing peace and love in various cultures.'), Document(id='dragonfly', metadata={'type': 'insect', 'number_of_legs': 6, 'keywords': ['fast-flying', 'vibrant', 'vision'], 'habitat': 'wetlands'}, page_content='dragonflies are fast-flying insects known for their vibrant colors and excellent vision.'), Document(id='duck', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['webbed feet', 'waterfowl', 'quacking'], 'habitat': 'wetlands'}, page_content='ducks are waterfowl birds known for their webbed feet and quacking sounds.'), Document(id='eagle', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['bird of prey', 'sharp vision', 'talons'], 'diet': 'carnivorous'}, page_content='eagles are powerful birds of prey known for their sharp vision and strong talons.'), Document(id='eel', metadata={'type': 'fish', 'number_of_legs': 0, 'keywords': ['elongated', 'marine', 'freshwater'], 'diet': 'carnivorous'}, page_content='eels are elongated fish often found in marine and freshwater environments.'), Document(id='elephant', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['largest', 'intelligent', 'social bonds'], 'diet': 'herbivorous'}, page_content='elephants are the largest land mammals, known for their intelligence and strong social bonds.'), Document(id='elk', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['antlers', 'bugling', 'mating'], 'habitat': 'forest-floor'}, page_content='elk are large deer species known for their impressive antlers and bugling calls during mating season.'), Document(id='emu', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['flightless', 'speed', 'australia'], 'diet': 'omnivorous'}, page_content='emus are large flightless birds native to australia, known for their speed and size.'), Document(id='falcon', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['speed', 'precision', 'bird of prey'], 'diet': 'carnivorous'}, page_content='falcons are birds of prey known for their speed and precision in hunting.'), Document(id='ferret', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['playful', 'curious', 'pet'], 'diet': 'carnivorous'}, page_content='ferrets are small, curious mammals often kept as pets for their playful nature.'), Document(id='finch', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['small', 'songs', 'diverse'], 'diet': 'herbivorous'}, page_content='finches are small birds known for their diverse species and beautiful songs.'), Document(id='fish', metadata={'type': 'fish', 'number_of_legs': 0, 'keywords': ['aquatic', 'gills', 'marine'], 'habitat': 'water'}, page_content='fish are aquatic animals with gills, commonly found in both freshwater and marine environments.'), Document(id='flamingo', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['pink', 'wading', 'long legs'], 'diet': 'omnivorous'}, page_content='flamingos are wading birds famous for their pink feathers and long legs.'), Document(id='fox', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['cunning', 'agile', 'bushy tail'], 'diet': 'omnivorous'}, page_content='foxes are small, agile mammals known for their cunning behavior and bushy tails.'), Document(id='frog', metadata={'type': 'amphibian', 'number_of_legs': 4, 'keywords': ['jumping', 'croaking', 'amphibian'], 'habitat': 'wetlands'}, page_content='frogs are amphibians known for their jumping ability and croaking sounds.'), Document(id='gazelle', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['fast', 'agile', 'grasslands'], 'diet': 'herbivorous', 'habitat': 'savanna'}, page_content='gazelles are fast and agile mammals commonly found in grasslands and savannas.'), Document(id='gecko', metadata={'type': 'reptile', 'number_of_legs': 4, 'keywords': ['climbing', 'small lizard', 'sticky feet'], 'habitat': 'forest'}, page_content='geckos are small lizards known for their ability to climb walls and ceilings with ease.'), Document(id='giraffe', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['tallest', 'long neck', 'spots'], 'diet': 'herbivorous'}, page_content='giraffes are the tallest land animals, known for their long necks and unique spotted patterns.'), Document(id='goat', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['hardy', 'milk', 'meat'], 'diet': 'herbivorous'}, page_content='goats are hardy mammals often kept for their milk, meat, and ability to graze on tough vegetation.'), Document(id='goose', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['waterfowl', 'migration', 'honking'], 'diet': 'herbivorous'}, page_content='geese are large waterfowl known for their migration patterns and loud honking calls.'), Document(id='gorilla', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['strength', 'intelligence', 'primate'], 'habitat': 'forest'}, page_content='gorillas are large primates known for their strength, intelligence, and family-oriented behavior.'), Document(id='grasshopper', metadata={'type': 'insect', 'number_of_legs': 6, 'keywords': ['jumping', 'hind legs', 'chirping'], 'diet': 'herbivorous'}, page_content='grasshoppers are jumping insects known for their powerful hind legs and chirping sounds.'), Document(id='guinea pig', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['pet', 'social', 'gentle'], 'diet': 'herbivorous'}, page_content='guinea pigs are small rodents often kept as pets due to their gentle and social nature.'), Document(id='hamster', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['small rodent', 'cheek pouches', 'food storage'], 'diet': 'omnivorous'}, page_content='hamsters are small rodents known for their cheek pouches used to store food.'), Document(id='hawk', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['bird of prey', 'keen eyesight', 'hunter'], 'diet': 'carnivorous'}, page_content='hawks are birds of prey known for their keen eyesight and hunting prowess.'), Document(id='hedgehog', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['spiky', 'curling', 'small mammal'], 'diet': 'omnivorous', 'habitat': 'grasslands'}, page_content='hedgehogs are small mammals known for their spiky quills and ability to curl into a ball.'), Document(id='heron', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['wading', 'long legs', 'water'], 'habitat': 'wetlands'}, page_content='herons are wading birds known for their long legs and necks, often seen near water.'), Document(id='hippopotamus', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['massive', 'semi-aquatic', 'territorial'], 'habitat': 'rivers'}, page_content='hippopotamuses are large semi-aquatic mammals known for their massive size and territorial behavior.'), Document(id='hornet', metadata={'type': 'insect', 'number_of_legs': 6, 'keywords': ['wasp', 'sting', 'aggressive'], 'diet': 'carnivorous'}, page_content='hornets are large, aggressive wasps known for their painful stings.'), Document(id='horse', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['domesticated', 'riding', 'strong'], 'diet': 'herbivorous'}, page_content='horses are strong, domesticated mammals often used for riding, work, and sports.'), Document(id='hummingbird', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['hovering', 'rapid wings', 'tiny bird'], 'diet': 'nectar'}, page_content='hummingbirds are tiny birds known for their rapid wing beats and ability to hover.'), Document(id='hyena', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['laughing', 'pack', 'carnivore'], 'habitat': 'savanna'}, page_content='hyenas are carnivorous mammals known for their distinctive laughs and pack behavior.'), Document(id='iguana', metadata={'type': 'reptile', 'number_of_legs': 4, 'keywords': ['large lizard', 'herbivore', 'basking'], 'habitat': 'forest'}, page_content='iguanas are large herbivorous lizards often found basking in trees and near water.'), Document(id='jackal', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['scavenger', 'adaptable', 'carnivore'], 'diet': 'omnivorous'}, page_content='jackals are carnivorous mammals known for their scavenging and adaptability.'), Document(id='jaguar', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['big cat', 'spotted coat', 'predator'], 'habitat': 'rainforest'}, page_content='jaguars are powerful big cats known for their spotted coats and strength as predators.'), Document(id='jellyfish', metadata={'type': 'cnidarian', 'number_of_legs': 0, 'keywords': ['marine', 'tentacles', 'stinging cells'], 'habitat': 'ocean'}, page_content='jellyfish are marine animals with umbrella-shaped bodies and tentacles, known for their stinging cells.'), Document(id='kangaroo', metadata={'type': 'mammal', 'number_of_legs': 2, 'keywords': ['marsupial', 'australia', 'pouch'], 'diet': 'herbivorous'}, page_content='kangaroos are marsupials native to australia, known for their powerful hind legs and pouches.'), Document(id='koala', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['marsupial', 'tree-dwelling', 'eucalyptus'], 'diet': 'herbivorous'}, page_content='koalas are tree-dwelling marsupials native to australia, known for their diet of eucalyptus leaves.'), Document(id='komodo dragon', metadata={'type': 'reptile', 'number_of_legs': 4, 'keywords': ['large lizard', 'venom', 'bite'], 'habitat': 'islands'}, page_content='komodo dragons are the largest living lizards, known for their powerful bite and venom.'), Document(id='lark', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['songbird', 'melodious', 'ground-nesting'], 'diet': 'herbivorous'}, page_content='larks are small songbirds known for their melodious calls and ground-nesting habits.'), Document(id='lemur', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['primates', 'madagascar', 'social'], 'diet': 'omnivorous'}, page_content='lemurs are primates native to madagascar, known for their large eyes and social behavior.'), Document(id='leopard', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['big cat', 'spotted coat', 'climbing'], 'habitat': 'forest'}, page_content='leopards are big cats known for their spotted coats and ability to climb trees.'), Document(id='lion', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['carnivorous', 'pride', 'king of the jungle'], 'habitat': 'savanna'}, page_content=\"lions are large carnivorous cats known as the 'king of the jungle,' living in prides.\"), Document(id='lizard', metadata={'type': 'reptile', 'number_of_legs': 4, 'keywords': ['reptile', 'regeneration', 'diverse'], 'habitat': 'varied'}, page_content='lizards are reptiles known for their ability to regenerate their tails and their diverse species.'), Document(id='lobster', metadata={'type': 'crustacean', 'number_of_legs': 10, 'keywords': ['marine', 'pincers', 'seafloor'], 'diet': 'carnivorous'}, page_content='lobsters are marine crustaceans known for their hard shells and pincers, often found on the seafloor.'), Document(id='magpie', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['intelligent', 'plumage', 'mimicry'], 'diet': 'omnivorous'}, page_content='magpies are intelligent birds known for their black-and-white plumage and ability to mimic sounds.'), Document(id='manatee', metadata={'type': 'mammal', 'number_of_legs': 0, 'keywords': ['marine', 'gentle', 'sea cow'], 'diet': 'herbivorous'}, page_content=\"manatees are large, gentle marine mammals often called 'sea cows' for their grazing habits.\"), Document(id='mongoose', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['carnivore', 'agile', 'snake fighter'], 'habitat': 'forest-floor'}, page_content='mongooses are small carnivorous mammals known for their agility and ability to fight snakes.'), Document(id='moose', metadata={'type': 'mammal', 'number_of_legs': 4, 'keywords': ['large', 'antlers', 'deer family'], 'diet': 'herbivorous'}, page_content='moose are the largest members of the deer family, known for their massive antlers.'), Document(id='mosquito', metadata={'type': 'insect', 'number_of_legs': 6, 'keywords': ['bites', 'disease', 'vector'], 'diet': 'blood'}, page_content='mosquitoes are small insects known for their bites and role as disease vectors.'), Document(id='narwhal', metadata={'type': 'mammal', 'number_of_legs': 0, 'keywords': ['marine', 'tusks', 'unicorn'], 'habitat': 'arctic'}, page_content=\"narwhals are marine mammals known as 'unicorns of the sea' due to their long tusks.\"), Document(id='newt', metadata={'type': 'amphibian', 'number_of_legs': 4, 'keywords': ['regeneration', 'amphibian', 'small'], 'habitat': 'wetlands'}, page_content='newts are small amphibians known for their ability to regenerate limbs and tails.'), Document(id='octopus', metadata={'type': 'mollusk', 'number_of_legs': 8, 'keywords': ['intelligent', 'problem-solving', 'marine'], 'diet': 'carnivorous'}, page_content='octopuses are highly intelligent marine animals known for their problem-solving skills and eight arms.'), Document(id='ostrich', metadata={'type': 'bird', 'number_of_legs': 2, 'keywords': ['large', 'flightless', 'fast'], 'habitat': 'savanna'}, page_content='ostriches are the largest and fastest birds, though they are flightless.')]\n"]}], "source": ["for doc in animals:\n", "    keys_to_delete = []\n", "    for key, value in doc.metadata.items():\n", "        if isinstance(value, dict):\n", "            keys_to_delete.append(key)\n", "    for key in keys_to_delete:\n", "        del doc.metadata[key]\n", "\n", "print(animals)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建一个向量数据库测试"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from langchain_chroma.vectorstores import Chroma\n", "from langchain_graph_retriever.transformers import ShreddingTransformer\n", "\n", "\n", "vector_store = Chroma.from_documents(\n", "    documents=list(ShreddingTransformer().transform_documents(animals)),\n", "    embedding=embeddings,\n", "    collection_name=\"animals\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建一个基于知识图谱的检索，在这个检索里检索器从与查询最匹配的单个动物开始，然后遍历到具有相同habitat或origin的动物"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from graph_retriever.strategies import Eager\n", "from langchain_graph_retriever import GraphRetriever\n", "\n", "traversal_retriever = GraphRetriever(\n", "    store = vector_store,\n", "    edges = [(\"habitat\", \"habitat\"), (\"origin\", \"origin\")],\n", "    strategy = Eager(k=5, start_k=1, max_depth=2),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["提问"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["capybara: capybaras are the largest rodents in the world and are highly social animals.\n", "heron: herons are wading birds known for their long legs and necks, often seen near water.\n", "crocodile: crocodiles are large reptiles with powerful jaws and a long lifespan, often living over 70 years.\n", "frog: frogs are amphibians known for their jumping ability and croaking sounds.\n", "duck: ducks are waterfowl birds known for their webbed feet and quacking sounds.\n"]}], "source": ["results = traversal_retriever.invoke(\"what animals could be found near a capybara?\")\n", "\n", "for doc in results:\n", "    print(f\"{doc.id}: {doc.page_content}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["整合到问答链中"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from langchain_deepseek import ChatDeepSeek\n", "import os\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_core.runnables import RunnablePassthrough\n", "\n", "prompt = ChatPromptTemplate.from_template(\n", "\"\"\"Answer the question based only on the context provided.\n", "\n", "Context: {context}\n", "\n", "Question: {question}\n", "the final answer should be use chinese\n", "\"\"\"\n", "\n", ")\n", "\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join(f\"text: {doc.page_content} metadata: {doc.metadata}\" for doc in docs)\n", "\n", "chain = (\n", "    {\"context\": traversal_retriever | format_docs, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | llm\n", "    | StrOutputParser()\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["'根据提供的上下文，以下动物可能会出现在水豚附近，因为它们都生活在湿地环境中：\\n\\n1. 鹭 (Herons)  \\n2. 鳄鱼 (Crocodiles)  \\n3. 青蛙 (Frogs)  \\n4. 鸭子 (Ducks)  \\n\\n这些动物与水豚共享湿地栖息地，因此可能会在附近出现。'"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["chain.invoke(\"what animals could be found near a capybara?\")"]}], "metadata": {"kernelspec": {"display_name": "py313", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 2}