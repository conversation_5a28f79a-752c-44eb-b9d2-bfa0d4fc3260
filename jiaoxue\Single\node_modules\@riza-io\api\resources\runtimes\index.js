"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.Runtimes = exports.RuntimesRuntimesPagination = exports.Revisions = void 0;
var revisions_1 = require("./revisions.js");
Object.defineProperty(exports, "Revisions", { enumerable: true, get: function () { return revisions_1.Revisions; } });
var runtimes_1 = require("./runtimes.js");
Object.defineProperty(exports, "RuntimesRuntimesPagination", { enumerable: true, get: function () { return runtimes_1.RuntimesRuntimesPagination; } });
Object.defineProperty(exports, "Runtimes", { enumerable: true, get: function () { return runtimes_1.Runtimes; } });
//# sourceMappingURL=index.js.map