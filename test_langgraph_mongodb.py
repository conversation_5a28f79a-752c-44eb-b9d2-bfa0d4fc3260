#!/usr/bin/env python3
"""
测试 LangGraph 与 MongoDB 的集成
"""

try:
    from langgraph.checkpoint.mongodb import MongoDBSaver
    print("✅ LangGraph MongoDB 模块导入成功")
    
    # MongoDB 连接字符串（无认证）
    MONGODB_URI = "mongodb://localhost:27017"
    
    # 测试 MongoDBSaver
    print("🔄 测试 MongoDBSaver...")
    
    with MongoDBSaver.from_conn_string(MONGODB_URI) as checkpointer:
        print("✅ MongoDBSaver 创建成功！")
        
        # 测试基本操作
        config = {"configurable": {"thread_id": "test_thread_1"}}
        
        # 这里可以添加更多的测试...
        print("✅ MongoDB checkpointer 可以正常使用")
    
    print("🎉 LangGraph MongoDB 集成测试成功！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保安装了 langgraph-checkpoint-mongodb:")
    print("pip install langgraph-checkpoint-mongodb")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    print("请检查 MongoDB 是否正在运行且可访问")
