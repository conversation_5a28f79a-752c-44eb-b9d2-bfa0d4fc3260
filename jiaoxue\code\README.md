## 说明
1. 本机安装[Docker](https://www.docker.com/products/docker-desktop/) (必需)和docker-compse（怎么安装自己搜索下）
2. 下载langchain-course.tar这个镜像文件
3. 在命令行中输入：docker load -i langchain-course.tar
4. 参考.env.example文件，补充环境变量为你的API KEY
5. 命令行运行： docker-compose up -d
6. 打开：http://localhost:8888/ 就可以使用了
******
## 注意
- 本代码仅适用于购买了网络课程 https://coding.imooc.com/class/925.html 的用户
- 访问 https://1goto.ai 扫码加入AI共进学习群，获取第一手AI发展资讯！ 

*****
.env.example
OPENAI_API_BASE=https://api.openai-proxy.org/v1
OPENAI_API_KEY=your_key_here
DEEPSEEK_API_KEY=your_key_here
DEEPSEEK_API_BASE=https://api.siliconflow.cn
LANGCHAIN_API_KEY=your_key_here
ANTHROPIC_BASE_URL=https://api.openai-proxy.org/anthropic
ANTHROPIC_API_KEY=your_key_here
GOOGLE_BASE_URL=https://api.openai-proxy.org/google
GOOGLE_API_KEY=your_key_here
PINECONE_API_KEY=your_key_here