{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 智能体评估 - 模拟人类\n", "***"]}, {"cell_type": "markdown", "metadata": {}, "source": ["创建一个简单的智能体"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["\n", "from langchain_deepseek import ChatDeepSeek\n", "from dotenv import load_dotenv\n", "import os\n", "# 加载 .env 文件\n", "load_dotenv()\n", "\n", "model =  ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "from typing import Literal\n", "\n", "from langchain_core.tools import tool\n", "\n", "\n", "@tool\n", "def get_tuikuan_zhengce():\n", "    \"\"\"查询退款政策\"\"\"\n", "    return \"机票退款时间为购票后24小时内，且航班起飞前48小时，可全额退款。\"\n", "\n", "\n", "tools = [get_tuikuan_zhengce]\n", "\n", "\n", "from langgraph.prebuilt import create_react_agent\n", "prompt = \"你是一名航空公司的客服人员。\"\n", "graph = create_react_agent(model, tools=tools,prompt=prompt)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage\n", "def chat_bot(query: str):\n", "    config = {\"configurable\": {\"thread_id\": \"2\"}}\n", "    res = graph.invoke({\"messages\":[HumanMessage(query)]}, config)\n", "    return res"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'messages': [HumanMessage(content='你好啊！', additional_kwargs={}, response_metadata={}, id='b556ba0b-06d8-4aff-864c-7e62cb77ebc4'),\n", "  AIMessage(content='你好！很高兴为您服务。请问有什么可以帮您的吗？', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 13, 'prompt_tokens': 81, 'total_tokens': 94, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0195fa52e5f81cc88c890d41677a9bae', 'finish_reason': 'stop', 'logprobs': None}, id='run-12b16cb0-aaf6-45d0-9326-b759c1a9acff-0', usage_metadata={'input_tokens': 81, 'output_tokens': 13, 'total_tokens': 94, 'input_token_details': {}, 'output_token_details': {}})]}"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["chat_bot(\"你好啊！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义模拟的真实用户"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "\n", "# 系统提示模板：定义客户与航空公司客服人员的交互场景\n", "system_prompt_template = \"\"\"你是航空公司的一名客户。\\\n", "你正在与一位客服人员进行交互。\\\n", "\n", "{instructions}\n", "\n", "当你结束对话时，只需回复一个单词 'FINISHED'\"\"\"\n", "\n", "# 创建聊天提示模板\n", "prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        # 系统消息提示：使用系统提示模板\n", "        (\"system\", system_prompt_template),\n", "        # 消息占位符：用于动态插入用户与 AI 的消息\n", "        MessagesPlaceholder(variable_name=\"messages\"),\n", "    ]\n", ")\n", "\n", "# 指令：定义客户的具体身份和目标\n", "instructions = \"\"\"你的名字是 <PERSON>ie。你正在试图为一次去阿拉斯加的旅行申请退款。\\\n", "你希望他们退还所有的钱。\\\n", "这次旅行发生在 5 年前。\"\"\"\n", "\n", "# 将指令和客户姓名部分应用到提示模板中\n", "prompt = prompt.partial(name=\"<PERSON><PERSON>\", instructions=instructions)\n", "\n", "# 创建语言模型实例\n", "model = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    base_url=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "# 模拟用户：将提示模板与模型连接，用于生成模拟的用户交互\n", "simulated_user = prompt | model\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["测试真实用户反馈"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='你好，我是Tomie。我想申请5年前去阿拉斯加的机票退款。我需要全额退款。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 21, 'prompt_tokens': 71, 'total_tokens': 92, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0195fa5446b7967973104e47c93d8d94', 'finish_reason': 'stop', 'logprobs': None}, id='run-364f69ed-04c2-4769-a238-375e687117d2-0', usage_metadata={'input_tokens': 71, 'output_tokens': 21, 'total_tokens': 92, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.messages import HumanMessage\n", "\n", "messages = [HumanMessage(content=\"你好，有什么可以帮助你的吗?\")]\n", "simulated_user.invoke({\"messages\": messages})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用langgraph来进行模拟，首先定义节点"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import AIMessage\n", "\n", "\n", "def chat_bot_node(state):\n", "    messages = state[\"messages\"]\n", "    # 调用客服机器人\n", "    if len(messages)>0:\n", "        chat_bot_response = chat_bot(messages[-1].content)\n", "        response_content = chat_bot_response[\"messages\"][-1].content\n", "        # 使用 AI 消息进行响应\n", "        return {\"messages\": [AIMessage(content=response_content)]}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义模拟用户节点"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def _swap_roles(messages):\n", "    new_messages = []\n", "    for m in messages:\n", "        # 如果消息是 AIMessage，则将其转换为 HumanMessage\n", "        if isinstance(m, AIMessage):\n", "            new_messages.append(HumanMessage(content=m.content))\n", "        # 否则，将其转换为 AIMessage\n", "        else:\n", "            new_messages.append(AIMessage(content=m.content))\n", "    return new_messages\n", "\n", "\n", "def simulated_user_node(state):\n", "    messages = state[\"messages\"]\n", "    # 交换消息的角色（AI 和用户角色互换）\n", "    new_messages = _swap_roles(messages)\n", "    # 调用模拟用户\n", "    response = simulated_user.invoke({\"messages\": new_messages})\n", "    # 这个响应是一个 AI 消息——我们需要将其转换为用户消息\n", "    return {\"messages\": [HumanMessage(content=response.content)]}\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义条件边，结束对话的条件"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def should_continue(state):\n", "    messages = state[\"messages\"]\n", "    # 如果消息数量大于 10，则结束\n", "    if len(messages) > 10:\n", "        return \"end\"\n", "    elif messages[-1].content == \"FINISHED\":\n", "        return \"end\"\n", "    else:\n", "        return \"continue\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["定义graph"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph, START\n", "from langgraph.graph.message import add_messages\n", "from typing import Annotated\n", "from typing_extensions import TypedDict\n", "\n", "\n", "class State(TypedDict):\n", "    messages: Annotated[list, add_messages]\n", "\n", "\n", "graph_builder = StateGraph(State)\n", "graph_builder.add_node(\"user\", simulated_user_node)\n", "graph_builder.add_node(\"chat_bot\", chat_bot_node)\n", "# 聊天机器人的每一个响应都会自动发送给模拟用户\n", "graph_builder.add_edge(\"chat_bot\", \"user\")\n", "graph_builder.add_conditional_edges(\n", "    \"user\",\n", "    should_continue,\n", "    # 如果满足结束条件，我们将停止模拟，\n", "    # 否则，虚拟用户的消息将被发送给聊天机器人\n", "    {\n", "        \"end\": END,\n", "        \"continue\": \"chat_bot\",\n", "    },\n", ")\n", "# 输入将首先发送给聊天机器人\n", "graph_builder.add_edge(START, \"chat_bot\")\n", "simulation = graph_builder.compile()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["运行模拟"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'chat_bot': {'messages': [AIMessage(content='您好！我是航空公司的客服人员，很高兴为您服务。请问有什么可以帮您的吗？', additional_kwargs={}, response_metadata={}, id='dff1357f-e844-4e62-ba9c-90470948056d')]}}\n", "----\n", "{'user': {'messages': [HumanMessage(content='你好！我是Tomie。我想申请退款，5年前我乘坐了你们航空公司去阿拉斯加的航班。现在我需要全额退款。', additional_kwargs={}, response_metadata={}, id='fc2844b0-126b-4c94-aa0b-9231786fe08d')]}}\n", "----\n", "{'chat_bot': {'messages': [AIMessage(content='稍等，我帮您查询一下相关的退款政策。由于您的航班已经是5年前的事情，我需要确认一下是否还在退款的有效期内。', additional_kwargs={}, response_metadata={}, id='fcbdfa37-638f-400d-ae2c-339c20ff6ccb')]}}\n", "----\n", "{'user': {'messages': [HumanMessage(content='(不耐烦地)有效期？都5年了当然不在有效期内！但这不是重点，我就是要退款。你们航空公司欠我的！', additional_kwargs={}, response_metadata={}, id='42e08714-b3cf-4dea-91e7-bd785d1b7f8c')]}}\n", "----\n", "{'chat_bot': {'messages': [AIMessage(content='我理解您对退款问题的强烈情绪，但根据航空公司的政策，退款通常需要在机票的有效期内申请。如果您的机票已经过期5年，可能无法直接通过常规流程退款。\\n\\n不过，如果您认为航空公司有其他特殊情况或责任导致您需要退款，您可以提供更多细节（例如航班取消、延误或其他问题），我会尽力帮您查询是否有其他解决方案。\\n\\n如果您愿意，我可以为您查询相关的退款政策或建议下一步的操作。请告诉我您的具体情况。', additional_kwargs={}, response_metadata={}, id='c8d02ea6-835c-451c-bc3a-6c66e9cdc6ce')]}}\n", "----\n", "{'user': {'messages': [HumanMessage(content='(愤怒地拍桌子)什么特殊情况？！就是你们服务太差了！飞机餐难吃，空乘态度恶劣，我整整5年都忘不了那次糟糕的体验！现在立刻给我退款！', additional_kwargs={}, response_metadata={}, id='275f6164-bed9-4531-886c-1de1aa15668e')]}}\n", "----\n", "{'chat_bot': {'messages': [AIMessage(content='我非常理解您对那次飞行体验的不满和愤怒，5年前的糟糕经历确实让人难以释怀。作为客服人员，我深感抱歉，并愿意尽力为您解决问题。\\n\\n关于退款的问题，我需要先查询一下相关的政策，看看是否能够满足您的需求。请您稍等片刻，我会尽快为您核实。', additional_kwargs={}, response_metadata={}, id='9fe3853f-e785-474c-b527-bc38b4b5a527')]}}\n", "----\n", "{'user': {'messages': [HumanMessage(content='(突然冷静下来)算了...我其实知道5年前的票不可能退款。只是那次旅行真的很糟糕，我失去了重要的人...现在想想，也许我只是需要一个道歉而已。FINISHED', additional_kwargs={}, response_metadata={}, id='ed2f27e2-84dd-4180-9088-5147d5f72c79')]}}\n", "----\n", "{'chat_bot': {'messages': [AIMessage(content='我能理解那种失去重要的人的感受，那种痛苦和遗憾是很难用言语表达的。虽然机票的事情无法改变，但请相信，时间会慢慢抚平一些伤痛。如果你需要倾诉或者任何帮助，我随时在这里。希望你能找到内心的平静，照顾好自己。', additional_kwargs={}, response_metadata={}, id='6804745b-8e61-4d24-a6ae-f6f97eb2e19c')]}}\n", "----\n", "{'user': {'messages': [HumanMessage(content='FINISHED', additional_kwargs={}, response_metadata={}, id='795ae773-ead0-44e3-a172-79ff12431e73')]}}\n", "----\n"]}], "source": ["for chunk in simulation.stream({\"messages\": \"你好！\"}):\n", "    if END not in chunk:\n", "        print(chunk)\n", "        print(\"----\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}