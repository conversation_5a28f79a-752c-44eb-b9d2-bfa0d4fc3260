[project]
name = "XiaoLang"
version = "0.1.0"
description = "浪智能客服助手"
authors = [
    {name = "to<PERSON><PERSON><PERSON>"}
]
readme = "README.md"
requires-python = ">=3.10,<4.0"
dependencies = [
    "fastapi (>=0.115.11,<0.116.0)",
    "uvicorn (>=0.34.0,<0.35.0)",
    "langgraph-supervisor (>=0.0.8,<0.0.9)",
    "langchain-openai (>=0.3.7,<0.4.0)",
    "langchain-deepseek (>=0.1.2,<0.2.0)",
    "taot (>=0.1.3,<0.2.0)",
    "websockets (>=15.0.1,<16.0.0)",
    "langchain-community (>=0.3.19,<0.4.0)",
    "rizaio (>=0.8.0,<0.9.0)"
]

[project.scripts]
start = "background.Server:main"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
packages = [
    {include = "background"}
]