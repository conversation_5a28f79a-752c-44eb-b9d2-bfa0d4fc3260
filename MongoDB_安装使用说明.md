# MongoDB Docker 安装和使用说明

## 🎉 安装完成状态

✅ MongoDB Docker 容器已成功安装并运行  
✅ 端口映射：localhost:27017  
✅ 无认证模式（适合开发环境）  
✅ LangGraph MongoDB 集成已测试通过  

## 📋 当前配置

- **容器名称**: `mongodb`
- **镜像版本**: `mongo:latest`
- **端口**: `27017:27017`
- **认证**: 无（开发环境）
- **数据持久化**: 容器内存储

## 🚀 使用方法

### 在 Jupyter Notebook 中使用

```python
from langgraph.checkpoint.mongodb import MongoDBSaver

# MongoDB 连接字符串
MONGODB_URI = "mongodb://localhost:27017"

# 使用 MongoDBSaver 作为 checkpointer
with MongoDBSaver.from_conn_string(MONGODB_URI) as checkpointer:
    graph = create_react_agent(model, tools=tools, checkpointer=checkpointer)
    config = {"configurable": {"thread_id": "your_thread_id"}}
    
    # 运行对话
    result = graph.invoke({"messages": [("user", "你好")]}, config)
```

### 基本 PyMongo 使用

```python
from pymongo import MongoClient

# 连接到 MongoDB
client = MongoClient("mongodb://localhost:27017/")

# 选择数据库和集合
db = client.your_database
collection = db.your_collection

# 插入数据
doc = {"name": "example", "value": 123}
result = collection.insert_one(doc)

# 查询数据
found_doc = collection.find_one({"name": "example"})
print(found_doc)

client.close()
```

## 🛠 管理命令

### 容器管理
```bash
# 查看容器状态
docker ps

# 启动容器
docker start mongodb

# 停止容器
docker stop mongodb

# 重启容器
docker restart mongodb

# 查看日志
docker logs mongodb

# 进入容器
docker exec -it mongodb bash
```

### MongoDB 操作
```bash
# 连接到 MongoDB shell
docker exec -it mongodb mongosh

# 在容器内执行 MongoDB 命令
docker exec mongodb mongosh --eval "db.runCommand({ping: 1})"
```

## 📁 文件说明

- `simple_mongodb_test.py` - 基本连接测试脚本
- `test_langgraph_mongodb.py` - LangGraph 集成测试脚本
- `docker-compose-mongodb.yml` - Docker Compose 配置文件

## 🔧 故障排除

### 连接问题
1. 确保容器正在运行：`docker ps`
2. 检查端口是否被占用：`netstat -an | findstr 27017`
3. 查看容器日志：`docker logs mongodb`

### 认证问题
当前配置为无认证模式。如果遇到认证错误，确保：
- 使用正确的连接字符串：`mongodb://localhost:27017`
- 没有设置用户名密码

### 重新安装
如果需要重新安装：
```bash
# 停止并删除容器
docker stop mongodb
docker rm mongodb

# 重新创建容器
docker run -d -p 27017:27017 --name mongodb mongo --noauth
```

## 📊 测试验证

运行测试脚本验证安装：
```bash
# 基本连接测试
python simple_mongodb_test.py

# LangGraph 集成测试
python test_langgraph_mongodb.py
```

## 🎯 下一步

现在您可以在 Jupyter Notebook 中使用 MongoDB 作为 LangGraph 的持久化存储了！

修改您的代码中的连接字符串：
```python
MONGODB_URI = "mongodb://localhost:27017"  # 使用这个连接字符串
```
