import { type Agent } from "./_shims/index.js";
import * as Core from "./core.js";
import * as Errors from "./error.js";
import * as Pagination from "./pagination.js";
import { type DefaultPaginationParams, DefaultPaginationResponse, type RuntimesPaginationParams, RuntimesPaginationResponse, type SecretsPaginationParams, SecretsPaginationResponse, type ToolsPaginationParams, ToolsPaginationResponse } from "./pagination.js";
import * as Uploads from "./uploads.js";
import * as API from "./resources/index.js";
import { Command, CommandExecFuncParams, CommandExecFuncResponse, CommandExecParams, CommandExecResponse } from "./resources/command.js";
import { Execution, ExecutionListParams, Executions, ExecutionsDefaultPagination } from "./resources/executions.js";
import { Secret, SecretCreateParams, SecretListParams, Secrets, SecretsSecretsPagination } from "./resources/secrets.js";
import { Tool, ToolCreateParams, ToolExecParams, ToolExecResponse, ToolListParams, ToolUpdateParams, Tools, ToolsToolsPagination } from "./resources/tools.js";
import { Runtime, RuntimeCreateParams, RuntimeDeleteResponse, RuntimeListParams, Runtimes, RuntimesRuntimesPagination } from "./resources/runtimes/runtimes.js";
export interface ClientOptions {
    /**
     * Defaults to process.env['RIZA_API_KEY'].
     */
    apiKey?: string | undefined;
    /**
     * Override the default base URL for the API, e.g., "https://api.example.com/v2/"
     *
     * Defaults to process.env['RIZA_BASE_URL'].
     */
    baseURL?: string | null | undefined;
    /**
     * The maximum amount of time (in milliseconds) that the client should wait for a response
     * from the server before timing out a single request.
     *
     * Note that request timeouts are retried by default, so in a worst-case scenario you may wait
     * much longer than this timeout before the promise succeeds or fails.
     */
    timeout?: number | undefined;
    /**
     * An HTTP agent used to manage HTTP(S) connections.
     *
     * If not provided, an agent will be constructed by default in the Node.js environment,
     * otherwise no agent is used.
     */
    httpAgent?: Agent | undefined;
    /**
     * Specify a custom `fetch` function implementation.
     *
     * If not provided, we use `node-fetch` on Node.js and otherwise expect that `fetch` is
     * defined globally.
     */
    fetch?: Core.Fetch | undefined;
    /**
     * The maximum number of times that the client will retry a request in case of a
     * temporary failure, like a network error or a 5XX error from the server.
     *
     * @default 2
     */
    maxRetries?: number | undefined;
    /**
     * Default headers to include with every request to the API.
     *
     * These can be removed in individual requests by explicitly setting the
     * header to `undefined` or `null` in request options.
     */
    defaultHeaders?: Core.Headers | undefined;
    /**
     * Default query parameters to include with every request to the API.
     *
     * These can be removed in individual requests by explicitly setting the
     * param to `undefined` in request options.
     */
    defaultQuery?: Core.DefaultQuery | undefined;
}
/**
 * API Client for interfacing with the Riza API.
 */
export declare class Riza extends Core.APIClient {
    #private;
    apiKey: string;
    private _options;
    /**
     * API Client for interfacing with the Riza API.
     *
     * @param {string | undefined} [opts.apiKey=process.env['RIZA_API_KEY'] ?? undefined]
     * @param {string} [opts.baseURL=process.env['RIZA_BASE_URL'] ?? https://api.riza.io] - Override the default base URL for the API.
     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.
     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.
     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.
     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.
     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.
     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.
     */
    constructor({ baseURL, apiKey, ...opts }?: ClientOptions);
    secrets: API.Secrets;
    tools: API.Tools;
    command: API.Command;
    runtimes: API.Runtimes;
    executions: API.Executions;
    protected defaultQuery(): Core.DefaultQuery | undefined;
    protected defaultHeaders(opts: Core.FinalRequestOptions): Core.Headers;
    protected authHeaders(opts: Core.FinalRequestOptions): Core.Headers;
    static Riza: typeof Riza;
    static DEFAULT_TIMEOUT: number;
    static RizaError: typeof Errors.RizaError;
    static APIError: typeof Errors.APIError;
    static APIConnectionError: typeof Errors.APIConnectionError;
    static APIConnectionTimeoutError: typeof Errors.APIConnectionTimeoutError;
    static APIUserAbortError: typeof Errors.APIUserAbortError;
    static NotFoundError: typeof Errors.NotFoundError;
    static ConflictError: typeof Errors.ConflictError;
    static RateLimitError: typeof Errors.RateLimitError;
    static BadRequestError: typeof Errors.BadRequestError;
    static AuthenticationError: typeof Errors.AuthenticationError;
    static InternalServerError: typeof Errors.InternalServerError;
    static PermissionDeniedError: typeof Errors.PermissionDeniedError;
    static UnprocessableEntityError: typeof Errors.UnprocessableEntityError;
    static toFile: typeof Uploads.toFile;
    static fileFromPath: typeof Uploads.fileFromPath;
}
export declare namespace Riza {
    export type RequestOptions = Core.RequestOptions;
    export import DefaultPagination = Pagination.DefaultPagination;
    export { type DefaultPaginationParams as DefaultPaginationParams, type DefaultPaginationResponse as DefaultPaginationResponse, };
    export import RuntimesPagination = Pagination.RuntimesPagination;
    export { type RuntimesPaginationParams as RuntimesPaginationParams, type RuntimesPaginationResponse as RuntimesPaginationResponse, };
    export import ToolsPagination = Pagination.ToolsPagination;
    export { type ToolsPaginationParams as ToolsPaginationParams, type ToolsPaginationResponse as ToolsPaginationResponse, };
    export import SecretsPagination = Pagination.SecretsPagination;
    export { type SecretsPaginationParams as SecretsPaginationParams, type SecretsPaginationResponse as SecretsPaginationResponse, };
    export { Secrets as Secrets, type Secret as Secret, SecretsSecretsPagination as SecretsSecretsPagination, type SecretCreateParams as SecretCreateParams, type SecretListParams as SecretListParams, };
    export { Tools as Tools, type Tool as Tool, type ToolExecResponse as ToolExecResponse, ToolsToolsPagination as ToolsToolsPagination, type ToolCreateParams as ToolCreateParams, type ToolUpdateParams as ToolUpdateParams, type ToolListParams as ToolListParams, type ToolExecParams as ToolExecParams, };
    export { Command as Command, type CommandExecResponse as CommandExecResponse, type CommandExecFuncResponse as CommandExecFuncResponse, type CommandExecParams as CommandExecParams, type CommandExecFuncParams as CommandExecFuncParams, };
    export { Runtimes as Runtimes, type Runtime as Runtime, type RuntimeDeleteResponse as RuntimeDeleteResponse, RuntimesRuntimesPagination as RuntimesRuntimesPagination, type RuntimeCreateParams as RuntimeCreateParams, type RuntimeListParams as RuntimeListParams, };
    export { Executions as Executions, type Execution as Execution, ExecutionsDefaultPagination as ExecutionsDefaultPagination, type ExecutionListParams as ExecutionListParams, };
}
export { toFile, fileFromPath } from "./uploads.js";
export { RizaError, APIError, APIConnectionError, APIConnectionTimeoutError, APIUserAbortError, NotFoundError, ConflictError, RateLimitError, BadRequestError, AuthenticationError, InternalServerError, PermissionDeniedError, UnprocessableEntityError, } from "./error.js";
export default Riza;
//# sourceMappingURL=index.d.ts.map