'use strict';

const OriginalHttpsAgent = require('https').Agent;
const HttpAgent = require('./agent');
const {
  INIT_SOCKET,
  CREATE_HTTPS_CONNECTION,
} = require('./constants');

class HttpsAgent extends HttpAgent {
  constructor(options) {
    super(options);

    this.defaultPort = 443;
    this.protocol = 'https:';
    this.maxCachedSessions = this.options.maxCachedSessions;
    /* istanbul ignore next */
    if (this.maxCachedSessions === undefined) {
      this.maxCachedSessions = 100;
    }

    this._sessionCache = {
      map: {},
      list: [],
    };
  }

  createConnection(options, oncreate) {
    const socket = this[CREATE_HTTPS_CONNECTION](options, oncreate);
    this[INIT_SOCKET](socket, options);
    return socket;
  }
}

// https://github.com/nodejs/node/blob/master/lib/https.js#L89
HttpsAgent.prototype[CREATE_HTTPS_CONNECTION] = OriginalHttpsAgent.prototype.createConnection;

[
  'getName',
  '_getSession',
  '_cacheSession',
  // https://github.com/nodejs/node/pull/4982
  '_evictSession',
].forEach(function(method) {
  /* istanbul ignore next */
  if (typeof OriginalHttpsAgent.prototype[method] === 'function') {
    HttpsAgent.prototype[method] = OriginalHttpsAgent.prototype[method];
  }
});

module.exports = HttpsAgent;
