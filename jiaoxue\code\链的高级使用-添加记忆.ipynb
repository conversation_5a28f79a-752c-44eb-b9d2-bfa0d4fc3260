{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 为链增加记忆能力\n", "***\n", "- 注意：简单的链的记忆添加可以使用v0.2的方式，复杂的官方推荐使用langgraph\n", "- 短时记忆：InMemoryHistory\n", "- 长时记忆 RunnableWithMessageHistory"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### InMemoryHistory"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'1': InMemoryHistory(messages=[AIMessage(content='你好', additional_kwargs={}, response_metadata={})])}\n"]}], "source": ["\n", "from typing import List  # 导入List类型提示\n", "from pydantic import BaseModel, Field  # 导入Pydantic的BaseModel和Field\n", "from langchain_core.chat_history import BaseChatMessageHistory  # 导入聊天历史基类\n", "\n", "from langchain_core.messages import BaseMessage, AIMessage  # 导入消息基类和AI消息类\n", "\n", "\n", "\n", "class InMemoryHistory(BaseChatMessageHistory, BaseModel):\n", "    \"\"\"内存中实现的聊天消息历史记录。\"\"\"\n", "\n", "    messages: List[BaseMessage] = Field(default_factory=list)  # 使用空列表作为默认值存储消息\n", "\n", "    def add_messages(self, messages: List[BaseMessage]) -> None:\n", "        \"\"\"添加一组消息到存储中\"\"\"\n", "        self.messages.extend(messages)\n", "\n", "    def clear(self) -> None:\n", "        \"\"\"清空所有消息\"\"\"\n", "        self.messages = []\n", "\n", "# 这里我们使用全局变量来存储聊天消息历史。\n", "# 这样可以更容易地检查它以查看底层结果。\n", "store = {}  # 创建空字典用于存储不同会话的历史记录\n", "\n", "def get_by_session_id(session_id: str) -> BaseChatMessageHistory:\n", "    \"\"\"根据会话ID获取历史记录，如果不存在则创建新的\"\"\"\n", "    if session_id not in store:\n", "        store[session_id] = InMemoryHistory()  # 为新会话创建新的历史记录对象\n", "    return store[session_id]\n", "\n", "\n", "# 获取会话ID为\"1\"的历史记录\n", "history = get_by_session_id(\"1\")\n", "# 添加一条AI消息到历史记录\n", "history.add_message(AIMessage(content=\"你好\"))  # 修改为中文消息\n", "# 打印存储的所有历史记录\n", "print(store)  # 将输出包含会话\"1\"的历史记录，其中有一条\"你好\"的AI消息\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["在链中增加短时记忆"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting langchain-deepseek\n", "  Downloading langchain_deepseek-0.1.3-py3-none-any.whl.metadata (1.1 kB)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.47 in /opt/conda/lib/python3.11/site-packages (from langchain-deepseek) (0.3.48)\n", "Requirement already satisfied: langchain-openai<1.0.0,>=0.3.9 in /opt/conda/lib/python3.11/site-packages (from langchain-deepseek) (0.3.10)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.125 in /opt/conda/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.3.18)\n", "Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.1.0 in /opt/conda/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (9.0.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /opt/conda/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (1.33)\n", "Requirement already satisfied: PyYAML>=5.3 in /opt/conda/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (6.0.1)\n", "Requirement already satisfied: packaging<25,>=23.2 in /opt/conda/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (23.2)\n", "Requirement already satisfied: typing-extensions>=4.7 in /opt/conda/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (4.13.0)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.5.2 in /opt/conda/lib/python3.11/site-packages (from langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2.10.6)\n", "Requirement already satisfied: openai<2.0.0,>=1.68.2 in /opt/conda/lib/python3.11/site-packages (from langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (1.68.2)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in /opt/conda/lib/python3.11/site-packages (from langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (0.9.0)\n", "Requirement already satisfied: jsonpointer>=1.9 in /opt/conda/lib/python3.11/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2.4)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in /opt/conda/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.28.1)\n", "Requirement already satisfied: or<PERSON><PERSON><4.0.0,>=3.9.14 in /opt/conda/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (3.10.16)\n", "Requirement already satisfied: requests<3,>=2 in /opt/conda/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2.31.0)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /opt/conda/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /opt/conda/lib/python3.11/site-packages (from langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.23.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /opt/conda/lib/python3.11/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (4.0.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /opt/conda/lib/python3.11/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (1.9.0)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /opt/conda/lib/python3.11/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (0.9.0)\n", "Requirement already satisfied: sniffio in /opt/conda/lib/python3.11/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (1.3.0)\n", "Requirement already satisfied: tqdm>4 in /opt/conda/lib/python3.11/site-packages (from openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (4.66.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /opt/conda/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.5.2->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in /opt/conda/lib/python3.11/site-packages (from pydantic<3.0.0,>=2.5.2->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2.27.2)\n", "Requirement already satisfied: regex>=2022.1.18 in /opt/conda/lib/python3.11/site-packages (from tiktoken<1,>=0.7->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (2024.11.6)\n", "Requirement already satisfied: idna>=2.8 in /opt/conda/lib/python3.11/site-packages (from anyio<5,>=3.5.0->openai<2.0.0,>=1.68.2->langchain-openai<1.0.0,>=0.3.9->langchain-deepseek) (3.4)\n", "Requirement already satisfied: certifi in /opt/conda/lib/python3.11/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2023.7.22)\n", "Requirement already satisfied: httpcore==1.* in /opt/conda/lib/python3.11/site-packages (from httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (1.0.7)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /opt/conda/lib/python3.11/site-packages (from httpcore==1.*->httpx<1,>=0.23.0->langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (0.14.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /opt/conda/lib/python3.11/site-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (3.3.0)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /opt/conda/lib/python3.11/site-packages (from requests<3,>=2->langsmith<0.4,>=0.1.125->langchain-core<1.0.0,>=0.3.47->langchain-deepseek) (2.0.7)\n", "Downloading langchain_deepseek-0.1.3-py3-none-any.whl (7.1 kB)\n", "Installing collected packages: langchain-deepseek\n", "Successfully installed langchain-deepseek-0.1.3\n"]}], "source": ["! pip install langchain-deepseek"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='余弦函数（通常记作 \\\\(\\\\cos\\\\)）是三角函数的一种，用于描述直角三角形中一个锐角的邻边与斜边的比值，或者在单位圆中表示横坐标与半径的关系。以下是余弦函数的详细解释：\\n\\n### 1. **直角三角形中的定义**\\n在直角三角形中，余弦值定义为：\\n\\\\[\\n\\\\cos \\\\theta = \\\\frac{\\\\text{邻边}}{\\\\text{斜边}}\\n\\\\]\\n其中：\\n- \\\\(\\\\theta\\\\) 是一个锐角，\\n- **邻边** 是该角相邻的直角边，\\n- **斜边** 是直角三角形的斜边（最长边）。\\n\\n**例子**：若一个角 \\\\(\\\\theta\\\\) 的邻边长为 3，斜边为 5，则 \\\\(\\\\cos \\\\theta = \\\\frac{3}{5}\\\\)。\\n\\n---\\n\\n### 2. **单位圆中的定义**\\n在直角坐标系中，以原点为中心、半径为 1 的单位圆上，余弦值等于角度 \\\\(\\\\theta\\\\) 的终边与圆交点的 **横坐标（x 坐标）**：\\n\\\\[\\n\\\\cos \\\\theta = x\\n\\\\]\\n- 当 \\\\(\\\\theta\\\\) 从 \\\\(0\\\\) 增加到 \\\\(2\\\\pi\\\\)（360°）时，余弦值从 1 递减到 -1，再回到 1，呈现周期性变化。\\n\\n---\\n\\n### 3. **余弦函数的性质**\\n- **周期性**：余弦函数的周期为 \\\\(2\\\\pi\\\\)（或 360°），即 \\\\(\\\\cos(\\\\theta + 2\\\\pi) = \\\\cos \\\\theta\\\\)。\\n- **取值范围**：值域为 \\\\([-1, 1]\\\\)。\\n- **偶函数**：满足 \\\\(\\\\cos(-\\\\theta) = \\\\cos \\\\theta\\\\)，图像关于 y 轴对称。\\n- **与正弦函数的关系**：\\\\(\\\\cos \\\\theta = \\\\sin\\\\left(\\\\theta + \\\\frac{\\\\pi}{2}\\\\right)\\\\)。\\n\\n---\\n\\n### 4. **图像特征**\\n余弦函数的图像（波形图）特点：\\n- **起点**：\\\\(\\\\cos 0 = 1\\\\)。\\n- **零点**：在 \\\\(\\\\theta = \\\\frac{\\\\pi}{2}, \\\\frac{3\\\\pi}{2}, \\\\ldots\\\\) 处值为 0。\\n- **极值点**：在 \\\\(\\\\theta = 0, \\\\pi, 2\\\\pi, \\\\ldots\\\\) 处取得最大值 1 或最小值 -1。\\n\\n---\\n\\n### 5. **应用场景**\\n- **几何**：计算角度或边长。\\n- **物理**：描述简谐振动（如弹簧运动、交流电）。\\n- **工程**：信号处理、傅里叶分析等。\\n\\n---\\n\\n### 示例计算\\n**问题**：求 \\\\(\\\\cos 60^\\\\circ\\\\) 的值。  \\n**解**：  \\n在单位圆中，\\\\(60^\\\\circ\\\\) 对应的坐标为 \\\\(\\\\left(\\\\frac{1}{2}, \\\\frac{\\\\sqrt{3}}{2}\\\\right)\\\\)，因此：\\n\\\\[\\n\\\\cos 60^\\\\circ = \\\\frac{1}{2}\\n\\\\]\\n\\n---\\n\\n通过上述定义和性质，余弦函数成为数学和科学中分析周期性现象的重要工具。如果需要进一步探讨其公式（如余弦定理）或其他扩展内容，可以继续提问！' additional_kwargs={'refusal': None} response_metadata={'token_usage': {'completion_tokens': 651, 'prompt_tokens': 13, 'total_tokens': 664, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0195d2a9a43046e1ecabf1b587b24599', 'finish_reason': 'stop', 'logprobs': None} id='run-d92a68be-61cf-4619-8fa3-dd7ff8285b31-0' usage_metadata={'input_tokens': 13, 'output_tokens': 651, 'total_tokens': 664, 'input_token_details': {}, 'output_token_details': {}}\n", "{'1': InMemoryHistory(messages=[AIMessage(content='你好', additional_kwargs={}, response_metadata={})]), 'foo': InMemoryHistory(messages=[HumanMessage(content='余弦函数是什么意思？', additional_kwargs={}, response_metadata={}), AIMessage(content='余弦函数（通常记作 \\\\(\\\\cos\\\\)）是三角函数的一种，用于描述直角三角形中一个锐角的邻边与斜边的比值，或者在单位圆中表示横坐标与半径的关系。以下是余弦函数的详细解释：\\n\\n### 1. **直角三角形中的定义**\\n在直角三角形中，余弦值定义为：\\n\\\\[\\n\\\\cos \\\\theta = \\\\frac{\\\\text{邻边}}{\\\\text{斜边}}\\n\\\\]\\n其中：\\n- \\\\(\\\\theta\\\\) 是一个锐角，\\n- **邻边** 是该角相邻的直角边，\\n- **斜边** 是直角三角形的斜边（最长边）。\\n\\n**例子**：若一个角 \\\\(\\\\theta\\\\) 的邻边长为 3，斜边为 5，则 \\\\(\\\\cos \\\\theta = \\\\frac{3}{5}\\\\)。\\n\\n---\\n\\n### 2. **单位圆中的定义**\\n在直角坐标系中，以原点为中心、半径为 1 的单位圆上，余弦值等于角度 \\\\(\\\\theta\\\\) 的终边与圆交点的 **横坐标（x 坐标）**：\\n\\\\[\\n\\\\cos \\\\theta = x\\n\\\\]\\n- 当 \\\\(\\\\theta\\\\) 从 \\\\(0\\\\) 增加到 \\\\(2\\\\pi\\\\)（360°）时，余弦值从 1 递减到 -1，再回到 1，呈现周期性变化。\\n\\n---\\n\\n### 3. **余弦函数的性质**\\n- **周期性**：余弦函数的周期为 \\\\(2\\\\pi\\\\)（或 360°），即 \\\\(\\\\cos(\\\\theta + 2\\\\pi) = \\\\cos \\\\theta\\\\)。\\n- **取值范围**：值域为 \\\\([-1, 1]\\\\)。\\n- **偶函数**：满足 \\\\(\\\\cos(-\\\\theta) = \\\\cos \\\\theta\\\\)，图像关于 y 轴对称。\\n- **与正弦函数的关系**：\\\\(\\\\cos \\\\theta = \\\\sin\\\\left(\\\\theta + \\\\frac{\\\\pi}{2}\\\\right)\\\\)。\\n\\n---\\n\\n### 4. **图像特征**\\n余弦函数的图像（波形图）特点：\\n- **起点**：\\\\(\\\\cos 0 = 1\\\\)。\\n- **零点**：在 \\\\(\\\\theta = \\\\frac{\\\\pi}{2}, \\\\frac{3\\\\pi}{2}, \\\\ldots\\\\) 处值为 0。\\n- **极值点**：在 \\\\(\\\\theta = 0, \\\\pi, 2\\\\pi, \\\\ldots\\\\) 处取得最大值 1 或最小值 -1。\\n\\n---\\n\\n### 5. **应用场景**\\n- **几何**：计算角度或边长。\\n- **物理**：描述简谐振动（如弹簧运动、交流电）。\\n- **工程**：信号处理、傅里叶分析等。\\n\\n---\\n\\n### 示例计算\\n**问题**：求 \\\\(\\\\cos 60^\\\\circ\\\\) 的值。  \\n**解**：  \\n在单位圆中，\\\\(60^\\\\circ\\\\) 对应的坐标为 \\\\(\\\\left(\\\\frac{1}{2}, \\\\frac{\\\\sqrt{3}}{2}\\\\right)\\\\)，因此：\\n\\\\[\\n\\\\cos 60^\\\\circ = \\\\frac{1}{2}\\n\\\\]\\n\\n---\\n\\n通过上述定义和性质，余弦函数成为数学和科学中分析周期性现象的重要工具。如果需要进一步探讨其公式（如余弦定理）或其他扩展内容，可以继续提问！', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 651, 'prompt_tokens': 13, 'total_tokens': 664, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0195d2a9a43046e1ecabf1b587b24599', 'finish_reason': 'stop', 'logprobs': None}, id='run-d92a68be-61cf-4619-8fa3-dd7ff8285b31-0', usage_metadata={'input_tokens': 13, 'output_tokens': 651, 'total_tokens': 664, 'input_token_details': {}, 'output_token_details': {}})])}\n"]}], "source": ["\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder  # 导入聊天提示模板和消息占位符\n", "from langchain_core.runnables.history import RunnableWithMessageHistory  # 导入带历史记录的可运行组件\n", "from langchain_deepseek import ChatDeepSeek\n", "import os\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "# 创建聊天提示模板，包含系统提示、历史记录和用户问题\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"你是一个擅长{ability}的助手\"),  # 系统角色提示，使用ability变量定义助手专长\n", "    MessagesPlaceholder(variable_name=\"history\"),  # 放置历史消息的占位符\n", "    (\"human\", \"{question}\"),  # 用户问题的占位符\n", "])\n", "\n", "# 将提示模板与DS模型连接成一个链\n", "chain = prompt | llm\n", "\n", "# 创建带有消息历史功能的可运行链\n", "chain_with_history = RunnableWithMessageHistory(\n", "    chain,  # 基础链\n", "    # 使用上一个示例中定义的get_by_session_id函数获取历史记录\n", "    get_by_session_id,\n", "    input_messages_key=\"question\",  # 指定输入消息的键名\n", "    history_messages_key=\"history\",  # 指定历史消息的键名\n", ")\n", "\n", "# 首次调用链，询问余弦的含义\n", "print(chain_with_history.invoke(  # noqa: T201\n", "    {\"ability\": \"math\", \"question\": \"余弦函数是什么意思？\"},  # 输入参数\n", "    config={\"configurable\": {\"session_id\": \"foo\"}}  # 配置会话ID为\"foo\"\n", "))\n", "\n", "# 打印存储中的历史记录\n", "# 此时应包含第一次对话的问题和回答\n", "print(store)  "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["content='余弦函数的反函数称为 **反余弦函数**（或 **arccosine**），记作 \\\\(\\\\arccos(x)\\\\) 或 \\\\(\\\\cos^{-1}(x)\\\\)。它的定义和性质如下：\\n\\n---\\n\\n### 1. **定义**\\n反余弦函数是余弦函数在特定定义域上的逆映射，即：\\n\\\\[\\ny = \\\\arccos(x) \\\\quad \\\\Leftrightarrow \\\\quad x = \\\\cos(y)\\n\\\\]\\n其中：\\n- **输入 \\\\(x\\\\)**：取值范围为 \\\\([-1, 1]\\\\)（因为余弦值域是 \\\\([-1, 1]\\\\)）。\\n- **输出 \\\\(y\\\\)**：取值范围为 \\\\([0, \\\\pi]\\\\)（称为“主值分支”），单位为弧度。\\n\\n---\\n\\n### 2. **为什么限制定义域？**\\n余弦函数 \\\\(\\\\cos \\\\theta\\\\) 本身是 **周期性** 和 **非单射** 的（例如 \\\\(\\\\cos 0 = \\\\cos 2\\\\pi = 1\\\\)）。为了使其可逆，必须限制定义域为 \\\\([0, \\\\pi]\\\\)，此时余弦函数是严格单调递减的，从而保证反函数存在且唯一。\\n\\n---\\n\\n### 3. **图像与性质**\\n- **图像**：反余弦函数的图像是余弦函数在 \\\\([0, \\\\pi]\\\\) 上的镜像对称（关于直线 \\\\(y = x\\\\)），如下所示：\\n  - 从点 \\\\((-1, \\\\pi)\\\\) 递减到 \\\\((1, 0)\\\\)。\\n- **性质**：\\n  - **定义域**：\\\\(x \\\\in [-1, 1]\\\\)。\\n  - **值域**：\\\\(y \\\\in [0, \\\\pi]\\\\)（即 \\\\(0^\\\\circ\\\\) 到 \\\\(180^\\\\circ\\\\)）。\\n  - **导数**：\\\\(\\\\frac{d}{dx} \\\\arccos(x) = -\\\\frac{1}{\\\\sqrt{1-x^2}}\\\\)（可通过隐函数求导证明）。\\n\\n---\\n\\n### 4. **计算示例**\\n**问题**：求 \\\\(\\\\arccos\\\\left(-\\\\frac{1}{2}\\\\right)\\\\) 的值。  \\n**解**：  \\n寻找角度 \\\\(y\\\\) 使得 \\\\(\\\\cos(y) = -\\\\frac{1}{2}\\\\)，且在 \\\\([0, \\\\pi]\\\\) 范围内。  \\n已知 \\\\(\\\\cos\\\\left(\\\\frac{2\\\\pi}{3}\\\\right) = -\\\\frac{1}{2}\\\\)，因此：\\n\\\\[\\n\\\\arccos\\\\left(-\\\\frac{1}{2}\\\\right) = \\\\frac{2\\\\pi}{3} \\\\quad (\\\\text{即 } 120^\\\\circ)\\n\\\\]\\n\\n---\\n\\n### 5. **与其他反三角函数的关系**\\n反余弦函数可以通过反正弦函数表示：\\n\\\\[\\n\\\\arccos(x) = \\\\frac{\\\\pi}{2} - \\\\arcsin(x)\\n\\\\]\\n这是因为 \\\\(\\\\cos(y) = \\\\sin\\\\left(\\\\frac{\\\\pi}{2} - y\\\\right)\\\\)。\\n\\n---\\n\\n### 6. **应用场景**\\n- **几何**：已知三角形两边及夹角，求第三边（余弦定理的逆运算）。\\n- **编程**：在计算角度时常用（如 `math.acos()` 函数）。\\n- **信号处理**：解调相位信息。\\n\\n---\\n\\n### 注意事项\\n- 符号 \\\\(\\\\cos^{-1}(x)\\\\) 易与 \\\\(\\\\frac{1}{\\\\cos x}\\\\) 混淆，需根据上下文区分。\\n- 计算器或编程语言通常直接提供 \\\\(\\\\arccos\\\\) 函数，输出单位为弧度或度（需注意设置）。\\n\\n如果需要进一步探讨反余弦函数的导数推导或其他扩展，可以继续提问！' additional_kwargs={'refusal': None} response_metadata={'token_usage': {'completion_tokens': 732, 'prompt_tokens': 671, 'total_tokens': 1403, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0195d2aa6012b40f2d425dc103acb020', 'finish_reason': 'stop', 'logprobs': None} id='run-1cb8a67b-bca4-480f-8ed6-8ee71671223f-0' usage_metadata={'input_tokens': 671, 'output_tokens': 732, 'total_tokens': 1403, 'input_token_details': {}, 'output_token_details': {}}\n", "{'1': InMemoryHistory(messages=[AIMessage(content='你好', additional_kwargs={}, response_metadata={})]), 'foo': InMemoryHistory(messages=[HumanMessage(content='余弦函数是什么意思？', additional_kwargs={}, response_metadata={}), AIMessage(content='余弦函数（通常记作 \\\\(\\\\cos\\\\)）是三角函数的一种，用于描述直角三角形中一个锐角的邻边与斜边的比值，或者在单位圆中表示横坐标与半径的关系。以下是余弦函数的详细解释：\\n\\n### 1. **直角三角形中的定义**\\n在直角三角形中，余弦值定义为：\\n\\\\[\\n\\\\cos \\\\theta = \\\\frac{\\\\text{邻边}}{\\\\text{斜边}}\\n\\\\]\\n其中：\\n- \\\\(\\\\theta\\\\) 是一个锐角，\\n- **邻边** 是该角相邻的直角边，\\n- **斜边** 是直角三角形的斜边（最长边）。\\n\\n**例子**：若一个角 \\\\(\\\\theta\\\\) 的邻边长为 3，斜边为 5，则 \\\\(\\\\cos \\\\theta = \\\\frac{3}{5}\\\\)。\\n\\n---\\n\\n### 2. **单位圆中的定义**\\n在直角坐标系中，以原点为中心、半径为 1 的单位圆上，余弦值等于角度 \\\\(\\\\theta\\\\) 的终边与圆交点的 **横坐标（x 坐标）**：\\n\\\\[\\n\\\\cos \\\\theta = x\\n\\\\]\\n- 当 \\\\(\\\\theta\\\\) 从 \\\\(0\\\\) 增加到 \\\\(2\\\\pi\\\\)（360°）时，余弦值从 1 递减到 -1，再回到 1，呈现周期性变化。\\n\\n---\\n\\n### 3. **余弦函数的性质**\\n- **周期性**：余弦函数的周期为 \\\\(2\\\\pi\\\\)（或 360°），即 \\\\(\\\\cos(\\\\theta + 2\\\\pi) = \\\\cos \\\\theta\\\\)。\\n- **取值范围**：值域为 \\\\([-1, 1]\\\\)。\\n- **偶函数**：满足 \\\\(\\\\cos(-\\\\theta) = \\\\cos \\\\theta\\\\)，图像关于 y 轴对称。\\n- **与正弦函数的关系**：\\\\(\\\\cos \\\\theta = \\\\sin\\\\left(\\\\theta + \\\\frac{\\\\pi}{2}\\\\right)\\\\)。\\n\\n---\\n\\n### 4. **图像特征**\\n余弦函数的图像（波形图）特点：\\n- **起点**：\\\\(\\\\cos 0 = 1\\\\)。\\n- **零点**：在 \\\\(\\\\theta = \\\\frac{\\\\pi}{2}, \\\\frac{3\\\\pi}{2}, \\\\ldots\\\\) 处值为 0。\\n- **极值点**：在 \\\\(\\\\theta = 0, \\\\pi, 2\\\\pi, \\\\ldots\\\\) 处取得最大值 1 或最小值 -1。\\n\\n---\\n\\n### 5. **应用场景**\\n- **几何**：计算角度或边长。\\n- **物理**：描述简谐振动（如弹簧运动、交流电）。\\n- **工程**：信号处理、傅里叶分析等。\\n\\n---\\n\\n### 示例计算\\n**问题**：求 \\\\(\\\\cos 60^\\\\circ\\\\) 的值。  \\n**解**：  \\n在单位圆中，\\\\(60^\\\\circ\\\\) 对应的坐标为 \\\\(\\\\left(\\\\frac{1}{2}, \\\\frac{\\\\sqrt{3}}{2}\\\\right)\\\\)，因此：\\n\\\\[\\n\\\\cos 60^\\\\circ = \\\\frac{1}{2}\\n\\\\]\\n\\n---\\n\\n通过上述定义和性质，余弦函数成为数学和科学中分析周期性现象的重要工具。如果需要进一步探讨其公式（如余弦定理）或其他扩展内容，可以继续提问！', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 651, 'prompt_tokens': 13, 'total_tokens': 664, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0195d2a9a43046e1ecabf1b587b24599', 'finish_reason': 'stop', 'logprobs': None}, id='run-d92a68be-61cf-4619-8fa3-dd7ff8285b31-0', usage_metadata={'input_tokens': 13, 'output_tokens': 651, 'total_tokens': 664, 'input_token_details': {}, 'output_token_details': {}}), HumanMessage(content='它的反函数是什么？', additional_kwargs={}, response_metadata={}), AIMessage(content='余弦函数的反函数称为 **反余弦函数**（或 **arccosine**），记作 \\\\(\\\\arccos(x)\\\\) 或 \\\\(\\\\cos^{-1}(x)\\\\)。它的定义和性质如下：\\n\\n---\\n\\n### 1. **定义**\\n反余弦函数是余弦函数在特定定义域上的逆映射，即：\\n\\\\[\\ny = \\\\arccos(x) \\\\quad \\\\Leftrightarrow \\\\quad x = \\\\cos(y)\\n\\\\]\\n其中：\\n- **输入 \\\\(x\\\\)**：取值范围为 \\\\([-1, 1]\\\\)（因为余弦值域是 \\\\([-1, 1]\\\\)）。\\n- **输出 \\\\(y\\\\)**：取值范围为 \\\\([0, \\\\pi]\\\\)（称为“主值分支”），单位为弧度。\\n\\n---\\n\\n### 2. **为什么限制定义域？**\\n余弦函数 \\\\(\\\\cos \\\\theta\\\\) 本身是 **周期性** 和 **非单射** 的（例如 \\\\(\\\\cos 0 = \\\\cos 2\\\\pi = 1\\\\)）。为了使其可逆，必须限制定义域为 \\\\([0, \\\\pi]\\\\)，此时余弦函数是严格单调递减的，从而保证反函数存在且唯一。\\n\\n---\\n\\n### 3. **图像与性质**\\n- **图像**：反余弦函数的图像是余弦函数在 \\\\([0, \\\\pi]\\\\) 上的镜像对称（关于直线 \\\\(y = x\\\\)），如下所示：\\n  - 从点 \\\\((-1, \\\\pi)\\\\) 递减到 \\\\((1, 0)\\\\)。\\n- **性质**：\\n  - **定义域**：\\\\(x \\\\in [-1, 1]\\\\)。\\n  - **值域**：\\\\(y \\\\in [0, \\\\pi]\\\\)（即 \\\\(0^\\\\circ\\\\) 到 \\\\(180^\\\\circ\\\\)）。\\n  - **导数**：\\\\(\\\\frac{d}{dx} \\\\arccos(x) = -\\\\frac{1}{\\\\sqrt{1-x^2}}\\\\)（可通过隐函数求导证明）。\\n\\n---\\n\\n### 4. **计算示例**\\n**问题**：求 \\\\(\\\\arccos\\\\left(-\\\\frac{1}{2}\\\\right)\\\\) 的值。  \\n**解**：  \\n寻找角度 \\\\(y\\\\) 使得 \\\\(\\\\cos(y) = -\\\\frac{1}{2}\\\\)，且在 \\\\([0, \\\\pi]\\\\) 范围内。  \\n已知 \\\\(\\\\cos\\\\left(\\\\frac{2\\\\pi}{3}\\\\right) = -\\\\frac{1}{2}\\\\)，因此：\\n\\\\[\\n\\\\arccos\\\\left(-\\\\frac{1}{2}\\\\right) = \\\\frac{2\\\\pi}{3} \\\\quad (\\\\text{即 } 120^\\\\circ)\\n\\\\]\\n\\n---\\n\\n### 5. **与其他反三角函数的关系**\\n反余弦函数可以通过反正弦函数表示：\\n\\\\[\\n\\\\arccos(x) = \\\\frac{\\\\pi}{2} - \\\\arcsin(x)\\n\\\\]\\n这是因为 \\\\(\\\\cos(y) = \\\\sin\\\\left(\\\\frac{\\\\pi}{2} - y\\\\right)\\\\)。\\n\\n---\\n\\n### 6. **应用场景**\\n- **几何**：已知三角形两边及夹角，求第三边（余弦定理的逆运算）。\\n- **编程**：在计算角度时常用（如 `math.acos()` 函数）。\\n- **信号处理**：解调相位信息。\\n\\n---\\n\\n### 注意事项\\n- 符号 \\\\(\\\\cos^{-1}(x)\\\\) 易与 \\\\(\\\\frac{1}{\\\\cos x}\\\\) 混淆，需根据上下文区分。\\n- 计算器或编程语言通常直接提供 \\\\(\\\\arccos\\\\) 函数，输出单位为弧度或度（需注意设置）。\\n\\n如果需要进一步探讨反余弦函数的导数推导或其他扩展，可以继续提问！', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 732, 'prompt_tokens': 671, 'total_tokens': 1403, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0195d2aa6012b40f2d425dc103acb020', 'finish_reason': 'stop', 'logprobs': None}, id='run-1cb8a67b-bca4-480f-8ed6-8ee71671223f-0', usage_metadata={'input_tokens': 671, 'output_tokens': 732, 'total_tokens': 1403, 'input_token_details': {}, 'output_token_details': {}})])}\n"]}], "source": ["\n", "# 第二次调用链，询问余弦的反函数\n", "# 由于使用相同的会话ID，模型可以参考前一次对话的上下文\n", "print(chain_with_history.invoke(  \n", "    {\"ability\": \"math\", \"question\": \"它的反函数是什么？\"},  # 输入参数\n", "    config={\"configurable\": {\"session_id\": \"foo\"}}  # 使用相同的会话ID\n", "))\n", "# 再次打印存储中的历史记录\n", "# 此时应包含两次对话的完整历史\n", "print(store) "]}, {"cell_type": "markdown", "metadata": {}, "source": ["增加用户与对话ID，精准控制记忆"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='关于李白妻子的姓名，历史记载并不详尽，但根据有限的史料和后世研究，可以整理出以下信息：\\n\\n1. **许氏**  \\n   李白在27岁左右（约727年）定居安陆（今湖北安陆），与当地名门许氏结婚。许氏的祖父许圉师是唐高宗时期的宰相。这段婚姻持续约十年，许氏育有一子一女（伯禽、平阳）。许氏去世后，李白离开安陆。\\n\\n2. **宗氏**  \\n   约750年，李白在梁园（今河南开封）与武则天时期宰相宗楚客的孙女宗氏再婚。两人感情深厚，宗氏曾为李白的仕途奔走。安史之乱后，李白因卷入永王李璘案被流放，宗氏为其奔走求救。此后宗氏出家为道，李白晚年漂泊，两人未再团聚。\\n\\n3. **其他记载**  \\n   有野史提及李白可能另有妻室（如“刘氏”），但缺乏可靠证据。郭沫若在《李白与杜甫》中推测李白可能有过多次婚姻，但具体姓名无考。\\n\\n**结论**：  \\n李白两位有明确记载的妻子为 **许氏** 和 **宗氏**，均出身唐代仕宦家族。由于唐代女性名字多不传于史，她们的完整姓名已不可考。其他说法多为后世演绎，需谨慎对待。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 299, 'prompt_tokens': 14, 'total_tokens': 313, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0195d2ab10b4061bf5076541aa9158a4', 'finish_reason': 'stop', 'logprobs': None}, id='run-f7dcce6c-5ad7-4621-8b2a-9039388cfde5-0', usage_metadata={'input_tokens': 14, 'output_tokens': 299, 'total_tokens': 313, 'input_token_details': {}, 'output_token_details': {}})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_deepseek import ChatDeepSeek\n", "from langchain_core.runnables import (\n", "    ConfigurableFieldSpec,\n", ")\n", "import os\n", "\n", "llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")\n", "\n", "store = {}  # 创建空字典用于存储不同用户和对话的历史记录\n", "\n", "def get_session_history(\n", "    user_id: str, conversation_id: str\n", ") -> BaseChatMessageHistory:\n", "    \"\"\"\n", "    根据用户ID和对话ID获取聊天历史记录\n", "    如果不存在则创建新的历史记录对象\n", "    \n", "    参数:\n", "        user_id: 用户的唯一标识符\n", "        conversation_id: 对话的唯一标识符\n", "    \n", "    返回:\n", "        对应的聊天历史记录对象\n", "    \"\"\"\n", "    if (user_id, conversation_id) not in store:\n", "        store[(user_id, conversation_id)] = InMemoryHistory()\n", "    return store[(user_id, conversation_id)]\n", "\n", "# 创建聊天提示模板，包含系统提示、历史记录和用户问题\n", "prompt = ChatPromptTemplate.from_messages([\n", "    (\"system\", \"你是一个擅长{ability}的助手\"),  # 系统角色提示\n", "    MessagesPlaceholder(variable_name=\"history\"),  # 历史消息占位符\n", "    (\"human\", \"{question}\"),  # 用户问题占位符\n", "])\n", "\n", "# 将提示模板与DS模型连接成一个链\n", "chain = prompt | llm\n", "\n", "# 创建带有消息历史功能的可运行链，支持用户ID和对话ID配置\n", "with_message_history = RunnableWithMessageHistory(\n", "    chain,  # 基础链\n", "    get_session_history=get_session_history,  # 获取历史记录的函数\n", "    input_messages_key=\"question\",  # 输入消息的键名\n", "    history_messages_key=\"history\",  # 历史消息的键名\n", "    history_factory_config=[  # 历史记录工厂配置\n", "        ConfigurableFieldSpec(\n", "            id=\"user_id\",  # 配置字段ID\n", "            annotation=str,  # 类型注解\n", "            name=\"用户ID\",  # 字段名称\n", "            description=\"用户的唯一标识符\",  # 字段描述\n", "            default=\"\",  # 默认值\n", "            is_shared=True,  # 是否在多个调用间共享\n", "        ),\n", "        ConfigurableFieldSpec(\n", "            id=\"conversation_id\",  # 配置字段ID\n", "            annotation=str,  # 类型注解\n", "            name=\"对话ID\",  # 字段名称\n", "            description=\"对话的唯一标识符\",  # 字段描述\n", "            default=\"\",  # 默认值\n", "            is_shared=True,  # 是否在多个调用间共享\n", "        ),\n", "    ],\n", ")\n", "\n", "# 调用链，询问余弦的含义\n", "# 指定用户ID为\"123\"，对话ID为\"1\"\n", "with_message_history.invoke(\n", "    {\"ability\": \"数学\", \"question\": \"李白的老婆叫什么？\"},  # 输入参数\n", "    config={\"configurable\": {\"user_id\": \"123\", \"conversation_id\": \"2\"}}  # 配置参数\n", ")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{('123', '2'): InMemoryHistory(messages=[HumanMessage(content='李白的老婆叫什么？', additional_kwargs={}, response_metadata={}), AIMessage(content='关于李白妻子的姓名，历史记载并不详尽，但根据有限的史料和后世研究，可以整理出以下信息：\\n\\n1. **许氏**  \\n   李白在27岁左右（约727年）定居安陆（今湖北安陆），与当地名门许氏结婚。许氏的祖父许圉师是唐高宗时期的宰相。这段婚姻持续约十年，许氏育有一子一女（伯禽、平阳）。许氏去世后，李白离开安陆。\\n\\n2. **宗氏**  \\n   约750年，李白在梁园（今河南开封）与武则天时期宰相宗楚客的孙女宗氏再婚。两人感情深厚，宗氏曾为李白的仕途奔走。安史之乱后，李白因卷入永王李璘案被流放，宗氏为其奔走求救。此后宗氏出家为道，李白晚年漂泊，两人未再团聚。\\n\\n3. **其他记载**  \\n   有野史提及李白可能另有妻室（如“刘氏”），但缺乏可靠证据。郭沫若在《李白与杜甫》中推测李白可能有过多次婚姻，但具体姓名无考。\\n\\n**结论**：  \\n李白两位有明确记载的妻子为 **许氏** 和 **宗氏**，均出身唐代仕宦家族。由于唐代女性名字多不传于史，她们的完整姓名已不可考。其他说法多为后世演绎，需谨慎对待。', additional_kwargs={'refusal': None}, response_metadata={'token_usage': {'completion_tokens': 299, 'prompt_tokens': 14, 'total_tokens': 313, 'completion_tokens_details': None, 'prompt_tokens_details': None}, 'model_name': 'Pro/deepseek-ai/DeepSeek-V3', 'system_fingerprint': '', 'id': '0195d2ab10b4061bf5076541aa9158a4', 'finish_reason': 'stop', 'logprobs': None}, id='run-f7dcce6c-5ad7-4621-8b2a-9039388cfde5-0', usage_metadata={'input_tokens': 14, 'output_tokens': 299, 'total_tokens': 313, 'input_token_details': {}, 'output_token_details': {}})])}\n"]}], "source": ["print(store)  # 输出存储的历史记录"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 使用Redis构建长期记忆\n", "***\n", "- 安装redis\n", "- 运行redis服务\n", "- 配置长期记忆"]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](redis.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["启动redis服务器：\n", "注意，因为使用了Docker，所以这里代码略有不同"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["! pip install -qU langchain-redis langchain-openai redis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["测试redis连接正常"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connecting to Redis at: redis://redis:6379\n"]}], "source": ["import os\n", "\n", "# Use the environment variable if set, otherwise default to localhost\n", "REDIS_URL = \"redis://redis:6379\"\n", "print(f\"Connecting to Redis at: {REDIS_URL}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["依赖包"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from langchain_core.chat_history import BaseChatMessageHistory\n", "from langchain_core.messages import AIMessage, HumanMessage\n", "from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder\n", "from langchain_core.runnables.history import RunnableWithMessageHistory\n", "from langchain_redis import RedisChatMessageHistory\n", "from langchain_deepseek import ChatDeepSeek\n", "import os"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用ds"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["llm = ChatDeepSeek(\n", "    model=\"Pro/deepseek-ai/DeepSeek-V3\",\n", "    temperature=0,\n", "    api_key=os.environ.get(\"DEEPSEEK_API_KEY\"),\n", "    api_base=os.environ.get(\"DEEPSEEK_API_BASE\"),\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["最简单的使用"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["聊天历史：\n", "HumanMessage: 你好，AI助手！2222\n", "AIMessage: 你好！我今天能为你提供什么帮助？222\n"]}], "source": ["# 初始化 Redis 聊天消息历史记录\n", "# 使用 Redis 存储聊天历史，需要提供会话 ID 和 Redis 连接 URL\n", "history = RedisChatMessageHistory(session_id=\"user_123\", redis_url=REDIS_URL)\n", "#history.clear()  # 首先清空历史记录\n", "# 向历史记录中添加消息\n", "history.add_user_message(\"你好，AI助手！2222\")  # 添加用户消息\n", "history.add_ai_message(\"你好！我今天能为你提供什么帮助？222\")  # 添加AI回复消息\n", "\n", "# 检索并显示历史消息\n", "print(\"聊天历史：\")\n", "for message in history.messages:\n", "    # 打印每条消息的类型和内容\n", "    print(f\"{type(message).__name__}: {message.content}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.6"}}, "nbformat": 4, "nbformat_minor": 4}