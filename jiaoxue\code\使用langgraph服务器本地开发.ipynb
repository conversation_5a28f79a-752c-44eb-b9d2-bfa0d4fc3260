{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 使用langgraph服务器进行本地开发\n", "****\n", "- 从模板创建新的应用程序react-agent。此模板是一个简单的代理，可以灵活地扩展到许多工具。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! pip install --upgrade \"langgraph-cli[inmem]\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[31m\u001b[1m❌ The specified directory already exists and is not empty. Aborting to prevent overwriting files.\u001b[0m\n"]}], "source": ["! langgraph new react-agent-python --template react-agent-python"]}, {"cell_type": "markdown", "metadata": {}, "source": ["安装项目依赖"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Obtaining file:///Volumes/MOVESPEED/AI%E8%AF%BE%E7%A8%8B/localCode/code/react-agent-python\n", "  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Checking if build backend supports build_editable ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build editable ... \u001b[?25ldone\n", "\u001b[?25h  Preparing editable metadata (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25hRequirement already satisfied: langgraph>=0.2.6 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from react-agent==0.0.1) (0.2.76)\n", "Requirement already satisfied: langchain-openai>=0.1.22 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from react-agent==0.0.1) (0.3.11)\n", "Requirement already satisfied: langchain-anthropic>=0.1.23 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from react-agent==0.0.1) (0.3.10)\n", "Requirement already satisfied: langchain>=0.2.14 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from react-agent==0.0.1) (0.3.22)\n", "Requirement already satisfied: langchain-fireworks>=0.1.7 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from react-agent==0.0.1) (0.2.9)\n", "Requirement already satisfied: python-dotenv>=1.0.1 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from react-agent==0.0.1) (1.1.0)\n", "Requirement already satisfied: langchain-community>=0.2.17 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from react-agent==0.0.1) (0.3.20)\n", "Requirement already satisfied: tavily-python>=0.4.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from react-agent==0.0.1) (0.5.3)\n", "Requirement already satisfied: langchain-core<1.0.0,>=0.3.49 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain>=0.2.14->react-agent==0.0.1) (0.3.49)\n", "Requirement already satisfied: langchain-text-splitters<1.0.0,>=0.3.7 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain>=0.2.14->react-agent==0.0.1) (0.3.7)\n", "Requirement already satisfied: langsmith<0.4,>=0.1.17 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain>=0.2.14->react-agent==0.0.1) (0.3.22)\n", "Requirement already satisfied: pydantic<3.0.0,>=2.7.4 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain>=0.2.14->react-agent==0.0.1) (2.11.1)\n", "Requirement already satisfied: SQLAlchemy<3,>=1.4 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain>=0.2.14->react-agent==0.0.1) (2.0.40)\n", "Requirement already satisfied: requests<3,>=2 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain>=0.2.14->react-agent==0.0.1) (2.32.3)\n", "Requirement already satisfied: PyYAML>=5.3 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain>=0.2.14->react-agent==0.0.1) (6.0.2)\n", "Requirement already satisfied: anthropic<1,>=0.49.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-anthropic>=0.1.23->react-agent==0.0.1) (0.49.0)\n", "Requirement already satisfied: aiohttp<4.0.0,>=3.8.3 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-community>=0.2.17->react-agent==0.0.1) (3.11.15)\n", "Requirement already satisfied: tenacity!=8.4.0,<10,>=8.1.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-community>=0.2.17->react-agent==0.0.1) (9.0.0)\n", "Requirement already satisfied: dataclasses-json<0.7,>=0.5.7 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-community>=0.2.17->react-agent==0.0.1) (0.6.7)\n", "Requirement already satisfied: pydantic-settings<3.0.0,>=2.4.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-community>=0.2.17->react-agent==0.0.1) (2.8.1)\n", "Requirement already satisfied: httpx-sse<1.0.0,>=0.4.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-community>=0.2.17->react-agent==0.0.1) (0.4.0)\n", "Requirement already satisfied: numpy<3,>=1.26.2 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-community>=0.2.17->react-agent==0.0.1) (2.2.4)\n", "Requirement already satisfied: fireworks-ai>=0.13.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-fireworks>=0.1.7->react-agent==0.0.1) (0.15.12)\n", "Requirement already satisfied: openai<2.0.0,>=1.10.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-fireworks>=0.1.7->react-agent==0.0.1) (1.70.0)\n", "Requirement already satisfied: tiktoken<1,>=0.7 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-openai>=0.1.22->react-agent==0.0.1) (0.9.0)\n", "Requirement already satisfied: langgraph-checkpoint<3.0.0,>=2.0.10 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langgraph>=0.2.6->react-agent==0.0.1) (2.0.23)\n", "Requirement already satisfied: langgraph-sdk<0.2.0,>=0.1.42 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langgraph>=0.2.6->react-agent==0.0.1) (0.1.60)\n", "Requirement already satisfied: httpx in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from tavily-python>=0.4.0->react-agent==0.0.1) (0.28.1)\n", "Requirement already satisfied: aiohappyeyeballs>=2.3.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community>=0.2.17->react-agent==0.0.1) (2.6.1)\n", "Requirement already satisfied: aiosignal>=1.1.2 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community>=0.2.17->react-agent==0.0.1) (1.3.2)\n", "Requirement already satisfied: attrs>=17.3.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community>=0.2.17->react-agent==0.0.1) (25.3.0)\n", "Requirement already satisfied: frozenlist>=1.1.1 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community>=0.2.17->react-agent==0.0.1) (1.5.0)\n", "Requirement already satisfied: multidict<7.0,>=4.5 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community>=0.2.17->react-agent==0.0.1) (6.3.1)\n", "Requirement already satisfied: propcache>=0.2.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community>=0.2.17->react-agent==0.0.1) (0.3.1)\n", "Requirement already satisfied: yarl<2.0,>=1.17.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from aiohttp<4.0.0,>=3.8.3->langchain-community>=0.2.17->react-agent==0.0.1) (1.18.3)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic>=0.1.23->react-agent==0.0.1) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic>=0.1.23->react-agent==0.0.1) (1.9.0)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic>=0.1.23->react-agent==0.0.1) (0.9.0)\n", "Requirement already satisfied: sniffio in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic>=0.1.23->react-agent==0.0.1) (1.3.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.10 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from anthropic<1,>=0.49.0->langchain-anthropic>=0.1.23->react-agent==0.0.1) (4.13.0)\n", "Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community>=0.2.17->react-agent==0.0.1) (3.26.1)\n", "Requirement already satisfied: typing-inspect<1,>=0.4.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from dataclasses-json<0.7,>=0.5.7->langchain-community>=0.2.17->react-agent==0.0.1) (0.9.0)\n", "Requirement already satisfied: httpx-ws in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from fireworks-ai>=0.13.0->langchain-fireworks>=0.1.7->react-agent==0.0.1) (0.7.2)\n", "Requirement already satisfied: Pillow in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from fireworks-ai>=0.13.0->langchain-fireworks>=0.1.7->react-agent==0.0.1) (11.1.0)\n", "Requirement already satisfied: certifi in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from httpx->tavily-python>=0.4.0->react-agent==0.0.1) (2025.1.31)\n", "Requirement already satisfied: httpcore==1.* in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from httpx->tavily-python>=0.4.0->react-agent==0.0.1) (1.0.7)\n", "Requirement already satisfied: idna in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from httpx->tavily-python>=0.4.0->react-agent==0.0.1) (3.10)\n", "Requirement already satisfied: h11<0.15,>=0.13 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from httpcore==1.*->httpx->tavily-python>=0.4.0->react-agent==0.0.1) (0.14.0)\n", "Requirement already satisfied: jsonpatch<2.0,>=1.33 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.49->langchain>=0.2.14->react-agent==0.0.1) (1.33)\n", "Requirement already satisfied: packaging<25,>=23.2 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langchain-core<1.0.0,>=0.3.49->langchain>=0.2.14->react-agent==0.0.1) (24.2)\n", "Requirement already satisfied: ormsgpack<2.0.0,>=1.8.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langgraph-checkpoint<3.0.0,>=2.0.10->langgraph>=0.2.6->react-agent==0.0.1) (1.9.1)\n", "Requirement already satisfied: orjson>=3.10.1 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langgraph-sdk<0.2.0,>=0.1.42->langgraph>=0.2.6->react-agent==0.0.1) (3.10.16)\n", "Requirement already satisfied: requests-toolbelt<2.0.0,>=1.0.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.17->langchain>=0.2.14->react-agent==0.0.1) (1.0.0)\n", "Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from langsmith<0.4,>=0.1.17->langchain>=0.2.14->react-agent==0.0.1) (0.23.0)\n", "Requirement already satisfied: tqdm>4 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from openai<2.0.0,>=1.10.0->langchain-fireworks>=0.1.7->react-agent==0.0.1) (4.67.1)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain>=0.2.14->react-agent==0.0.1) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain>=0.2.14->react-agent==0.0.1) (2.33.0)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.4->langchain>=0.2.14->react-agent==0.0.1) (0.4.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain>=0.2.14->react-agent==0.0.1) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from requests<3,>=2->langchain>=0.2.14->react-agent==0.0.1) (2.3.0)\n", "Requirement already satisfied: regex>=2022.1.18 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from tiktoken<1,>=0.7->langchain-openai>=0.1.22->react-agent==0.0.1) (2024.11.6)\n", "Requirement already satisfied: jsonpointer>=1.9 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from jsonpatch<2.0,>=1.33->langchain-core<1.0.0,>=0.3.49->langchain>=0.2.14->react-agent==0.0.1) (3.0.0)\n", "Requirement already satisfied: mypy-extensions>=0.3.0 in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from typing-inspect<1,>=0.4.0->dataclasses-json<0.7,>=0.5.7->langchain-community>=0.2.17->react-agent==0.0.1) (1.0.0)\n", "Requirement already satisfied: wsproto in /Volumes/MOVESPEED/AI课程/localCode/.venv/lib/python3.13/site-packages (from httpx-ws->fireworks-ai>=0.13.0->langchain-fireworks>=0.1.7->react-agent==0.0.1) (1.2.0)\n", "Building wheels for collected packages: react-agent\n", "  Building editable for react-agent (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for react-agent: filename=react_agent-0.0.1-0.editable-py3-none-any.whl size=7010 sha256=41377bd3977dbd6b7d2d19980562404061d739f5470d8745046698f6747374b0\n", "  Stored in directory: /private/var/folders/gp/1nc10nrd3kjfxp5kkm9h93l80000gn/T/pip-ephem-wheel-cache-56vunzg3/wheels/47/05/f6/7db43f4787e989efc38ba4ea8826c075d401e9ed278139a3fb\n", "Successfully built react-agent\n", "Installing collected packages: react-agent\n", "  Attempting uninstall: react-agent\n", "    Found existing installation: react-agent 0.0.1\n", "    Uninstalling react-agent-0.0.1:\n", "      Successfully uninstalled react-agent-0.0.1\n", "Successfully installed react-agent-0.0.1\n"]}], "source": ["! cd react-agent-python && pip install -e ."]}, {"cell_type": "markdown", "metadata": {}, "source": ["在该文件夹下创建环境配置文件\n", "***\n", "- .env"]}, {"cell_type": "markdown", "metadata": {}, "source": ["启动服务器\n", "*****"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INFO:langgraph_api.cli:\n", "\n", "        Welcome to\n", "\n", "╦  ┌─┐┌┐┌┌─┐╔═╗┬─┐┌─┐┌─┐┬ ┬\n", "║  ├─┤││││ ┬║ ╦├┬┘├─┤├─┘├─┤\n", "╩═╝┴ ┴┘└┘└─┘╚═╝┴└─┴ ┴┴  ┴ ┴\n", "\n", "- 🚀 API: \u001b[36mhttp://127.0.0.1:2024\u001b[0m\n", "- 🎨 Studio UI: \u001b[36mhttps://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024\u001b[0m\n", "- 📚 API Docs: \u001b[36mhttp://127.0.0.1:2024/docs\u001b[0m\n", "\n", "This in-memory server is designed for development and testing.\n", "For production use, please use LangGraph Cloud.\n", "\n", "\n", "\u001b[2m2025-04-03T08:10:21.197268Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWill watch for changes in these directories: ['/Volumes/MOVESPEED/AI课程/localCode/code/react-agent-python']\u001b[0m [\u001b[0m\u001b[1m\u001b[34muvicorn.error\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.197544Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mUvicorn running on http://127.0.0.1:2024 (Press CTRL+C to quit)\u001b[0m [\u001b[0m\u001b[1m\u001b[34muvicorn.error\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mcolor_message\u001b[0m=\u001b[35m'Uvicorn running on \\x1b[1m%s://%s:%d\\x1b[0m (Press CTRL+C to quit)'\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.197660Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarted reloader process [79443] using WatchFiles\u001b[0m [\u001b[0m\u001b[1m\u001b[34muvicorn.error\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mcolor_message\u001b[0m=\u001b[35m'Started reloader process [\\x1b[36m\\x1b[1m79443\\x1b[0m] using \\x1b[36m\\x1b[1mWatchFiles\\x1b[0m'\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.552435Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mUsing auth of type=noop       \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.auth.middleware\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.554575Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarted server process [79454]\u001b[0m [\u001b[0m\u001b[1m\u001b[34muvicorn.error\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mcolor_message\u001b[0m=\u001b[35m'Started server process [\\x1b[36m%d\\x1b[0m]'\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.554618Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWaiting for application startup.\u001b[0m [\u001b[0m\u001b[1m\u001b[34muvicorn.error\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.689647Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mRegistering graph with id 'agent'\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.graph\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mgraph_id\u001b[0m=\u001b[35magent\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.689879Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarting metadata loop        \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.metadata\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.702211Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarting 1 background workers \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.702321Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarting thread TTL sweeper with interval 5 minutes\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.thread_ttl\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36minterval_minutes\u001b[0m=\u001b[35m5\u001b[0m \u001b[36mstrategy\u001b[0m=\u001b[35mdelete\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.721689Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mApplication startup complete. \u001b[0m [\u001b[0m\u001b[1m\u001b[34muvicorn.error\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:21.722036Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWorker stats                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mactive\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mavailable\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmax\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:10:22.223503Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mQueue stats                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mmax_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mmed_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mn_pending\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mn_running\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:10:22.223908Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mSweeped runs                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_ids\u001b[0m=\u001b[35m[]\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:22.290579Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.smith.langchain.com/v1/metadata/submit \"HTTP/1.1 204 No Content\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:32.390231Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /info 200 6ms             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m6\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/info\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '60', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:10:32.392386Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGetting auth instance: None   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.auth.custom\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlanggraph_auth\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:32.393464Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /assistants/search 200 6ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m6\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '25', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '265', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:10:32.466994Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 44ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m44\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:10:32.477114Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/search 200 54ms \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m54\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '39', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:10:32.481055Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 12ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m12\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:10:32.481992Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 12ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m12\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:10:45.756203Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads 200 2ms         \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m2\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '15', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '204', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:10:45.770450Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mCreated run                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.ops\u001b[0m]\u001b[0m \u001b[36mafter_seconds\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36massistant_id\u001b[0m=\u001b[35mfe096781-5601-53d2-b2f6-0d3403f7e9ca\u001b[0m \u001b[36mif_not_exists\u001b[0m=\u001b[35mreject\u001b[0m \u001b[36mmultitask_strategy\u001b[0m=\u001b[35mrollback\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010632-509f-62e2-b450-9f5c0e806fb8\u001b[0m \u001b[36mstream_mode\u001b[0m=\u001b[35m['debug', 'messages']\u001b[0m \u001b[36mtemporary\u001b[0m=\u001b[35mFalse\u001b[0m \u001b[36mthread_id\u001b[0m=\u001b[35m003dfdf1-55b6-48d5-9b02-55a6fd19318d\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:45.918484Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs 200 141ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m141\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '1503', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:10:45.920471Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history 200 145ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m145\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:10:45.921192Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history 200 143ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m143\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:10:45.921828Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs 200 0ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '1503', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:10:46.274809Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarting background run       \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:10:45.770394+00:00\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010632-509f-62e2-b450-9f5c0e806fb8\u001b[0m \u001b[36mrun_queue_ms\u001b[0m=\u001b[35m503\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:10:46.274385+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:10:46.623699Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 22ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m22\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:10:46.623839Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 22ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m22\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:10:46.624050Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 22ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m22\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:10:48.038118Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.openai-proxy.org/anthropic/v1/messages \"HTTP/1.1 200 OK\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:10:49.383543Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mBackground run succeeded      \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:10:45.770394+00:00\u001b[0m \u001b[36mrun_ended_at\u001b[0m=\u001b[35m2025-04-03T08:10:49.383418+00:00\u001b[0m \u001b[36mrun_exec_ms\u001b[0m=\u001b[35m3109\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010632-509f-62e2-b450-9f5c0e806fb8\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:10:46.274385+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:10:49.385140Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs/stream 200 3615ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3615\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs/stream\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '566', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'location': '/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs/1f010632-509f-62e2-b450-9f5c0e806fb8/stream', 'cache-control': 'no-store', 'connection': 'keep-alive', 'x-accel-buffering': 'no', 'content-type': 'text/event-stream; charset=utf-8'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs/stream\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:10:49.448381Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 50ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m50\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:10:49.449442Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 51ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m51\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:10:49.450150Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 51ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m51\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:10:49.451026Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs 200 3ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:10:49.452673Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history 200 5ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m5\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '6885', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:11:00.167089Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mCreated run                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.ops\u001b[0m]\u001b[0m \u001b[36mafter_seconds\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36massistant_id\u001b[0m=\u001b[35mfe096781-5601-53d2-b2f6-0d3403f7e9ca\u001b[0m \u001b[36mif_not_exists\u001b[0m=\u001b[35mreject\u001b[0m \u001b[36mmultitask_strategy\u001b[0m=\u001b[35mrollback\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010632-d9eb-637a-b15d-f28248f6564c\u001b[0m \u001b[36mstream_mode\u001b[0m=\u001b[35m['debug', 'messages']\u001b[0m \u001b[36mtemporary\u001b[0m=\u001b[35mFalse\u001b[0m \u001b[36mthread_id\u001b[0m=\u001b[35m003dfdf1-55b6-48d5-9b02-55a6fd19318d\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:11:00.171657Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs 200 0ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '1621', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:11:00.173689Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history 200 2ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m2\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '6885', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:11:00.804861Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarting background run       \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:11:00.167052+00:00\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010632-d9eb-637a-b15d-f28248f6564c\u001b[0m \u001b[36mrun_queue_ms\u001b[0m=\u001b[35m637\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:11:00.804516+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:11:00.883090Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 37ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m37\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:11:00.883872Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 37ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m37\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:11:00.884149Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 1ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:11:02.430989Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.openai-proxy.org/anthropic/v1/messages \"HTTP/1.1 200 OK\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:11:04.713867Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 38ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m38\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:11:04.715306Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 39ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m39\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:11:04.715565Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 39ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m39\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:11:09.307065Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 54ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m54\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:11:09.307677Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 54ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m54\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:11:09.308013Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 55ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m55\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:11:11.552509Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.openai-proxy.org/anthropic/v1/messages \"HTTP/1.1 200 OK\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:11:18.102410Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mBackground run succeeded      \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:11:00.167052+00:00\u001b[0m \u001b[36mrun_ended_at\u001b[0m=\u001b[35m2025-04-03T08:11:18.102250+00:00\u001b[0m \u001b[36mrun_exec_ms\u001b[0m=\u001b[35m17297\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010632-d9eb-637a-b15d-f28248f6564c\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:11:00.804516+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:11:18.103793Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs/stream 200 17950ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m17950\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs/stream\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '684', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'location': '/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs/1f010632-d9eb-637a-b15d-f28248f6564c/stream', 'cache-control': 'no-store', 'connection': 'keep-alive', 'x-accel-buffering': 'no', 'content-type': 'text/event-stream; charset=utf-8'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs/stream\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:11:18.172911Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 53ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m53\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:11:18.173698Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 54ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m54\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:11:18.174268Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 53ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m53\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:11:18.175586Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs 200 1ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:11:18.178057Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history 200 4ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '111173', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:11:21.833286Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWorker stats                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mactive\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mavailable\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmax\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:11:22.334883Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mQueue stats                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mmax_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mmed_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mn_pending\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mn_running\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:11:24.537046Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /info 200 1ms             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/info\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '60', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:11:24.540616Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d 200 0ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '24789', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:11:24.541336Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /assistants/search 200 4ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '25', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '265', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:11:24.605280Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 47ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m47\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:11:24.606362Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs 200 48ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m48\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:11:24.609752Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/search 200 53ms \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m53\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '39', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '24791', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:11:24.613607Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 6ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m6\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:11:24.616132Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 8ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m8\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:11:24.618008Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history 200 61ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m61\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/003dfdf1-55b6-48d5-9b02-55a6fd19318d/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '003dfdf1-55b6-48d5-9b02-55a6fd19318d'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '111173', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:12:04.697341Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /info 200 3ms             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/info\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '60', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:12:04.699052Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /assistants/search 200 3ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '25', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '265', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:12:04.777394Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 50ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m50\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:12:04.781009Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/search 200 52ms \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m52\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '39', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '24791', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:12:04.781791Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 3ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:12:04.782449Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 4ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:12:07.936475Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 53ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m53\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:12:07.937641Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 54ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m54\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:12:07.938043Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 53ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m53\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:12:07.939107Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /assistants/search 200 56ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m56\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '25', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '265', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:12:07.939545Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/search 200 56ms \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m56\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '39', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '24791', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:12:21.964499Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWorker stats                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mactive\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mavailable\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmax\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:12:22.466352Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mQueue stats                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mmax_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mmed_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mn_pending\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mn_running\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:12:28.131918Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads 200 1ms         \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '15', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '204', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:12:28.146253Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mCreated run                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.ops\u001b[0m]\u001b[0m \u001b[36mafter_seconds\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36massistant_id\u001b[0m=\u001b[35mfe096781-5601-53d2-b2f6-0d3403f7e9ca\u001b[0m \u001b[36mif_not_exists\u001b[0m=\u001b[35mreject\u001b[0m \u001b[36mmultitask_strategy\u001b[0m=\u001b[35mrollback\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010636-20f3-6dc8-a684-fe2cda94e529\u001b[0m \u001b[36mstream_mode\u001b[0m=\u001b[35m['debug', 'messages']\u001b[0m \u001b[36mtemporary\u001b[0m=\u001b[35mFalse\u001b[0m \u001b[36mthread_id\u001b[0m=\u001b[35m6917ab1b-b817-4b03-8cf3-55c07c29907f\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:12:28.152142Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs 200 1ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '1503', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:12:28.155750Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs 200 2ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m2\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '1503', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:12:28.156055Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 5ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m5\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:12:28.156904Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 6ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m6\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:12:28.982348Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarting background run       \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:12:28.146204+00:00\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010636-20f3-6dc8-a684-fe2cda94e529\u001b[0m \u001b[36mrun_queue_ms\u001b[0m=\u001b[35m835\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:12:28.982071+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:12:29.102026Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 25ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m25\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:12:29.102607Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 26ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m26\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:12:29.102903Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 26ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m26\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:12:30.808601Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.openai-proxy.org/anthropic/v1/messages \"HTTP/1.1 200 OK\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:12:32.086238Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mBackground run succeeded      \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:12:28.146204+00:00\u001b[0m \u001b[36mrun_ended_at\u001b[0m=\u001b[35m2025-04-03T08:12:32.086148+00:00\u001b[0m \u001b[36mrun_exec_ms\u001b[0m=\u001b[35m3104\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010636-20f3-6dc8-a684-fe2cda94e529\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:12:28.982071+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:12:32.087347Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/stream 200 3942ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3942\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/stream\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '566', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'location': '/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/1f010636-20f3-6dc8-a684-fe2cda94e529/stream', 'cache-control': 'no-store', 'connection': 'keep-alive', 'x-accel-buffering': 'no', 'content-type': 'text/event-stream; charset=utf-8'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs/stream\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:12:32.108475Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs 200 1ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:12:32.111105Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 3ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '6903', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:12:32.216132Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 99ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m99\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:12:32.217228Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 102ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m102\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:12:32.217912Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 102ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m102\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:03.123387Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mCreated run                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.ops\u001b[0m]\u001b[0m \u001b[36mafter_seconds\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36massistant_id\u001b[0m=\u001b[35mfe096781-5601-53d2-b2f6-0d3403f7e9ca\u001b[0m \u001b[36mif_not_exists\u001b[0m=\u001b[35mreject\u001b[0m \u001b[36mmultitask_strategy\u001b[0m=\u001b[35mrollback\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010637-6e85-673e-b81f-c527c07dce1a\u001b[0m \u001b[36mstream_mode\u001b[0m=\u001b[35m['debug', 'messages']\u001b[0m \u001b[36mtemporary\u001b[0m=\u001b[35mFalse\u001b[0m \u001b[36mthread_id\u001b[0m=\u001b[35m6917ab1b-b817-4b03-8cf3-55c07c29907f\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:13:03.130598Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs 200 1ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '1618', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:13:03.132698Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 3ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '6903', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:04.058981Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarting background run       \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:13:03.123343+00:00\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010637-6e85-673e-b81f-c527c07dce1a\u001b[0m \u001b[36mrun_queue_ms\u001b[0m=\u001b[35m935\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:13:04.058649+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:04.140552Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 36ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m36\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:13:04.141047Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 36ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m36\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:04.141790Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 1ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:05.623363Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.openai-proxy.org/anthropic/v1/messages \"HTTP/1.1 200 OK\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:13:06.912804Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 39ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m39\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:06.913458Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 39ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m39\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:13:06.914830Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 40ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m40\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:13:10.538726Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 50ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m50\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:13:10.539107Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 49ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m49\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:10.540061Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 51ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m51\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:12.416226Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.openai-proxy.org/anthropic/v1/messages \"HTTP/1.1 200 OK\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:13:19.253134Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mBackground run succeeded      \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:13:03.123343+00:00\u001b[0m \u001b[36mrun_ended_at\u001b[0m=\u001b[35m2025-04-03T08:13:19.253017+00:00\u001b[0m \u001b[36mrun_exec_ms\u001b[0m=\u001b[35m15194\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010637-6e85-673e-b81f-c527c07dce1a\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:13:04.058649+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:13:19.254876Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/stream 200 16132ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m16132\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/stream\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '681', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'location': '/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/1f010637-6e85-673e-b81f-c527c07dce1a/stream', 'cache-control': 'no-store', 'connection': 'keep-alive', 'x-accel-buffering': 'no', 'content-type': 'text/event-stream; charset=utf-8'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs/stream\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:19.325231Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 54ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m54\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:19.326593Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 55ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m55\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:13:19.326991Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 55ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m55\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:19.327599Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs 200 3ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:13:19.331672Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 5ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m5\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '97845', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:13:22.096349Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWorker stats                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mactive\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mavailable\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmax\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:22.598310Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mQueue stats                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mmax_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mmed_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mn_pending\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mn_running\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:13:33.203865Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mCreated run                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.ops\u001b[0m]\u001b[0m \u001b[36mafter_seconds\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36massistant_id\u001b[0m=\u001b[35mfe096781-5601-53d2-b2f6-0d3403f7e9ca\u001b[0m \u001b[36mif_not_exists\u001b[0m=\u001b[35mreject\u001b[0m \u001b[36mmultitask_strategy\u001b[0m=\u001b[35mrollback\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010638-8d64-62a8-b75d-a4a3ddaade0d\u001b[0m \u001b[36mstream_mode\u001b[0m=\u001b[35m['debug', 'messages']\u001b[0m \u001b[36mtemporary\u001b[0m=\u001b[35mFalse\u001b[0m \u001b[36mthread_id\u001b[0m=\u001b[35m6917ab1b-b817-4b03-8cf3-55c07c29907f\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:13:33.213545Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs 200 1ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '1621', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:33.217299Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 4ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '97845', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:34.125947Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarting background run       \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:13:33.203822+00:00\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010638-8d64-62a8-b75d-a4a3ddaade0d\u001b[0m \u001b[36mrun_queue_ms\u001b[0m=\u001b[35m921\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:13:34.125610+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:13:34.213792Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 33ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m33\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:13:34.217977Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 34ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m34\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:34.219122Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 36ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m36\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:36.228265Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.openai-proxy.org/anthropic/v1/messages \"HTTP/1.1 200 OK\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:13:37.632114Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 40ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m40\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:13:37.632699Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 41ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m41\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:37.633791Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 40ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m40\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:13:42.000283Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 37ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m37\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:13:42.000952Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 37ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m37\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:13:42.001145Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 36ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m36\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:45.186275Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.openai-proxy.org/anthropic/v1/messages \"HTTP/1.1 200 OK\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:13:52.245186Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mBackground run succeeded      \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:13:33.203822+00:00\u001b[0m \u001b[36mrun_ended_at\u001b[0m=\u001b[35m2025-04-03T08:13:52.245027+00:00\u001b[0m \u001b[36mrun_exec_ms\u001b[0m=\u001b[35m18119\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f010638-8d64-62a8-b75d-a4a3ddaade0d\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:13:34.125610+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:52.246604Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/stream 200 19043ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m19043\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/stream\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '684', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'location': '/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/1f010638-8d64-62a8-b75d-a4a3ddaade0d/stream', 'cache-control': 'no-store', 'connection': 'keep-alive', 'x-accel-buffering': 'no', 'content-type': 'text/event-stream; charset=utf-8'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs/stream\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:52.321601Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 61ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m61\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:13:52.322495Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 62ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m62\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:13:52.322864Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 62ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m62\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:13:52.324575Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs 200 2ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m2\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:13:52.328939Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 6ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m6\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '305592', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:14:01.521389Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 48ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m48\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:14:01.522032Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 49ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m49\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:01.522267Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 48ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m48\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:14:04.975612Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 51ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m51\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:14:04.975921Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 51ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m51\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:04.976515Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 51ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m51\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:14:07.573351Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 49ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m49\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:14:07.573958Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 49ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m49\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:14:07.574332Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 49ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m49\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:10.574454Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 50ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m50\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:10.574957Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 50ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m50\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:14:10.575420Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 50ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m50\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:14:12.860795Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 57ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m57\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:14:12.861133Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 57ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m57\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:14:12.861623Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 57ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m57\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:15.332689Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 45ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m45\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:15.333020Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 44ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m44\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:14:15.333153Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 44ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m44\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:14:22.230963Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWorker stats                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mactive\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mavailable\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmax\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:14:22.732622Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mQueue stats                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mmax_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mmed_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mn_pending\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mn_running\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:22.732913Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mSweeped runs                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_ids\u001b[0m=\u001b[35m[]\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:14:25.810497Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 37ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m37\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:25.811351Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 38ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m38\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:14:25.811449Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 37ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m37\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:14:31.403911Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 51ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m51\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:14:31.404232Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 51ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m51\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:14:31.404984Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 52ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m52\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:39.438454Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/state/checkpoint 200 2ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m2\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/state/checkpoint\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '141', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '47882', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/state/checkpoint\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:14:53.241492Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 50ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m50\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:14:53.242149Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 50ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m50\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:53.242451Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 51ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m51\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:14:57.730130Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/state 200 3ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/state\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '176', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '338', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/state\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:14:57.744288Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 5ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m5\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '329806', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:14:57.761109Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mCreated run                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.ops\u001b[0m]\u001b[0m \u001b[36mafter_seconds\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36massistant_id\u001b[0m=\u001b[35mfe096781-5601-53d2-b2f6-0d3403f7e9ca\u001b[0m \u001b[36mif_not_exists\u001b[0m=\u001b[35mreject\u001b[0m \u001b[36mmultitask_strategy\u001b[0m=\u001b[35mrollback\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f01063b-b3cb-6038-8c87-9a17792898bc\u001b[0m \u001b[36mstream_mode\u001b[0m=\u001b[35m['debug', 'messages']\u001b[0m \u001b[36mtemporary\u001b[0m=\u001b[35mFalse\u001b[0m \u001b[36mthread_id\u001b[0m=\u001b[35m6917ab1b-b817-4b03-8cf3-55c07c29907f\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:14:57.767752Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs 200 0ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '1570', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:14:57.775975Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 8ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m8\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '329806', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:14:57.820134Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 45ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m45\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:14:57.822303Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 4ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:14:57.823256Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 5ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m5\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:14:58.318815Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mStarting background run       \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:14:57.761080+00:00\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f01063b-b3cb-6038-8c87-9a17792898bc\u001b[0m \u001b[36mrun_queue_ms\u001b[0m=\u001b[35m557\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:14:58.318695+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:15:04.572527Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.openai-proxy.org/anthropic/v1/messages \"HTTP/1.1 200 OK\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:15:11.003076Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mBackground run succeeded      \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.worker\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_attempt\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mrun_created_at\u001b[0m=\u001b[35m2025-04-03T08:14:57.761080+00:00\u001b[0m \u001b[36mrun_ended_at\u001b[0m=\u001b[35m2025-04-03T08:15:11.002938+00:00\u001b[0m \u001b[36mrun_exec_ms\u001b[0m=\u001b[35m12684\u001b[0m \u001b[36mrun_id\u001b[0m=\u001b[35m1f01063b-b3cb-6038-8c87-9a17792898bc\u001b[0m \u001b[36mrun_started_at\u001b[0m=\u001b[35m2025-04-03T08:14:58.318695+00:00\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:15:11.004952Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/stream 200 13244ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m13244\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/stream\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '635', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'location': '/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs/1f01063b-b3cb-6038-8c87-9a17792898bc/stream', 'cache-control': 'no-store', 'connection': 'keep-alive', 'x-accel-buffering': 'no', 'content-type': 'text/event-stream; charset=utf-8'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs/stream\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:15:11.061299Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 42ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m42\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:15:11.062328Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 43ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m43\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:15:11.062684Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 43ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m43\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:15:11.063953Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs 200 1ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:15:11.069272Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 6ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m6\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '467382', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:15:19.697984Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/versions 200 49ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m49\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/versions\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '23', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '198', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/versions\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:15:19.698394Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 47ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m47\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:15:19.699288Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /assistants/search 200 1ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '25', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '265', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:15:19.733123Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /info 200 4ms             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/info\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '60', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:15:22.372577Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWorker stats                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mactive\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mavailable\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmax\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:15:22.844301Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mHTTP Request: POST https://api.smith.langchain.com/v1/metadata/submit \"HTTP/1.1 204 No Content\"\u001b[0m [\u001b[0m\u001b[1m\u001b[34mhttpx\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:15:22.874062Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mQueue stats                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mmax_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mmed_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mn_pending\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mn_running\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:15:38.950418Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/search 200 1ms  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '39', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '70432', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:15:45.550625Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/search 200 1ms  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '39', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '70432', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:16:22.508780Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWorker stats                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mactive\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mavailable\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmax\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:16:23.010839Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mQueue stats                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mmax_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mmed_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mn_pending\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mn_running\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:16:57.095979Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 38ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m38\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:16:57.096957Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 38ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m38\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:16:57.097220Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 38ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m38\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:16:57.099395Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /assistants/search 200 43ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m43\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '25', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '265', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:16:57.099815Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/search 200 41ms \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m41\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '39', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '70432', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:16:57.100502Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs 200 2ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m2\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/runs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'limit=1000&offset=0&status=pending'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/runs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:16:57.103163Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history 200 44ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m44\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/6917ab1b-b817-4b03-8cf3-55c07c29907f/history\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'thread_id': '6917ab1b-b817-4b03-8cf3-55c07c29907f'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '14', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '467382', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/{thread_id}/history\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:17:22.649921Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWorker stats                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mactive\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mavailable\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmax\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:17:23.151908Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mQueue stats                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mmax_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mmed_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mn_pending\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mn_running\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:18:20.825202Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /info 200 2ms             \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m2\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/info\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '60', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:18:20.827258Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /assistants/search 200 3ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m3\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '25', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '265', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:18:20.903160Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas 200 47ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m47\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '56844', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/schemas\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:18:20.907999Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mPOST /threads/search 200 52ms \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m52\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mPOST\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'content-length': '39', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'content-type': 'application/json', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '70432', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/threads/search\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:18:20.909318Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs 200 4ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/subgraphs\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'recurse=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '2', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/subgraphs\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:18:20.909989Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mGET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph 200 4ms\u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_api.server\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mlatency_ms\u001b[0m=\u001b[35m4\u001b[0m \u001b[36mmethod\u001b[0m=\u001b[35mGET\u001b[0m \u001b[36mpath\u001b[0m=\u001b[35m/assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/graph\u001b[0m \u001b[36mpath_params\u001b[0m=\u001b[35m{'assistant_id': 'fe096781-5601-53d2-b2f6-0d3403f7e9ca'}\u001b[0m \u001b[36mproto\u001b[0m=\u001b[35m1.1\u001b[0m \u001b[36mquery_string\u001b[0m=\u001b[35m'xray=true'\u001b[0m \u001b[36mreq_header\u001b[0m=\u001b[35m{'host': '127.0.0.1:2024', 'connection': 'keep-alive', 'x-auth-scheme': 'langsmith', 'sec-ch-ua-platform': '\"macOS\"', 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'sec-ch-ua': '\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"', 'sec-ch-ua-mobile': '?0', 'accept': '*/*', 'origin': 'https://smith.langchain.com', 'sec-fetch-site': 'cross-site', 'sec-fetch-mode': 'cors', 'sec-fetch-dest': 'empty', 'accept-encoding': 'gzip, deflate, br, zstd', 'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8'}\u001b[0m \u001b[36mres_header\u001b[0m=\u001b[35m{'content-length': '567', 'content-type': 'application/json'}\u001b[0m \u001b[36mroute\u001b[0m=\u001b[35m/assistants/{assistant_id}/graph\u001b[0m \u001b[36mstatus\u001b[0m=\u001b[35m200\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n", "\u001b[2m2025-04-03T08:18:22.779049Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWorker stats                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mactive\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mavailable\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmax\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_0\u001b[0m\n", "\u001b[2m2025-04-03T08:18:23.280700Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mQueue stats                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mmax_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mmed_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mn_pending\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mn_running\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_3\u001b[0m\n", "\u001b[2m2025-04-03T08:18:23.280901Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mSweeped runs                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mrun_ids\u001b[0m=\u001b[35m[]\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35mMainThread\u001b[0m\n", "\u001b[2m2025-04-03T08:19:22.916109Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mWorker stats                  \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mactive\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mavailable\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mmax\u001b[0m=\u001b[35m1\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_2\u001b[0m\n", "\u001b[2m2025-04-03T08:19:23.418103Z\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mQueue stats                   \u001b[0m [\u001b[0m\u001b[1m\u001b[34mlanggraph_storage.queue\u001b[0m]\u001b[0m \u001b[36mapi_variant\u001b[0m=\u001b[35mlocal_dev\u001b[0m \u001b[36mmax_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mmed_age_secs\u001b[0m=\u001b[35mNone\u001b[0m \u001b[36mn_pending\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mn_running\u001b[0m=\u001b[35m0\u001b[0m \u001b[36mthread_name\u001b[0m=\u001b[35masyncio_1\u001b[0m\n"]}], "source": ["!  cd react-agent-python && langgraph dev"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用SDK交互\n", "*****"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["! pip install langgraph-sdk"]}, {"cell_type": "markdown", "metadata": {}, "source": ["发送消息给langgraph服务器"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langgraph_sdk import get_client\n", "\n", "client = get_client(url=\"http://localhost:2024\")\n", "\n", "async for chunk in client.runs.stream(\n", "    None,  # Threadless run\n", "    \"agent\", # Name of assistant. Defined in langgraph.json.\n", "    input={\n", "        \"messages\": [{\n", "            \"role\": \"human\",\n", "            \"content\": \"What is <PERSON><PERSON><PERSON><PERSON>?\",\n", "        }],\n", "    },\n", "    stream_mode=\"updates\",\n", "):\n", "    print(f\"Receiving new event of type: {chunk.event}...\")\n", "    print(chunk.data)\n", "    print(\"\\n\\n\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 2}