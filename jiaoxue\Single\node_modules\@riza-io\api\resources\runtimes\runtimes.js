"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuntimesRuntimesPagination = exports.Runtimes = void 0;
const resource_1 = require("../../resource.js");
const core_1 = require("../../core.js");
const RevisionsAPI = __importStar(require("./revisions.js"));
const revisions_1 = require("./revisions.js");
const pagination_1 = require("../../pagination.js");
class Runtimes extends resource_1.APIResource {
    constructor() {
        super(...arguments);
        this.revisions = new RevisionsAPI.Revisions(this._client);
    }
    /**
     * Creates a runtime.
     */
    create(body, options) {
        return this._client.post('/v1/runtimes', { body, ...options });
    }
    list(query = {}, options) {
        if ((0, core_1.isRequestOptions)(query)) {
            return this.list({}, query);
        }
        return this._client.getAPIList('/v1/runtimes', RuntimesRuntimesPagination, { query, ...options });
    }
    /**
     * Deletes a runtime.
     */
    delete(id, options) {
        return this._client.delete(`/v1/runtimes/${id}`, options);
    }
    /**
     * Retrieves a runtime.
     */
    get(id, options) {
        return this._client.get(`/v1/runtimes/${id}`, options);
    }
}
exports.Runtimes = Runtimes;
class RuntimesRuntimesPagination extends pagination_1.RuntimesPagination {
}
exports.RuntimesRuntimesPagination = RuntimesRuntimesPagination;
Runtimes.RuntimesRuntimesPagination = RuntimesRuntimesPagination;
Runtimes.Revisions = revisions_1.Revisions;
//# sourceMappingURL=runtimes.js.map