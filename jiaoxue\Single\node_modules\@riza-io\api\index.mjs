// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
var _Riza_instances, _a, _Riza_baseURLOverridden;
import * as Core from "./core.mjs";
import * as Errors from "./error.mjs";
import * as Pagination from "./pagination.mjs";
import * as Uploads from "./uploads.mjs";
import * as API from "./resources/index.mjs";
import { Command, } from "./resources/command.mjs";
import { Executions, ExecutionsDefaultPagination, } from "./resources/executions.mjs";
import { Secrets, SecretsSecretsPagination, } from "./resources/secrets.mjs";
import { Tools, ToolsToolsPagination, } from "./resources/tools.mjs";
import { Runtimes, RuntimesRuntimesPagination, } from "./resources/runtimes/runtimes.mjs";
/**
 * API Client for interfacing with the Riza API.
 */
export class Riza extends Core.APIClient {
    /**
     * API Client for interfacing with the Riza API.
     *
     * @param {string | undefined} [opts.apiKey=process.env['RIZA_API_KEY'] ?? undefined]
     * @param {string} [opts.baseURL=process.env['RIZA_BASE_URL'] ?? https://api.riza.io] - Override the default base URL for the API.
     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.
     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.
     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.
     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.
     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.
     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.
     */
    constructor({ baseURL = Core.readEnv('RIZA_BASE_URL'), apiKey = Core.readEnv('RIZA_API_KEY'), ...opts } = {}) {
        if (apiKey === undefined) {
            throw new Errors.RizaError("The RIZA_API_KEY environment variable is missing or empty; either provide it, or instantiate the Riza client with an apiKey option, like new Riza({ apiKey: 'My API Key' }).");
        }
        const options = {
            apiKey,
            ...opts,
            baseURL: baseURL || `https://api.riza.io`,
        };
        super({
            baseURL: options.baseURL,
            baseURLOverridden: baseURL ? baseURL !== 'https://api.riza.io' : false,
            timeout: options.timeout ?? 60000 /* 1 minute */,
            httpAgent: options.httpAgent,
            maxRetries: options.maxRetries,
            fetch: options.fetch,
        });
        _Riza_instances.add(this);
        this.secrets = new API.Secrets(this);
        this.tools = new API.Tools(this);
        this.command = new API.Command(this);
        this.runtimes = new API.Runtimes(this);
        this.executions = new API.Executions(this);
        this._options = options;
        this.apiKey = apiKey;
    }
    defaultQuery() {
        return this._options.defaultQuery;
    }
    defaultHeaders(opts) {
        return {
            ...super.defaultHeaders(opts),
            ...this._options.defaultHeaders,
        };
    }
    authHeaders(opts) {
        return { Authorization: `Bearer ${this.apiKey}` };
    }
}
_a = Riza, _Riza_instances = new WeakSet(), _Riza_baseURLOverridden = function _Riza_baseURLOverridden() {
    return this.baseURL !== 'https://api.riza.io';
};
Riza.Riza = _a;
Riza.DEFAULT_TIMEOUT = 60000; // 1 minute
Riza.RizaError = Errors.RizaError;
Riza.APIError = Errors.APIError;
Riza.APIConnectionError = Errors.APIConnectionError;
Riza.APIConnectionTimeoutError = Errors.APIConnectionTimeoutError;
Riza.APIUserAbortError = Errors.APIUserAbortError;
Riza.NotFoundError = Errors.NotFoundError;
Riza.ConflictError = Errors.ConflictError;
Riza.RateLimitError = Errors.RateLimitError;
Riza.BadRequestError = Errors.BadRequestError;
Riza.AuthenticationError = Errors.AuthenticationError;
Riza.InternalServerError = Errors.InternalServerError;
Riza.PermissionDeniedError = Errors.PermissionDeniedError;
Riza.UnprocessableEntityError = Errors.UnprocessableEntityError;
Riza.toFile = Uploads.toFile;
Riza.fileFromPath = Uploads.fileFromPath;
Riza.Secrets = Secrets;
Riza.SecretsSecretsPagination = SecretsSecretsPagination;
Riza.Tools = Tools;
Riza.ToolsToolsPagination = ToolsToolsPagination;
Riza.Command = Command;
Riza.Runtimes = Runtimes;
Riza.RuntimesRuntimesPagination = RuntimesRuntimesPagination;
Riza.Executions = Executions;
Riza.ExecutionsDefaultPagination = ExecutionsDefaultPagination;
export { toFile, fileFromPath } from "./uploads.mjs";
export { RizaError, APIError, APIConnectionError, APIConnectionTimeoutError, APIUserAbortError, NotFoundError, ConflictError, RateLimitError, BadRequestError, AuthenticationError, InternalServerError, PermissionDeniedError, UnprocessableEntityError, } from "./error.mjs";
export default Riza;
//# sourceMappingURL=index.mjs.map