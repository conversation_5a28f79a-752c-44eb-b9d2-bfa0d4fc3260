名称,内容,章节部位,知识域,过程
专家判断,"是指基于某应用领域、知识领域、学科和行业等的专业知识而做出的，关于当前活动的
合理判断，这些专业知识可来自具有专业学历、知识、技能、经验或培训经历的任何小组或个人。
◆组织战略；
◆ 效益管理；
◆ 关于项目所在的行业以及项目关注的领域的技术知识；
◆ 持续时间和预算的估算；
◆ 风险识别。",4.1.1.1 ,项目整合管理,制定项目章程
头脑风暴," 本技术用于在短时间内获得大量创意，适用于团队环境，需要引导者进行引导。
头脑风暴由两个部分构成：创意产生和创意分析。制定项目章程时可通过头脑风暴向相关方、主题专家和团队成员收集数据、解决方案或创意。",4.1.1.1 ,项目整合管理,制定项目章程
★引导, 引导是指有效引导团队活动成功以达成决定、解决方案或结论的能力。引导者确保参与者有效参与，互相理解，考虑所有意见，按既定决策流程全力支持得到的结论或结果，以及所达成的行动计划和协议在之后得到合理执行。,4.1.1.1 ,项目整合管理,制定项目章程
会议,"在本过程中，可以通过会议讨论项目方法，确定为达成项目目标而采用的工作执行方式，以及制
定项目监控方式。
项目开工会议通常意味着规划阶段结束和执行阶段开始，旨在传达项目目标、获得团队对项
目的承诺，以及阐明每个相关方的角色和职责。开工会议可能在不同时间点举行，具体取决于项
目的特征：
◆ 对于小型项目，通常由同一个团队开展项目规划和执行。这种情况下，项目在启动之后很快就
会开工（规划过程组），因为执行团队参与了规划。
◆ 对于大型项目，通常由项目管理团队开展大部分规划工作。在初始规划工作完成、开发（执
行）阶段开始时，项目团队其他成员才参与进来。这种情况下，将随同执行过程组的相关过程
召开开工会议。
对于多阶段项目，通常在每个阶段开始时都要 举行一次开工会议。",4.2.2.4,项目整合管理,制定项目管理计划
项目管理信息系统 (PMIS),"PMIS 提供信息技术 (IT) 软件工具，例如进度计划软件工具、工作授权系统、配置管理系统、信息收
集与发布系统，以及进入其他在线自动化系统（如公司知识库）的界面。自动收集和报告关键绩效
指标（KPI）可以是本系统的一项功能。",4.3.2.2 ,项目整合管理,指导与管理项目工作
偏差分析,"偏差分析审查目标绩效与实际绩效之间的差异（或偏差），可涉及持续时间估算、成本估算、资源使用、资源费率、技术绩效和其他测量指标。
可以在每个知识领域，针对特定变量，开展偏差分析。在监控项目工作过程中，通过偏差分析对成本、时间、技术和资源偏差进行综合分析，以了解项目的总体偏差情况。这样就便于采取合适的预防或纠正措施",4.5.2.2,项目整合管理,监控项目工作- 数据分析
 变更控制工具,"为了便于开展配置和变更管理，可以使用一些手动或自动化的工具。配置控制重点关注可交付成果及各个过程的技术规范，而变更控制则着眼于识别、记录、批准或否决对项目文件、可交付成果或基准的变更。
工具的选择应基于项目相关方的需要，包括考虑组织和环境情况和（或）制约因素。工具应支持以下配置管理活动：
◆ 识别配置项。 识别与选择配置项，从而为定义与核实产品配置、标记产品和文件、管理变更和
明确责任提供基础。
◆ 记录并报告配置项状态。 关于各个配置项的信息记录和报告。
◆ 进行配置项核实与审计。 通过配置核实与审计，确保项目的配置项组成的正确性，以及相应的变更都被登记、评估、批准、跟踪和正确实施，从而确保配置文件所规定的功能要求都已实现。
工具还应支持以下变更管理活动：
◆ 识别变更。 识别并选择过程或项目文件的变更项。
◆ 记录变更。 将变更记录为合适的变更请求。
◆ 做出变更决定。 审查变更，批准、否决、推迟对项目文件、可交付成果或基准的变更或做出
其他决定。
◆ 跟踪变更。 确认变更被登记、评估、批准、跟踪并向相关方传达最终结果。
也可以使用工具来管理变更请求和后续的决策，同时还要格外关注沟通，以帮助变更控制委员会的成员履行职责，以及向相关方传达决定。",4.6.2.2 ,项目整合管理,实施整体变更控制
访谈, 访谈是通过与相关方直接交谈，来获取信息的正式或非正式的方法。访谈的典型做法是向被访者提出预设和即兴的问题，并记录他们的回答。访谈经常是一个访谈者和一个被访者之间的“一对一”谈话，但也可以包括多个访谈者和/或多个被访者。访谈有经验的项目参与者、发起人和其他高管，以及主题专家，有助于识别和定义所需产品可交付成果的特征和功能。访谈也可用于获取机密信息,5.1.3.2 ,项目范围管理,收集需求
焦点小组, 焦点小组是召集预定的相关方和主题专家，了解他们对所讨论的产品、服务或成果的期望和态度。由一位受过训练的主持人引导大家进行互动式讨论。焦点小组往往比“一对一”的访谈更热烈。,5.1.3.2 ,项目范围管理,收集需求
问卷调查,问卷调查是指设计一系列书面问题，向众多受访者快速收集信息。问卷调查方法非常适用于以下情况：受众多样化，需要快速完成调查，受访者地理位置分散，并且适合开展统计分析。,5.2.2.2,项目范围管理,收集需求
决策,"
适用于收集需求过程的决策技术包括（但不限于）：
◆ 投票。 投票是一种为达成某种期望结果，而对多个未来行动方案进行评估的集体决策技术和过
程。本技术用于生成、归类和排序产品需求。投票技术示例包括：
◆一致同意。 每个人都同意某个行动方案。
◆大多数同意。 获得群体中超过 50% 人员的支持，就能做出决策。把参与决策的小组人数定
为奇数，可防止因平局而无法达成决策。
◆相对多数同意。 根据群体中相对多数人的意见做出决策，即便未能获得大多数人的支持。
通常在候选项超过两个时使用。
◆ 独裁型决策制定。 采用这种方法，将由一个人负责为整个集体制定决策。
◆ 多标准决策分析。 该技术借助决策矩阵，用系统分析方法建立诸如风险水平、不确定性和价
值收益等多种标准，以对众多创意进行评估和排序。",5.2.2.4 ,项目范围管理,收集需求
名义小组技术,"名义小组技术是用于促进头脑风暴的一种技术，通过投票排列最有用的创意，
以便进一步开展头脑风暴或优先排序。名义小组技术是一种结构化的头脑风暴形式，由四
个步骤组成：
◆向集体提出一个问题或难题。每个人在沉思后写出自己的想法。
◆主持人在活动挂图上记录所有人的想法。
◆集体讨论各个想法，直到全体成员达成一个明确的共识。
◆个人私下投票决出各种想法的优先排序，通常采用 5 分制， 1 分最低， 5 分最高。为减少想法
数量、集中关注想法，可进行数轮投票。每轮投票后，都将清点选票，得分最高者被选出。",5.2.2.6,项目范围管理,收集需求
观察和交谈," 观察和交谈是指直接察看个人在各自的环境中如何执行工作（或任务）和实施
流程。当产品使用者难以或不愿清晰说明他们的需求时，就特别需要通过观察来了解他们的
工作细节。观察，也称为“工作跟随”，通常由旁站观察者观察业务专家如何执行工作，但
也可以由“参与观察者”来观察，通过实际执行一个流程或程序，来体验该流程或程序是如
何实施的，以便挖掘隐藏的需求。",5.2.2.6,项目范围管理,收集需求
产品分析,"产品分析可用于定义产品和服务，包括针对产品或服务提问并回答，以描述要交付的产品的用
途、特征及其他方面。
每个应用领域都有一种或几种普遍公认的方法，用以把高层级的产品或服务描述转变为有意义的
可交付成果。首先获取高层级的需求，然后将其细化到最终产品设计所需的详细程度。产品分析技
术包括（但不限于）：
◆ 产品分解；
◆ 需求分析；
◆ 系统分析；
◆ 系统工程；
◆ 价值分析；
◆ 价值工程。",5.3.2.5 ,项目范围管理,定义范围
分解,"分解是一种把项目范围和项目可交付成果逐步划分为更小、更便于管理的组成部分的技术；工作
包是 WBS 最低层的工作，可对其成本和持续时间进行估算和管理。分解的程度取决于所需的控制程
度，以实现对项目的高效管理；工作包的详细程度则因项目规模和复杂程度而异。要把整个项目工
作分解为工作包，通常需要开展以下活动：
◆ 识别和分析可交付成果及相关工作；
◆ 确定 WBS 的结构和编排方法；
◆ 自上而下逐层细化分解；
◆ 为 WBS 组成部分制定和分配标识编码；
◆ 核实可交付成果分解的程度是否恰当。
对 WBS 较高层组件进行分解，就是要把每个可交付成果或组件分解为最基本的组成部分，即可核
实的产品、服务或成果。如果采用敏捷方法，可以将长篇故事分解成用户故事。 WBS 可以采用提纲
式、组织结构图或能说明层级结构的其他形式。通过确认 WBS 较低层组件是完成上层相应可交付成
果的必要且充分的工作，来核实分解的正确性。不同的可交付成果可以分解到不同的层次。某些可
交付成果只需分解到下一层，即可到达工作包的层次，而另一些则须分解更多层。工作分解得越细
致，对工作的规划、管理和控制就越有力。但是，过细的分解会造成管理努力的无效耗费、资源使
用效率低下、工作实施效率降低，同时造成 WBS 各层级的数据汇总困难。
要在未来远期才完成的可交付成果或组件，当前可能无法分解。项目管理团队因而通常需要等
待对该可交付成果或组成部分达成一致意见，才能够制定出 WBS 中的相应细节。这种技术有时称做
滚动式规划。",5.4.2.2 ,项目范围管理,创建WBS
★滚动式规划,"是一种迭代式的规划技术，即详细规划近期要完成的工作，同时在较高层级上粗略规
划远期工作。它是一种渐进明细的规划方式，适用于工作包、规划包以及采用敏捷或瀑布式方法的
发布规划。因此，在项目生命周期的不同阶段，工作的详细程度会有所不同。在早期的战略规划阶
段，信息尚不够明确，工作包只能分解到已知的详细水平；而后，随着了解到更多的信息，近期即
将实施的工作包就可以分解到具体的活动。",6.2.2.3,项目进度管理,定义活动
紧前关系绘图法,"紧前关系绘图法（PDM）是创建进度模型的一种技术，用节点表示活动，用一种或多种逻辑关系连
接活动，以显示活动的实施顺序。
PDM 包括四种依赖关系或逻辑关系。紧前活动是在进度计划的逻辑路径中，排在非开始活动前面
的活动。紧后活动是在进度计划的逻辑路径中，排在某个活动后面的活动。
u 完成到开始（FS）。 只有紧前活动完成，紧后活动才能开始的逻辑关系。例如，只有完成装配
PC 硬件（紧前活动），才能开始在 PC 上安装操作系统（紧后活动）。
◆ 完成到完成（FF）。 只有紧前活动完成，紧后活动才能完成的逻辑关系。例如，只有完成文件
的编写（紧前活动），才能完成文件的编辑（紧后活动）。
◆ 开始到开始（SS）。 只有紧前活动开始，紧后活动才能开始的逻辑关系。例如，开始地基浇灌
（紧后活动）之后，才能开始混凝土的找平（紧前活动）。
◆ 开始到完成（SF）。 只有紧前活动开始，紧后活动才能完成的逻辑关系。例如，只有启动新的
应付账款系统（紧前活动），才能关闭旧的应付账款系统（紧后活动）。
在 PDM 图中， FS 是最常用的逻辑关系类型； SF 关系则很少使用，为了保持 PDM 四种逻辑关系类型
的完整性，这里也将 SF 列出。
虽然两个活动之间可能同时存在两种逻辑关系（例如 SS 和 FF），但不建议相同的活动之间存在多
种关系。因此必须做出选出影响最大关系的决定。此外也不建议采用闭环的逻辑关系",6.3.2.1,项目进度管理,排列活动顺序
确定和整合依赖关系,"依赖关系可能是强制或选择的，内部或外部的。这四种依赖关系可以组合成强制性外部依赖关系、强制性内部依赖关系、选择性外部依赖关系或选择性内部依赖关系。
◆ 强制性依赖关系。 强制性依赖关系是法律或合同要求的或工作的内在性质决定的依赖关系，强制性依赖关系往往与客观限制有关。例如，在建筑项目中，只有在地基建成后，才能建立地面结构；在电子项目中，必须先把原型制造出来，然后才能对其进行测试。强制性依赖关系又称硬逻辑关系或硬依赖关系，技术依赖关系可能不是强制性的。在活动排序过程中，项目团队应明确哪些关系是强制性依赖关系，不应把强制性依赖关系和进度计划编制工具中的进度制约因素相混淆。
◆ 选择性依赖关系。 选择性依赖关系有时又称首选逻辑关系、优先逻辑关系或软逻辑关系。即便还有其他依赖关系可用，选择性依赖关系应基于具体应用领域的最佳实践或项目的某些特殊性质对活动顺序的要求来创建。例如，根据普遍公认的最佳实践，在建造期间，应先完成卫生管道工程，才能开始电气工程。这个顺序并不是强制性要求，两个工程可以同时（并行）开展工作，但如按先后顺序进行可以降低整体项目风险。应该对选择性依赖关系进行全面记录，因为它们会影响总浮动时间，并限制后续的进度安排。如果打算进行快速跟进，则应当审查相应的选择性依赖关系，并考虑是否需要调整或去除。在排列活动顺序过程中，项目团队应明确哪些依赖关系属于选择性依赖关系
◆外部依赖关系。 外部依赖关系是项目活动与非项目活动之间的依赖关系，这些依赖关系往往不在项目团队的控制范围内。例如，软件项目的测试活动取决于外部硬件的到货；建筑项目的现场准备，可能要在政府的环境听证会之后才能开始。在排列活动顺序过程中，项目管理团队应明确哪些依赖关系属于外部依赖关系。
◆ 内部依赖关系。 内部依赖关系是项目活动之间的紧前关系，通常在项目团队的控制之中。例如，只有机器组装完毕，团队才能对其测试，这是一个内部的强制性依赖关系。在排列活动顺序过程中，项目管理团队应明确哪些依赖关系属于内部依赖关系。",6.3.2.2 ,项目进度管理,排列活动顺序
提前量和滞后量,"提前量是相对于紧前活动，紧后活动可以提前的时间量。例如，在新办公大楼建设项目中，绿化
施工可以在尾工清单编制完成前 2 周开始，这就是带 2 周提前量的完成到开始的关系，如图 6-10 所
示。在进度计划软件中，提前量往往表示为负滞后量。
滞后量是相对于紧前活动，紧后活动需要推迟的时间量。例如，对于一个大型技术文档，编写小
组可以在编写工作开始后 15 天，开始编辑文档草案，这就是带 15 天滞后量的开始到开始关系，如
图 6-10 所示。在图 6-11 的项目进度网络图中，活动 H 和活动 I 之间就有滞后量，表示为 SS+10（带
10 天滞后量的开始到开始关系），虽然图中并没有用精确的时间刻度来表示滞后的量值。
项目管理团队应该明确哪些依赖关系中需要加入提前量或滞后量，以便准确地表示活动之间的逻
辑关系。提前量和滞后量的使用不能替代进度逻辑关系，而且持续时间估算中不包括任何提前量或
滞后量，同时还应该记录各种活动及与之相关的假设条件。",6.3.2.3 ,项目进度管理,排列活动顺序
★类比估算,"类比估算是一种使用相似活动或项目的历史数据，来估算当前活动或项目的持续时间或成本的技
术。类比估算以过去类似项目的参数值（如持续时间、预算、规模、重量和复杂性等）为基础，来
估算未来项目的同类参数或指标。在估算持续时间时，类比估算技术以过去类似项目的实际持续时
间为依据，来估算当前项目的持续时间。这是一种粗略的估算方法，有时需要根据项目复杂性方面
的已知差异进行调整，在项目详细信息不足时，就经常使用类比估算来估算项目持续时间。
相对于其他估算技术，类比估算通常成本较低、耗时较少，但准确性也较低。类比估算可以针对
整个项目或项目中的某个部分进行，或可以与其他估算方法联合使用。如果以往活动是本质上而不
是表面上类似，并且从事估算的项目团队成员具备必要的专业知识，那么类比估算就最为可靠。",6.4.2.2 ,项目进度管理,估算活动持续时间
★参数估算,"参数估算是一种基于历史数据和项目参数，使用某种算法来计算成本或持续时间的估算技术。
它是指利用历史数据之间的统计关系和其他变量（如建筑施工中的平方英尺），来估算诸如成本、预算和持续时间等活动参数。
把需要实施的工作量乘以完成单位工作量所需的工时，即可计算出持续时间。例如，对于设计项
目，将图纸的张数乘以每张图纸所需的工时；或者对于电缆铺设项目，将电缆的长度乘以铺设每米
电缆所需的工时。如果所用的资源每小时能够铺设 25 米电缆，那么铺设 1000 米电缆的持续时间是
40 小时（1000 米除以 25 米/小时）。
参数估算的准确性取决于参数模型的成熟度和基础数据的可靠性。且参数进度估算可以针对整个
项目或项目中的某个部分，并可以与其他估算方法联合使用。",6.4.2.3 ,项目进度管理,估算活动持续时间
★三点估算,"通过考虑估算中的不确定性和风险，可以提高持续时间估算的准确性。使用三点估算有助于界定
活动持续时间的近似区间：
◆ 最可能时间 (tM)。 基于最可能获得的资源、最可能取得的资源生产率、对资源可用时间的现实
预计、资源对其他参与者的可能依赖关系及可能发生的各种干扰等，所估算的活动持续时间。
◆ 最乐观时间 (tO)。 基于活动的最好情况所估算的活动持续时间。
◆ 最悲观时间 (tP)。 基于活动的最差情况所估算的持续时间。
基于持续时间在三种估算值区间内的假定分布情况，可计算期望持续时间 tE。一个常用公式为三角分布：
tE = (tO + tM + tP) / 3.
历史数据不充分或使用判断数据时，使用三角分布，基于三点的假定分布估算出期望持续时间，
并说明期望持续时间的不确定区间。",6.4.2.4 ,项目进度管理,估算活动持续时间
自下而上估算,"自下而上估算是一种估算项目持续时间或成本的方法，通过从下到上逐层汇总 WBS 组成部分的估
算而得到项目估算。如果无法以合理的可信度对活动持续时间进行估算，则应将活动中的工作进一
步细化，然后估算具体的持续时间，接着再汇总这些资源需求估算，得到每个活动的持续时间。活
动之间可能存在或不存在会影响资源利用的依赖关系；如果存在，就应该对相应的资源使用方式加
以说明，并记录在活动资源需求中",6.4.2.5 ,项目进度管理,估算活动持续时间
★储备分析, 储备分析用于确定项目所需的应急储备量和管理储备。在进行持续时间估算时，需考虑应急储备（有时称为“进度储备”），以应对进度方面的不确定性。应急储备是包含在进度基准中的一段持续时间，用来应对已经接受的已识别风险。应急储备与“已知 — 未知”风险相关，需要加以合理估算，用于完成未知的工作量。应急储备可取活动持续时间估算值的某一百分比或某一固定的时间段，亦可把应急储备从各个活动中剥离出来并汇总。随着项目信息越来越明确，可以动用、减少或取消应急储备，应该在项目进度文件中清楚地列出应急储备。也可以估算项目进度管理所需要的管理储备量。管理储备是为管理控制的目的而特别留出的项目预算，用来应对项目范围中不可预见的工作。管理储备用来应对会影响项目的“未知-未知”风险，它不包括在进度基准中，但属于项目总持续时间的一部分。依据合同条款，使用管理储备可能需要变更进度基准。,6.4.2.6,项目进度管理,估算活动持续时间-数据分析
 进度网络分析,"进度网络分析是创建项目进度模型的一种综合技术，它采用了其他几种技术，例如关键路径法（见
6.5.2.2 节）、资源优化技术（见 6.5.2.3 节）和建模技术（见 6.5.2.4 节）。其他分析包括（但不于）：
◆ 当多个路径在同一时间点汇聚或分叉时，评估汇总进度储备的必要性，以减少出现进度落后的可能性。
◆ 审查网络，看看关键路径是否存在高风险活动或具有较多提前量的活动，是否需要使用进度储
备或执行风险应对计划来降低关键路径的风险。进度网络分析是一个反复进行的过程，一直持续到创建出可行的进度模型。",6.5.2.1,项目进度管理,制定进度计划
★关键路径法,"关键路径法用于在进度模型中估算项目最短工期，确定逻辑网络路径的进度灵活性大小。这种进
度网络分析技术在不考虑任何资源限制的情况下，沿进度网络路径使用顺推与逆推法，计算出所有
活动的最早开始、最早结束、最晚开始和最晚法完成日期，如图 6-16 所示。
在任一网络路径上，进度活动可以从最早开始日期推迟或拖延的时间，而不至于延误项目完成
日期或违反进度制约因素，就是总浮动时间或进度灵活性。正常情况下，关键路径的总浮动时间为
零。在进行紧前关系绘图法排序的过程中，取决于所用的制约因素，关键路径的总浮动时间可能是
正值、零或负值。总浮动时间为正值，是由于逆推计算所使用的进度制约因素要晚于顺推计算所得
出的最早完成日期；总浮动时间为负值，是由于持续时间和逻辑关系违反了对最晚日期的制约因
素。负值浮动时间分析是一种有助于找到推动延迟的进度回到正轨的方法的技术。进度网络图可能
有多条次关键路径。许多软件允许用户自行定义用于确定关键路径的参数。为了使网络路径的总浮
动时间为零或正值，可能需要调整活动持续时间（可增加资源或缩减范围时）、逻辑关系（针对选
择性依赖关系时）、提前量和滞后量，或其他进度制约因素。一旦计算出总浮动时间和自由浮动时
间，自由浮动时间就是指在不延误任何紧后活动最早开始日期或不违反进度制约因素的前提下，某
进度活动可以推迟的时间量。",6.5.2.2,项目进度管理,制定进度计划
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
★成本效益分析, 成本效益分析是用来估算备选方案优势和劣势的财务分析工具，以确定可以创造最佳效益的备选方案。成本效益分析可帮助项目经理确定规划的质量活动是否有效利用了成本。达到质量要求的主要效益包括减少返工、提高生产率、降低成本、提升相关方满意度及提升赢利能力。对每个质量活动进行成本效益分析，就是要比较其可能成本与预期效益。,8.1.2.3,项目质量管理,规划质量管理-数据分析
★质量成本,"与项目有关的质量成本 (COQ) 包含以下一种或多种成本（图 8-5 提供了各组成本的例子）：
◆预防成本。 预防特定项目的产品、可交付成果或服务质量低劣所带来的相关成本。
◆评估成本。 评估、测量、审计和测试特定项目的产品、可交付成果或服务所带来的相关成本。
◆失败成本（内部/外部）。 因产品、可交付成果或服务与相关方需求或期望不一致而导致的
相关成本。",8.1.2.3,项目质量管理,规划质量管理-数据分析
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
流程图," 流程图，也称过程图，用来显示在一个或多个输入转化成一个或多个输出的过程中，
所需要的步骤顺序和可能分支。它通过映射水平价值链的过程细节来显示活动、决策点、分支循环、并行路径及整体处理顺序。图 8-6 展示了其中一个版本的价值链，即 SIPOC（供应商、输入、过程、输出和客户）模型。流程图可能有助于了解和估算一个过程的质量成本。通过工作流的逻辑分支及其相对频率来估算质量成本。这些逻辑分支细分为完成符合要求的输出而需要开展的一致性工作和非一致性工作。用于展示过程步骤时，流程图有时又被称为“过程流程图”或“过程流向图”，可帮助改进过程并识别可能出现质量缺陷或可以纳入质量检查的地方。",*******,项目质量管理,规划质量管理-数据表现
逻辑数据模型,逻辑数据模型把组织数据可视化，以商业语言加以描述，不依赖任何特定技术。逻辑数据模型可用于识别会出现数据完整性或其他质量问题的地方,*******,项目质量管理,规划质量管理-数据表现
矩阵图,矩阵图在行列交叉的位置展示因素、原因和目标之间的关系强弱。根据可用来比较因素的数量，项目经理可使用不同形状的矩阵图，如 L 型、 T 型、Y 型、 X 型、 C 型和屋顶型矩阵。在本过程中，它们有助于识别对项目成功至关重要的质量测量指标。,*******,项目质量管理,规划质量管理-数据表现
思维导图,"见 5.2.2.5 节（把从头脑风暴中获得的创意整合成一张图，用以反映创意之间的共性与差异，激
发新创意。）思维导图是一种用于可视化组织信息的绘图法。质量思维导图通常是基于单个质量概念创建的，是绘制在空白的页面中央的图像，之后再增加以图像、词汇或词条形式表现的想法。思维导图技术可以有助于快速收集项目质量要求、制约因素、依赖关系和联系。",*******,项目质量管理,规划质量管理-数据表现
★根本原因分析(RCA)," 根本原因分析是确定引起偏差、缺陷或风险的根本原因的一种分析技术。
一项根本原因可能引起多项偏差、缺陷或风险。根本原因分析还可以作为一项技术，用于识别问题的根本原因并解决问题。消除所有根本原因可以杜绝问题再次发生。",8.2.2.2,项目质量管理,管理质量-数据分析
亲和图。 ,见 5.2.2.5 节（用来对大量创意进行分组的技术，以便进一步审查和分析。）。亲和图可以对潜在缺陷成因进行分类，展示最应关注的领域。,8.2.2.4,项目质量管理,管理质量-数据分析
"
★因果图。 ","因果图，又称“鱼骨图”、“why-why分析图”和“石川图”，将问题陈述的原因分
解为离散的分支，有助于识别问题的主要原因或根本原因。图 8-9 是因果图的一个例子。",8.2.2.4,项目质量管理,管理质量-数据分析
直方图。," 直方图是一种展示数字数据的条形图，可以展示每个可交付成果的缺陷数量、缺陷成
因的排列、各个过程的不合规次数，或项目或产品缺陷的其他表现形式。",8.2.2.4,项目质量管理,管理质量-数据分析
★散点图。," 散点图是一种展示两个变量之间的关系的图形，它能够展示两支轴的关系，一支轴表
示过程、环境或活动的任何要素，另一支轴表示质量缺陷。",8.2.2.4,项目质量管理,管理质量-数据分析
★审计,"审计是用于确定项目活动是否遵循了组织和项目的政策、过程与程序的一种结构化且独立的过程。质量审计通常由项目外部的团队开展，如组织内部审计部门、项目管理办公室 (PMO) 或组织外部的审计师。质量审计目标可能包括（但不限于）：
◆ 识别全部正在实施的良好及最佳实践；
◆ 识别所有违规做法、差距及不足；
◆ 分享所在组织和/或行业中类似项目的良好实践；
◆ 积极、主动地提供协助，以改进过程的执行，从而帮助团队提高生产效率；
◆ 强调每次审计都应对组织经验教训知识库的积累做出贡献。
采取后续措施纠正问题，可以降低质量成本，并提高发起人或客户对项目产品的接受度。质量审计可事先安排，也可随机进行；可由内部或外部审计师进行。
质量审计还可确认已批准的变更请求（包括更新、纠正措施、缺陷补救和预防措施）的实施情况。",8.2.2.5 ,项目质量管理,管理质量-数据分析
面向 X 的设计,"面向 X 的设计 (DfX) 是产品设计期间可采用的一系列技术指南，旨在优化设计的特定方面，可以控
制或提高产品最终特性。 DfX 中的“X”可以是产品开发的不同方面，例如可靠性、调配、装配、制造、
成本、服务、可用性、安全性和质量。使用 DfX 可以降低成本、改进质量、提高绩效和客户满意度",8.2.2.6 ,项目质量管理,管理质量
问题解决,"问题解决发现解决问题或应对挑战的解决方案。它包括收集其他信息、具有批判性思维的、创造
性的、量化的和/或逻辑性的解决方法。有效和系统化地解决问题是质量保证和质量改进的基本要
素。问题可能在控制质量过程或质量审计中发现，也可能与过程或可交付成果有关。使用结构化的
问题解决方法有助于消除问题和制定长久有效的解决方案。问题解决方法通常包括以下要素：
◆ 定义问题；
◆ 识别根本原因；
◆ 生成可能的解决方案；
◆ 选择最佳解决方案；
◆ 执行解决方案；
◆ 验证解决方案的有效性。",8.2.2.7 ,项目质量管理,管理质量
质量改进方法,"质量改进的开展，可基于质量控制过程的发现和建议、质量审计的发现，或管理质量过程的问题
解决。计划 — 实施 — 检查 — 行动和六西格玛是最常用于分析和评估改进机会的两种质量改进工具。",8.2.2.8 ,项目质量管理,管理质量
核查表,"核查表，又称计数表，用于合理排列各种事项，以便有效地收集关于潜在质量问题的有用数据。在开展检查以识别缺陷时，用核查表收集属性数据就特别方便，例如关于缺陷数量
或后果的数据。见图 8-12。",8.3.2.1 ,项目质量管理,控制质量-数据收集
统计抽样," 统计抽样是指从目标总体中选取部分样本用于检查（如从 75 张工程图纸中随机抽取
10 张）。样本用于测量控制和确认质量。抽样的频率和规模应在规划质量管理过程中确定。",8.3.2.1 ,项目质量管理,控制质量-数据收集
检查,"检查是指检验工作产品，以确定是否符合书面标准。检查的结果通常包括相关的测量数据，可
在任何层面上进行。可以检查单个活动的成果，也可以检查项目的最终产品。检查也可称为审查、
同行审查、审计或巡检等，而在某些应用领域，这些术语的含义比较狭窄和具体。检查也可用于确
认缺陷补救。",8.3.2.3 ,项目质量管理,控制质量
测试/产品评估,"测试是一种有组织的、结构化的调查，旨在根据项目需求提供有关被测产品或服务质量的客观
信息。测试的目的是找出产品或服务中存在的错误、缺陷、漏洞或其他不合规问题。用于评估各项
需求的测试的类型、数量和程度是项目质量计划的一部分，具体取决于项目的性质、时间、预算或
其他制约因素。测试可以贯穿于整个项目，可以随着项目的不同组成部分变得可用时进行，也可以
在项目结束（即交付最终可交付成果）时进行。早期测试有助于识别不合规问题，帮助减少修补不
合规组件的成本。不同应用领域需要不同测试。例如，软件测试可能包括单元测试、集成测试、黑盒测试、白盒测试、接口测试、回归测试、α 测试等；在建筑项目中，测试可能包括水泥强度测试、混凝土和易性测试、在建筑工地进行的旨在测试硬化混凝土结构的质量的无损伤测试，以及土壤试验；在硬件开发中，测试可能包括环境应力筛选、老化测试、系统测试等。",8.3.2.4 ,项目质量管理,控制质量
控制图,控制图用于确定一个过程是否稳定，或者是否具有可预测的绩效。规格上限和下限是根据要求制定的，反映了可允许的最大值和最小值。上下控制界限不同于规格界限。控制界限根据标准的统计原则，通过标准的统计计算确定，代表一个稳定过程的自然波动范围。项目经理和相关方可基于计算出的控制界限，识别须采取纠正措施的检查点，以预防不在控制界限内的绩效。控制图可用于监测各种类型的输出变量。虽然控制图最常用来跟踪批量生产中的重复性活动，但也可用来监测成本与进度偏差、产量、范围变更频率或其他管理工作成果，以便帮助确定项目管理过程是否受控。,8.3.2.5 ,项目质量管理,控制质量-数据表现
★资源优化,"资源优化用于调整活动的开始和完成日期，以调整计划使用的资源，使其等于或少于可用的资
源。资源优化技术是根据资源供需情况，来调整进度模型的技术，包括（但不限于）：
◆ 资源平衡。 为了在资源需求与资源供给之间取得平衡，根据资源制约因素对开始日期和完成日
期进行调整的一种技术。如果共享资源或关键资源只在特定时间可用，数量有限，或被过度分
配，如一个资源在同一时段内被分配至两个或多个活动（见图 6-17），就需要进行资源平衡。
也可以为保持资源使用量处于均衡水平而进行资源平衡。资源平衡往往导致关键路径改变。
而可以用浮动时间平衡资源。因此，在项目进度计划期间，关键路径可能发生变化。
◆ 资源平滑。 对进度模型中的活动进行调整，从而使项目资源需求不超过预定的资源限制的一种
技术。相对于资源平衡而言，资源平滑不会改变项目关键路径，完工日期也不会延迟。也就是
说，活动只在其自由和总浮动时间内延迟，但资源平滑技术可能无法实现所有资源的优化。",6.5.2.3,项目进度管理,制定进度计划
★假设情景分析," 假设情景分析是对各种情景进行评估，预测它们对项目目标的影响（积极或消
极的）。假设情景分析就是对“如果情景 X 出现，情况会怎样？”这样的问题进行分析，即基
于已有的进度计划，考虑各种各样的情景。例如，推迟某主要部件的交货日期，延长某设计工
作的时间，或加入外部因素（如罢工或许可证申请流程变化等）。可以根据假设情景分析的结
果，评估项目进度计划在不同条件下的可行性，以及为应对意外情况的影响而编制进度储备和
应对计划。",6.5.2.3,项目进度管理,制定进度计划-数据分析
模拟,"模拟是把单个项目风险和不确定性的其他来源模型化的方法，以评估它们对项目目标的
潜在影响。最常见的模拟技术是蒙特卡罗分析（见 11.4.2.5 节），它利用风险和其他不确定资
源计算整个项目可能的进度结果。模拟包括基于多种不同的活动假设、制约因素、风险、问题
或情景，使用概率分布和不确定性的其他表现形式（见 11.4.2.4 节），来计算出多种可能的工
作包持续时间。图 6-18 显示了一个项目的概率分布，表明实现特定目标日期（即项目完成日
期）的可能性。在这个例子中，项目按时或在目标日期，即 5 月 13 日之前完成的概率是 10%，
而在 5 月 28 日之前完成的概率是 90%。",6.5.2.3,项目进度管理,制定进度计划-数据分析
★进度压缩,"进度压缩技术是指在不缩减项目范围的前提下，缩短或加快进度工期，以满足进度制约因素、
强制日期或其他进度目标。负值浮动时间分析是一种有用的技术。关键路径是浮动时间最少的
方法。在违反制约因素或强制日期时，总浮动时间可能变成负值。图 6-19 比较了多个进度压缩
技术，包括：
◆ 赶工。 通过增加资源，以最小的成本代价来压缩进度工期的一种技术。赶工的例子包括：批准
加班、增加额外资源或支付加急费用，来加快关键路径上的活动。赶工只适用于那些通过增加
资源就能缩短持续时间的，且位于关键路径上的活动。但赶工并非总是切实可行的，因它可能
导致风险和/或成本的增加。
◆ 快速跟进。一种进度压缩技术，将正常情况下按顺序进行的活动或阶段改为至少是部分并行
开展。例如，在大楼的建筑图纸尚未全部完成前就开始建地基。快速跟进可能造成返工和风
险增加，所以它只适用于能够通过并行活动来缩短关键路径上的项目工期的情况。以防进度
加快而使用提前量通常增加相关活动之间的协调工作，并增加质量风险。快速跟进还有可能
增加项目成本。",6.5.2.6 ,项目进度管理,制定进度计划
敏捷发布规划,"
敏捷发布规划基于项目路线图和产品发展愿景，提供了高度概括的发布进度时间轴（通常是 3 到 6
个月）。同时，敏捷发布规划还确定了发布的迭代或冲刺次数，使产品负责人和团队能够决定需要
开发的内容，并基于业务目标、依赖关系和障碍因素确定达到产品放行所需的时间。
对客户而言，产品功能就是价值，因此，该时间轴定义了每次迭代结束时交付的功能，提供了更
易于理解的项目进度计划，而这些就是客户真正需要的信息。
图 6-20 展示了产品愿景、产品路线图、发布规划和迭代计划之间的关系。",6.5.2.8 ,项目进度管理,制定进度计划
迭代燃尽图。,"这类图用于追踪迭代未完项中尚待完成的工作。它基于迭代规划（见 6.4.2.8 节）
中确定的工作，分析与理想燃尽图的偏差。可使用预测趋势线来预测迭代结束时可能出现的偏
差，以及在迭代期间应该采取的合理行动。在燃尽图中，先用对角线表示理想的燃尽情况，再
每天画出实际剩余工作，最后基于剩余工作计算出趋势线以预测完成情况。图 6-24 是迭代燃尽
图的一个例子。",6.6.2.1,项目进度管理,控制进度
挣值分析 (EVA),,7.4.2.2 ,项目成本管理,控制成本-数据分析
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
,,,,
★责任分配矩阵,"责任分配矩阵展示项目资源在各个工作包中的任务分配。矩阵型图表的一个
例子是职责分配矩阵(RAM)，它显示了分配给每个工作包的项目资源，用于说明工作包或活
动与项目团队成员之间的关系。在大型项目中，可以制定多个层次的 RAM。例如，高层次的
RAM 可定义项目团队、小组或部门负责 WBS 中的哪部分工作，而低层次的 RAM 则可在各小组
内为具体活动分配角色、职责和职权。矩阵图能反映与每个人相关的所有活动，以及与每项
活动相关的所有人员，它也可确保任何一项任务都只有一个人负责，从而避免职权不清。 RAM
的一个例子是 RACI（执行、负责、咨询和知情）矩阵，如图 9-4 所示。图中最左边的一列表示
有待完成的工作（活动）。分配给每项工作的资源可以是个人或小组，项目经理也可根据项
目需要，选择“领导”或“资源”等适用词汇，来分配项目责任。如果团队是由内部和外部
人员组成， RACI 矩阵对明确划分角色和职责特别有用。",9.1.2.2 数据表现,项目资源管理,规划资源管理
"预分派
","预分派指事先确定项目的实物或团队资源，可在下列情况下发生：在竞标过程中承诺分派特定人
员进行项目工作；项目取决于特定人员的专有技能；在完成资源管理计划的前期工作之前，制定项
目章程过程或其他过程已经指定了某些团队成员的工作分派。",9.3.2.3,项目资源管理,获取资源
★虚拟团队,"虚拟团队的使用为招募项目团队成员提供了新的可能性。虚拟团队可定义为具有共同目标、
在完成角色任务的过程中很少或没有时间面对面工作的一群人。现代沟通技术（如电子邮件、电话
会议、社交媒体、网络会议和视频会议等）使虚拟团队成为可行。虚拟团队模式使人们有可能：
◆在组织内部地处不同地理位置的员工之间组建团队；
◆为项目团队增加特殊技能，即使相应的专家不在同一地理区域；
◆将在家办公的员工纳入团队；
◆在工作班次、工作小时或工作日不同的员工之间组建团队；
◆将行动不便者或残疾人纳入团队；
◆执行那些原本会因差旅费用过高而被搁置或取消的项目；
◆节省员工所需的办公室和所有实物设备的开支。
在虚拟团队的环境中，沟通规划变得日益重要。可能需要花更多时间，来设定明确的期望、促进
沟通、制定冲突解决方法、召集人员参与决策、理解文化差异，以及共享成功喜悦。",9.3.2.4 ,项目资源管理,获取资源
 集中办公,"集中办公是指把许多或全部最活跃的项目团队成员安排在同一个物理地点工作，以增强团队工作
能力。集中办公既可以是临时的（如仅在项目特别重要的时期），也可以贯穿整个项目。实施集中
办公策略，可借助团队会议室、张贴进度计划的场所，以及其他能增进沟通和集体感的设施。",9.4.2.1,项目资源管理,建设团队
团队建设,"团队建设是通过举办各种活动，强化团队的社交关系，打造积极合作的工作环境。
团队建设活动既可以是状态审查会上的五分钟议程，也可以是为改善人际关系而设计的、在非
工作场所专门举办的专业提升活动。团队建设活动旨在帮助各团队成员更加有效地协同工作。
如果团队成员的工作地点相隔甚远，无法进行面对面接触，就特别需要有效的团队建设策略。
非正式的沟通和活动有助于建立信任和良好的工作关系。团队建设在项目前期必不可少，但它
更是个持续的过程。项目环境的变化不可避免，要有效应对这些变化，就需要持续不断地开展
团队建设。项目经理应该持续地监督团队机能和绩效，确定是否需要采取措施来预防或纠正各
种团队问题。",9.4.2.1,项目资源管理,建设团队
培训,"培训包括旨在提高项目团队成员能力的全部活动，可以是正式或非正式的，方式包括课堂培训、
在线培训、计算机辅助培训、在岗培训（由其他项目团队成员提供）、辅导及训练。如果项目团队
成员缺乏必要的管理或技术技能，可以把对这种技能的培养作为项目工作的一部分。项目经理应该
按资源管理计划中的安排来实施预定的培训，也应该根据管理项目团队过程中的观察、交谈和项目
绩效评估的结果，来开展必要的计划外培训，培训成本通常应该包括在项目预算中，或者如果增加
的技能有利于未来的项目，则由执行组织承担。培训可以由内部或外部培训师来执行。",9.4.2.6 ,项目资源管理,建设团队
 个人和团队评估,"
个人和团队评估工具能让项目经理和项目团队洞察成员的优势和劣势。这些工具可帮助项目经
理评估团队成员的偏好和愿望、团队成员如何处理和整理信息、如何制定决策，以及团队成员如何
与他人打交道。有各种可用的工具，如态度调查、专项评估、结构化访谈、能力测试及焦点小组。
这些工具有利于增进团队成员间的理解、信任、承诺和沟通，在整个项目期间不断提高团队成效。",9.4.2.7,项目资源管理,建设团队
团队绩效评价,"随着项目团队建设工作（如培训、团队建设和集中办公等）的开展，项目管理团队应该对项目团
队的有效性进行正式或非正式的评价。有效的团队建设策略和活动可以提高团队绩效，从而提高实
现项目目标的可能性。
评价团队有效性的指标可包括：
◆个人技能的改进，从而使成员更有效地完成工作任务；
◆团队能力的改进，从而使团队成员更好地开展工作；
◆团队成员离职率的降低；
◆团队凝聚力的加强，从而使团队成员公开分享信息和经验，并互相帮助来提高项目绩效。
通过对团队整体绩效的评价，项目管理团队能够识别出所需的特殊培训、教练、辅导、协助或改
变，以提高团队绩效。项目管理团队也应该识别出合适或所需的资源，以执行和实现在绩效评价过
程中提出的改进建议",9.4.3.1 ,项目资源管理,建设团队
★冲突管理,"项目环境中，冲突不可避免。冲突的来源包括资源稀缺、进度优先级排序和个人
工作风格差异等。采用团队基本规则、团队规范及成熟的项目管理实践（如沟通规划和角色定
义），可以减少冲突的数量。
成功的冲突管理可提高生产力，改进工作关系。同时，如果管理得当，意见分歧有利于提高创
造力和改进决策。假如意见分歧成为负面因素，应该首先由项目团队成员负责解决；如果冲突
升级，项目经理应提供协助，促成满意的解决方案，采用直接和合作的方式，尽早并且通常在
私下处理冲突。如果破坏性冲突继续存在，则可使用正式程序，包括采取惩戒措施。
项目经理解决冲突的能力往往决定其管理项目团队的成败。不同的项目经理可能采用不同的解
决冲突方法。影响冲突解决方法的因素包括：
◆冲突的重要性与激烈程度；
◆解决冲突的紧迫性；
◆涉及冲突的人员的相对权力；
◆维持良好关系的重要性；
◆永久或暂时解决冲突的动机。
有五种常用的冲突解决方法，每种技巧都有各自的作用和用途。
撤退/回避。 从实际或潜在冲突中退出，将问题推迟到准备充分的时候，或者将问题推给
其他人员解决。
缓和/包容。 强调一致而非差异；为维持和谐与关系而退让一步，考虑其他方的需要。
妥协/调解。 为了暂时或部分解决冲突，寻找能让各方都在一定程度上满意的方案，但这
种方法有时会导致“双输”局面。
强迫/命令。 以牺牲其他方为代价，推行某一方的观点；只提供赢 — 输方案。通常是利用
权力来强行解决紧急问题，这种方法通常会导致“赢输”局面。
合作/解决问题。 综合考虑不同的观点和意见，采用合作的态度和开放式对话引导各方达
成共识和承诺，这种方法可以带来双赢局面。",9.5.2.1,项目资源管理,管理团队
沟通方法,"项目相关方之间用于分享信息的沟通方法有几种。这些方法可以大致分为：
互动沟通。 在两方或多方之间进行的实时多向信息交换。它使用诸如会议、电话、即时信息、
社交媒体和视频会议等沟通工件。
推式沟通。 向需要接收信息的特定接收方发送或发布信息。这种方法可以确保信息的发送，但
不能确保信息送达目标受众或被目标受众理解。在推式沟通中，可以采用的沟通工件包括信
件、备忘录、报告、电子邮件、传真、语音邮件、博客、新闻稿。
拉式沟通。 适用于大量复杂信息或大量信息受众的情况。它要求接收方在遵守有关安全规定的
前提之下自行访问相关内容。这种方法包括门户网站、企业内网、电子在线课程、经验教训数
据库或知识库",10.1.2.5 ,沟通管理,规划沟通管理
核对单,"核对单是包括需要考虑的项目、行动或要点的清单。它常被用作提醒。基于从类似项
目和其他信息来源积累的历史信息和知识来编制核对单。编制核对单，列出过去曾出现且可能
与当前项目相关的具体单个项目风险，这是吸取已完成的类似项目的经验教训的有效方式。组
织可能基于自己已完成的项目来编制核对单，或者可能采用特定行业的通用风险核对单。虽然
核对单简单易用，但它不可能穷尽所有风险。所以，必须确保不要用核对单来取代所需的风险
识别工作；同时，项目团队也应该注意考察未在核对单中列出的事项。此外，还应该不时地审
查核对单，增加新信息，删除或存档过时信息",11.2.2.2,风险管理,识别风险-数据收集
SWOT 分析,"这是对项目的优势、劣势、机会和威胁 (SWOT) 进行逐个检查。在识别风险时，
它会将内部产生的风险包含在内，从而拓宽识别风险的范围。首先，关注项目、组织或一
般业务领域，识别出组织的优势和劣势；然后，找出组织优势可能为项目带来的机会，组织
劣势可能造成的威胁。还可以分析组织优势能在多大程度上克服威胁，组织劣势是否会妨碍
机会的产生。",11.2.2.3,风险管理,识别风险-数据分析
 提示清单,"提示清单是关于可能引发单个项目风险以及可作为整体项目风险来源的风险类别的预设清单。在
采用风险识别技术时，提示清单可作为框架用于协助项目团队形成想法。可以用风险分解结构底层
的风险类别作为提示清单，来识别单个项目风险。某些常见的战略框架更适用于识别整体项目风险
的来源，如 PESTLE（政治、经济、社会、技术、法律、环境）、 TECOP（技术、环境、商业、运营、
政治），或 VUCA（易变性、不确定性、复杂性、模糊性）",11.2.2.5,风险管理,识别风险
风险数据质量评估,"风险数据是开展定性风险分析的基础。风险数据质量评估旨在评价关于
单个项目风险的数据的准确性和可靠性。使用低质量的风险数据，可能导致定性风险分析对
项目来说基本没用。如果数据质量不可接受，就可能需要收集更好的数据。可以开展问卷调
查，了解项目相关方对数据质量各方面的评价，包括数据的完整性、客观性、相关性和及时
性，进而对风险数据的质量进行综合评估。可以计算这些方面的加权平均数，将其作为数据
质量的总体分数。",11.3.2.3,风险管理,实施定性风险分析-数据分析
风险概率和影响评估," 风险概率评估考虑的是特定风险发生的可能性，而风险影响评估考虑的
是风险对一项或多项项目目标的潜在影响，如进度、成本、质量或绩效。威胁将产生负面的影
响，机会将产生正面的影响。要对每个已识别的单个项目风险进行概率和影响评估。风险评估
可以采用访谈或会议的形式，参加者将依照他们对风险登记册中所记录的风险类型的熟悉程度
而定。项目团队成员和项目外部资深人员应该参加访谈或会议。在访谈或会议期间，评估每个
风险的概率水平及其对每项目标的影响级别。如果相关方对概率水平和影响级别的感知存在差
异，则应对差异进行探讨。此外，还应记录相应的说明性细节，例如，确定概率水平或影响级
别所依据的假设条件。应该采用风险管理计划中的概率和影响定义（表11-1），来评估风险的
概率和影响。低概率和影响的风险将被列入风险登记册中的观察清单，以供未来监控。",11.3.2.3,风险管理,实施定性风险分析-数据分析
概率和影响矩阵," 概率和影响矩阵是把每个风险发生的概率和一旦发生对项目目标的影响映
射起来的表格。此矩阵对概率和影响进行组合，以便于把单个项目风险划分成不同的优先级
组别（见图 11-5）。基于风险的概率和影响，对风险进行优先级排序，以便未来进一步分析
并制定应对措施。采用风险管理计划中规定的风险概率和影响定义，逐一对单个项目风险的
发生概率及其对一项或多项项目目标的影响（若发生）进行评估。然后，基于所得到的概率
和影响的组合，使用概率和影响矩阵，来为单个项目风险分配优先级别。
组织可针对每个项目目标（如成本、时间和范围）制定单独的概率和影响矩阵，并用它们来评
估风险针对每个目标的优先级别。组织还可以用不同的方法为每个风险确定一个总体优先级
别。即可综合针对不同目标的评估结果，也可采用最高优先级别（无论针对哪个目标），作为
风险的总体优先级别。",11.3.2.6,风险管理,实施定性风险分析-数据表现
模拟（蒙特卡洛分析）,"◆ 模拟。 在定量风险分析中，使用模型来模拟单个项目风险和其他不确定性来源的综合影响，以
评估它们对项目目标的潜在影响。模拟通常采用蒙特卡洛分析。对成本风险进行蒙特卡洛分析
时，使用项目成本估算作为模拟的输入；对进度风险进行蒙特卡洛分析时，使用进度网络图和
持续时间估算作为模拟的输入。开展综合定量成本-进度风险分析时，同时使用这两种输入。
其输出就是定量风险分析模型。
用计算机软件数千次迭代运行定量风险分析模型。每次运行，都要随机选择输入值（如成本估
算、持续时间估算或概率分支发生频率）。这些运行的输出构成了项目可能结果（如项目结束
日期、项目完工成本）的区间。典型的输出包括：表示模拟得到特定结果的次数的直方图，或
表示获得等于或小于特定数值的结果的累积概率分布曲线（S 曲线）。蒙特卡洛成本风险分析
所得到的 S 曲线示例，
在定量进度风险分析中，还可以执行关键性分析，以确定风险模型的哪些活动对项目关键路径
的影响最大。对风险模型中的每一项活动计算关键性指标，即：在全部模拟中，该活动出现在
关键路径上的频率，通常以百分比表示。通过关键性分析，项目团队就能够重点针对那些对项
目整体进度绩效存在最大潜在影响的活动，来规划风险应对措施。",11.4.2.5,风险管理,实施定量风险分析- 数据分析
敏感性分析,"u 敏感性分析。 敏感性分析有助于确定哪些单个项目风险或其他不确定性来源对项目结果具有最
大的潜在影响。它在项目结果变异与定量风险分析模型中的要素变异之间建立联系。
敏感性分析的结果通常用龙卷风图来表示。在该图中，标出定量风险分析模型中的每项要素
与其能影响的项目结果之间的关联系数。这些要素可包括单个项目风险、易变的项目活动，
或具体的不明确性来源。每个要素按关联强度降序排列，形成典型的龙卷风形状。",11.4.2.5,风险管理,实施定量风险分析- 数据分析
决策树分析,"u 决策树分析。 用决策树在若干备选行动方案中选择一个最佳方案。在决策树中，用不同的分
支代表不同的决策或事件，即项目的备选路径。每个决策或事件都有相关的成本和单个项目
风险（包括威胁和机会）。决策树分支的终点表示沿特定路径发展的最后结果，可以是负面
或正面的结果。",11.4.2.5,风险管理,实施定量风险分析- 数据分析
★威胁应对策略,"◆ 上报。 如果项目团队或项目发起人认为某威胁不在项目范围内，或提议的应对措施超出了项目
经理的权限，就应该采用上报策略。被上报的风险将在项目集层面、项目组合层面或组织的其
他相关部门加以管理，而不在项目层面。项目经理确定应就威胁通知哪些人员，并向该人员或
组织部门传达关于该威胁的详细信息。对于被上报的威胁，组织中的相关人员必须愿意承担应
对责任，这一点非常重要。威胁通常要上报给其目标会受该威胁影响的那个层级。威胁一旦上
报，就不再由项目团队做进一步监督，虽然仍可出现在风险登记册中供参考。
u 规避。 风险规避是指项目团队采取行动来消除威胁，或保护项目免受威胁的影响。它可能适用
于发生概率较高，且具有严重负面影响的高优先级威胁。规避策略可能涉及变更项目管理计划
的某些方面，或改变会受负面影响的目标，以便于彻底消除威胁，将它的发生概率降低到零。
风险责任人也可以采取措施，来分离项目目标与风险万一发生的影响。规避措施可能包括消除
威胁的原因、延长进度计划、改变项目策略，或缩小范围。有些风险可以通过澄清需求、获取
信息、改善沟通或取得专有技能来加以规避。
◆ 转移。 转移涉及到将应对威胁的责任转移给第三方，让第三方管理风险并承担威胁发生的影
响。采用转移策略，通常需要向承担威胁的一方支付风险转移费用。风险转移可能需要通过一
系列行动才得以实现，包括（但不限于）购买保险、使用履约保函、使用担保书、使用保证书
等。也可以通过签订协议，把具体风险的归属和责任转移给第三方。
◆ 减轻。 风险减轻是指采取措施来降低威胁发生的概率和（或）影响。提前采取减轻措施通常比
威胁出现后尝试进行弥补更加有效。减轻措施包括采用较简单的流程，进行更多次测试，或者
选用更可靠的卖方。还可能涉及原型开发（见 5.2.2.8 节），以降低从实验台模型放大到实际工
艺或产品中的风险。如果无法降低概率，也许可以从决定风险严重性的因素入手，来减轻风险
发生的影响。例如，在一个系统中加入冗余部件，可以减轻原始部件故障所造成的影响。
◆ 接受。 风险接受是指承认威胁的存在，但不主动采取措施。此策略可用于低优先级威胁，也可
用于无法以任何其他方式加以经济有效地应对的威胁。接受策略又分为主动或被动方式。最常
见的主动接受策略是建立应急储备，包括预留时间、资金或资源以应对出现的威胁；被动接受
策略则不会主动采取行动，而只是定期对威胁进行审查，确保其并未发生重大改变。",11.5.2.4 ,风险管理,规划风险应对
★机会应对策略,"u 上报。 如果项目团队或项目发起人认为某机会不在项目范围内，或提议的应对措施超出了项
目经理的权限，就应该取用上报策略。被上报的机会将在项目集层面、项目组合层面或组织
的其他相关部门加以管理，而不在项目层面。项目经理确定应就机会通知哪些人员，并向该
人员或组织部门传达关于该机会的详细信息。对于被上报的机会，组织中的相关人员必须愿
意承担应对责任，这一点非常重要。机会通常要上报给其目标会受该机会影响的那个层级。
机会一旦上报，就不再由项目团队做进一步监督，虽然仍可出现在风险登记册中供参考。
◆ 开拓。 如果组织想确保把握住高优先级的机会，就可以选择开拓策略。此策略将特定机会的出
现概率提高到 100%，确保其肯定出现，从而获得与其相关的收益。开拓措施可能包括：把组
织中最有能力的资源分配给项目来缩短完工时间，或采用全新技术或技术升级来节约项目成本
并缩短项目持续时间。
◆ 分享。 分享涉及到将应对机会的责任转移给第三方，使其享有机会所带来的部分收益。必须仔
细为已分享的机会安排新的风险责任人，让那些最有能力为项目抓住机会的人担任新的风险责
任人。采用风险分享策略，通常需要向承担机会应对责任的一方支付风险费用。分享措施包括
建立合伙关系、合作团队、特殊公司或合资企业来分享机会。
◆ 提高。 提高策略用于提高机会出现的概率和（或）影响。提前采取提高措施通常比机会出现后
尝试改善收益更加有效。通过关注其原因，可以提高机会出现的概率；如果无法提高概率，
也许可以针对决定其潜在收益规模的因素来提高机会发生的影响。机会提高措施包括为早日完
成活动而增加资源。
◆ 接受。 接受机会是指承认机会的存在，但不主动采取措施。此策略可用于低优先级机会，也
可用于无法以任何其他方式加以经济有效地应对的机会。接受策略又分为主动或被动方式。
最常见的主动接受策略是建立应急储备，包括预留时间、资金或资源，以便在机会出现时加
以利用；被动接受策略则不会主动采取行动，而只是定期对机会进行审查，确保其并未发生
重大改变。",11.5.2.5,风险管理,规划风险应对
应急应对策略,"
可以设计一些仅在特定事件发生时才采用的应对措施。对于某些风险，如果项目团队相信其发
生会有充分的预警信号，那么就应该制定仅在某些预定条件出现时才执行的应对计划。应该定义
并跟踪应急应对策略的触发条件，例如，未实现中间的里程碑，或获得卖方更高程度的重视。采
用此技术制定的风险应对计划，通常称为应急计划或弹回计划，其中包括已识别的、用于启动计
划的触发事件。",11.5.2.6 ,风险管理,规划风险应对
索赔管理,"如果买卖双方不能就变更补偿达成一致意见，或对变更是否发生存在分歧，那么被请求的变更就
成为有争议的变更或潜在的推定变更。此类有争议的变更称为索赔。如果不能妥善解决，它们会成
为争议并最终引发申诉。在整个合同生命周期中，通常会按照合同条款对索赔进行记录、处理、监
督和管理。如果合同双方无法自行解决索赔问题，则可能不得不按合同中规定的程序，用替代争议
解决方法（ADR）去处理。谈判是解决所有索赔和争议的首选方法。",12.3.2.2 ,采购管理,实施采购
★相关方分析," 相关方分析会产生相关方清单和关于相关方的各种信息，例如，在组织内的位
置、在项目中的角色、与项目的利害关系、期望、态度（对项目的支持程度），以及对项目信
息的兴趣。相关方的利害关系可包括（但不限于）以下各条的组合：
◆兴趣。 个人或群体会受与项目有关的决策或成果的影响。
◆权利（合法权利或道德权利）。 国家的法律框架可能已就相关方的合法权利做出规定，
如职业健康和安全。道德权利可能涉及保护历史遗迹或环境的可持续性。
◆所有权。 人员或群体对资产或财产拥有的法定所有权。
◆知识。 专业知识有助于更有效地达成项目目标和组织成果，或有助于了解组织的权力结
构，从而有益于项目。
◆贡献。 提供资金或其他资源，包括人力资源，或者以无形方式为项目提供支持，例如，
宣传项目目标，或在项目与组织权力结构及政治之间扮演缓冲角色。",13.1.2.3 ,相关方管理,识别相关方-数据分析
,,,,
